.wrapper{
  width: 622rpx;
  height: 650rpx;
  background: #fff;
  border-radius: 32rpx;
  padding: 48rpx 32rpx 48rpx;
  box-sizing: border-box;
  color: #17204D;
  margin: 0 auto;
  margin-top: 50%;
  position: relative;
}
.close{
  width: 30rpx;
  height: 30rpx;
  position: absolute;
  right: 28rpx;
  top: 28rpx;
}
/* .name{
  width: 558rpx;
  height: 64rpx;
  line-height: 64rpx;
  background: #F7F8FA;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #17204D;
  padding-left: 24rpx;
  box-sizing: border-box;
  margin: 24rpx 0 32rpx;
} */
.changeTieClick {
  width: 558rpx;
  height: 64rpx;
  background: #F7F8FA;
  border-radius: 8rpx;
  opacity: 1;
  margin: 24rpx 0 32rpx;
  padding-left: 24rpx;
  box-sizing: border-box;
}

.changeTieClick input {
  /* padding-left: 24rpx; */
  font-size: 28rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 400;
  color: #17204D;
  height: 64rpx;
  line-height: 64rpx;
}
.tip{
  color: #555C80;
}
.title{
  color: #FF5030;
  font-size: 28rpx;
  margin-bottom: 32rpx;
  margin-top: 48rpx;
}
.footer{
  display: flex;
  height: 96rpx;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.onlyChange{
  justify-content:center;
}
.footer view{
  width: 264rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  color: #FF5030;
  border-radius: 48rpx;
  
}
.noNeed{
  border: 1px solid #FF5030;
}
.footer .sure{
  color: #fff;
  background:linear-gradient(to right, #FC3E1B,#FC6B37);
 
}
/* .onlyChange .sure{
  width: 100%;
} */