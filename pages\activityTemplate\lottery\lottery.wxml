<!-- 登录逻辑 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close"></login-box>
<!-- 登录逻辑 -->
<!-- 触发授权操作弹框 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler">点击进入</button>
  </view>
</view>
<!-- 触发授权操作弹框 -->
<view class="activity"  style="height:{{height*2}}rpx;background-image:url({{backgroundImg}})">
  <view class="lottery">
    <view wx:for="{{pageData}}" wx:key="index">
    <!-- 跳转按钮 -->
      <view wx:if="{{item.type === 'button'&&item.btEvent!='other'&&item.btEvent!='draw'}}" style="{{item.styleStr}};background-image: url({{item.bgStatus==1?item.imgOptions.url:null}});background-color:{{item.bgStatus==1?'transparent':item.style.backgroundColor}};border-style: {{item.style.borderStyle}};border-width:{{item.style.borderWidth ? item.style.borderWidth*2 : 0}}rpx;" class="rule" data-btevent="{{item.btEvent}}" bindtap="jumpPage" >
        <view>{{item.propValue}}</view>
      </view>
      <!-- 头部标题图片 -->
      <image class="title" wx:if="{{item.type === 'img'}}" style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx;" src="{{item.propValue.url}}"></image>
      <!-- 转盘 -->
      <block  wx:if="{{!showModalDlg && !showSwitchSale && !showPoster && !showReject && isShowLucky && !isInfo}}">
        <view wx:if="{{item.type === 'flip'&&item.filpSettings.method != 0}}" class="flip-bg" style="{{item.styleStr}};background-image: url({{item.imgOptions.url}})">
          <view class="flip_crad">
            <view class="flip_crad_item" wx:for="{{item.filpSettings.imgListOptions}}" wx:for-index="flipIndex" wx:for-item="filpItem" wx:key="flipIndex">
              <image class="flip-img" wx:if="{{item.filpSettings.method == 1}}" src="{{item.filpSettings.imgOptions.url}}" bindtap="cradClick" data-index="{{flipIndex}}"></image>
              <image class="flip-img" wx:else src="{{filpItem.url}}" bindtap="cradClick" data-index="{{flipIndex}}"></image>
              </view>
          </view>
        </view>
       
        <view wx:if="{{item.type === 'luck' }}" style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx" class="wheel">
          <canvas-view class="luckWheel" wx:if="{{item.type === 'luck'}}"  style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;">
            <lucky-wheel
            id="myLucky"
            width="{{item.style.width*2}}rpx"
            height="{{item.style.height*2}}rpx"
            blocks="{{blocks}}"
            prizes="{{prizes}}"
            buttons="{{buttons}}"
            bindstart="start"
            bindend="end"
          />
          </canvas-view >
          <canvas-view class="luckGrid" wx:else  style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;">
            <lucky-grid
            id="myLucky"
            width="{{item.style.width*2}}rpx"
            height="{{item.style.height*2}}rpx"
            blocks="{{blocks}}"
            prizes="{{prizes}}"
            buttons="{{buttons}}"
            bindstart="start"
            bindend="end"
          />
          </canvas-view >
        </view>
        <view wx:if="{{item.type === 'prize' }}" class="wheel"  style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx">
          <canvas-view class="luckWheel" wx:if="{{item.type === 'prize'}}"  style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;">
            <slot-machine
              id="myLucky"
              width="{{item.style.width*2}}rpx"
              height="{{item.style.height*2}}rpx"
              blocks="{{tigerBlock}}"
              prizes="{{prizes}}"
              slots="{{slots}}"
              defaultConfig="{{defaultConfig}}"
              defaultStyle="{{defaultStyle}}"
              bindstart="start"
              bindend="end"
            />
            <!-- prizes="{{prizes}}" -->
          </canvas-view >
        </view>
        <view wx:if="{{item.type === 'smashGoldenEggs'}}" style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx;" class="wheel">
          <view class="smashingGoldenEggs" style="background-image: url({{eggBgImg}});">
            <view class="smashingGoldenEggs_main">
              <view class="smashingGoldenEggs_main_img" wx:for="{{eggImg}}" wx:key="$this">
                <image class="eggImg" data-index="{{item}}" bindtap="smashingGoldenEggs" src="{{item.url}}"></image>
                <image class="hammer" wx:if="{{showIndex === item.index}}" src="{{hammer}}"></image>
              </view>
            </view>
          </view>
        </view>
        <view wx:if="{{item.type === 'twistEgg' }}" class="wheel"  style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx;background-image: url({{twistingEggsListBG[0].url}})">
          <view class="twistingEggs">
            <image wx:for="{{twistingEggsList}}" wx:key="$this" wx:for-index="i" class="image{{i}} {{twistingEggsStart ? 'weiyi_'+ i :''}}"  src="{{item.url}}" />
          </view>
          <image class="twistingEggsImage" hidden="{{twistingEggsEnd}}" animation="{{ani}}" src="{{twistingEggsChooseImage}}" />
        </view>
      </block>
      <!-- 翻牌商品列表 -->
      <view wx:if="{{item.type === 'flip'&&item.filpSettings.method === 0}}" class="flip-card" style="{{item.styleStr}}">
          <view class="flip_crad">
            <scroll-view  scroll-y="true" style="height:{{item.style.height*2}}rpx"> 
              <view class="{{selectedId===flipIndex?'active':''}} cardItem" wx:for="{{item.filpSettings.imgListOptions}}" wx:for-index="flipIndex" wx:for-item="filpItem" wx:key="flipIndex" bindtap="changeCard" data-key="{{flipIndex}}" >
                <view class="product">
                  <image src="{{filpItem.url}}" mode="widthFix"/>
                </view>
                <view class="select">
                  <image src="../../../image/template/selected.png" wx:if="{{selectedId===flipIndex}}"/>
                  <image src="../../../image/template/unselect.png" wx:else/>
                </view>
              </view>
          </scroll-view>
          </view>
      </view>
      <!-- 翻牌图片回显 -->
      <view wx:if="{{item.type === 'flipDetails'&&resultImg}}" class="flipDetails" style="{{item.styleStr}}">
        <block wx:for="{{flipDetails.filpSettings.imgListOptions}}" wx:key="flipIndex" wx:for-index="flipIndex" wx:for-item="filpItem">
        <block wx:if="{{selectedId===flipIndex}}">
          <view class="richText" style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx">
            <rich-text nodes="{{filpItem.labelText}}"  class="ql-editor"></rich-text> 
          </view>
          <image src="{{filpItem.url}}"  />
        </block>
        </block>
      </view>
      <!-- 翻牌抽奖按钮 -->
      <view wx:if="{{item.type === 'button'&&(item.btEvent==='other'||item.btEvent==='draw')&&resultImg}}" style="{{item.styleStr}};background-image: url({{item.bgStatus==1?item.imgOptions.url:null}});background-color:{{item.bgStatus==1?'transparent':item.style.backgroundColor}};border-style: {{item.style.borderStyle}};border-width: {{item.style.borderWidth ? item.style.borderWidth*2 : 0}}rpx;" class="rule" data-btevent="{{item.btEvent}}" data-set="{{item.setting}}" bindtap="jumpPage" >
        <view>{{item.propValue}}</view>
      </view>
      <block wx:if="{{item.type === 'poster' && flagPoster == 1}}">
        <!-- 专属海报弹框 -->
        <post-popup show="{{showPoster}}" codeUrl="{{qrcode}}" activityName="{{title}}" btnText="{{item}}" banner="{{posterBackgroundImg.propValue.url}}" title="{{posterText.propValue}}" textStyle="{{posterText}}" bind:onClickHide="onClickHide" bind:clickDownPoster="downPoster" bind:showSalesPop="showSalesPop" audit="{{audit}}"></post-popup>
        <!-- 专属海报弹框 -->
      </block>

      <!-- 任务列表 -->
      <view wx:if="{{taskConfigEnable == 1 && item.type === 'myTaskList'}}"  style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx;color:{{item.style.color}};overflow: hidden;background:{{item.style.wrapBackgroundColor}};opacity:{{item.style.opacity}};" class="roster">
        <view class="roster_main" style="color:{{item.style.color}};background-color:{{item.style.backgroundColor}};">
          <view class="roster_title" style="color:{{item.style.color}};">
            <image  class="egg_roster" wx:if="{{activityTemplateType == '7' || activityTemplateType == '12'}}" src="../../../image/activity/egg_desc.png"></image>
            <image wx:else src="../../../image/activity/desc.png"></image>
            <view style="color: {{item.style.titleColor}}">{{item.listTitle}}</view>
            <image wx:if="{{activityTemplateType == '7'||activityTemplateType == '12'}}" class="egg_roster" src="../../../image/activity/egg_desc.png"></image>
            <image wx:else src="../../../image/activity/desc.png"></image>
          </view>
          <view class="task-wrap">
            <!-- <scroll-view scroll-y="true" style="height: 300rpx;" show-scrollbar="{{false}}"> -->
              <view wx:for="{{activityTaskVos}}" wx:for-item="taskItem" wx:key="index" class="task-item">
                <view>
                  <view class="task-name" style="color: {{item.style.taskColor}}">{{taskItem.taskName}}</view>
									<view class="task-desc" style="color: {{item.style.taskDescColor}}">完成任务赠送{{taskItem.extraDrawCount}}次抽奖机会</view>
                </view>
                <button wx:if="{{taskItem.taskState == 0}}" bindtap="taskHandle" open-type="{{taskItem.taskType == 'SHARING' ? 'share' : ''}}" class="task-btn" style="background: {{item.style.btnColor}}" data-item="{{taskItem}}" data-index="{{index}}" >{{item.btnText}}</button>
								<button wx:else bindtap="taskHandle" class="task-btn task-btn-complete" style="background: {{item.style.btnColor}}" data-item="{{taskItem}}" data-index="{{index}}" >已完成</button>
              </view>
            <!-- </scroll-view> -->
          </view>
        </view>
      </view>
      <!-- 任务列表 -->
      
      <view wx:if="{{item.type === 'text'&&!flipDetails && activityTemplateType !== 16}}" style="{{item.styleStr}};opacity:{{item.style.opacity}};" class="frequency">您还有{{numberDay}}次抽奖机会</view>
      <!-- 中奖列表 -->
      <view wx:if="{{item.type === 'list'}}" style="width:{{item.style.width*2}}rpx;height:{{item.style.height*2}}rpx;top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx;color:{{item.style.color}};overflow: hidden;background:{{item.style.wrapBackgroundColor}};opacity:{{item.style.opacity}};" class="roster">
        <view class="roster_main" style="color:{{item.style.color}};background-color:{{item.style.backgroundColor}};">
          <!-- style="background-color:{{item.style.backgroundColor}};"暂时注释老虎机 -->
          <view class="roster_title" style="color:{{item.style.color}};">
            <image  class="egg_roster" wx:if="{{activityTemplateType == '7' || activityTemplateType == '12'}}" src="../../../image/activity/egg_desc.png"></image>
            <!-- <image wx:elif="{{activityTemplateType == '7'}}" class="egg_roster" src="../../../image/activity/twistingEggs_desc.png"></image> -->
            <image wx:else src="../../../image/activity/desc.png"></image>
            <view>中奖名单</view>
            <image wx:if="{{activityTemplateType == '7'||activityTemplateType == '12'}}" class="egg_roster" src="../../../image/activity/egg_desc.png"></image>
            <!-- <image wx:elif="{{activityTemplateType == '12'}}" class="egg_roster" src="../../../image/activity/twistingEggs_desc.png"></image> -->
            <image wx:else src="../../../image/activity/desc.png"></image>
          </view>
          <swiper class="roster_play" wx:if="{{format&&format !== 'barrage'}}" autoplay="{{swiperList.length > 4 ? true:false }}" interval="{{1000}}" duration="{{500}}" vertical="{{true}}" display-multiple-items="{{swiperList.length<5?swiperList.length:4}}" circular="{{true}}" easing-function="{{easeInOutCubic}}">
            <block wx:for="{{swiperList}}" wx:for-item="list" wx:for-index="idx" wx:key="idx">
              <swiper-item class="roster_item" style="height: 70rpx;color:{{item.style.color}};">
                <view class="swiperItem">
                  <view>恭喜{{list.consumerName || '***'}}</view>
                  <view>获得  {{list.prizeName}}</view>
                </view>
              </swiper-item> 
            </block>
          </swiper>
          <view class="roster_play_animation" wx:else style="width:{{item.style.width*2-20}}rpx;height:{{item.style.height*2-100}}rpx;">
            <block wx:for="{{bulletChatData}}" wx:for-item="list" wx:for-index="id">
              <text class="item"  style="animation: first 8s linear forwards;top:{{list.top}}%;color:{{item.style.color}};">{{list.text}}</text>
            </block>
          </view>
        </view>
      </view>
      <!-- xiamin 注释 -->
    </view> 
  </view>
</view>

<dialog-box id='dialog'  
  dialogData="{{dialogData}}" dialogImg="{{dialogImg}}" acceptDialogData="{{acceptDialogData}}" activityTemplateType="{{activityTemplateType}}" bind:cancelEvent="_cancelEvent"  bind:saveEvent="_saveEvent" attributes="{{attributes}}" isSuccess="{{isSuccess}}"
  bind:confirmEvent="_confirmEvent" isInfo="{{false}}" isShow='{{isShow}}' mode="{{drawMode}}" prizeObj='{{prizeObj}}' activityId="{{recordId}}" receiveType="{{receiveType}}" isFirst="{{isFirst}}" getUrlRes="{{getUrlRes}}"></dialog-box>

<!-- 领奖信息弹窗 -->
<van-popup show="{{isInfo}}" closeable="{{false}}" close-on-click-overlay="{{false}}" round overlay-style="z-index: 1" bind:close="onClose" custom-style="background-color: {{acceptAwardImg&&acceptAwardImg.propValue ? 'transparent' : '#fff'}}" >
  <edit-infor activityTemplateType="{{activityTemplateType}}" acceptAwardImg="{{acceptAwardImg}}" acceptAwardBtn="{{acceptAwardBtn}}" dialogType="receiveInfo" attributes="{{attributes}}" recordId="{{recordId}}" topTitle="领奖信息" bindsaveSuccess="_saveEvent" receiveType="{{receiveType}}" isFirst="{{isFirst}}" getUrlRes="{{getUrlRes}}" msg="{{encryptMsg}}"></edit-infor>
</van-popup>
<!-- 领奖信息弹窗 -->

  <!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" salesCode="{{changeSalesCode}}" showCancel="{{showCancel}}" bind:close="closeChangeTieModal"></switch-sale>
<!-- 分享页面是否切换业务员 -->
<!-- 活动审核显示底部操作按钮 -->
<audit linkType="draw" activityId="{{activityId}}" wx:if="{{audit}}" bind:reject="reject"></audit>
<!-- 活动审核显示底部操作按钮 -->
<!-- 活动审核驳回弹框 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" rejectReason="{{rejectReason}}" audit="{{auditStatus}}" financeStatus="{{financeStatus}}"></reject-reason>
<!-- 活动审核驳回弹框 -->
<contact module="活动参与" pageName="抽奖" pageUrl="{{pageUrl}}" businessData="{{businessData}}" isShow="{{isShowSubmitIncident}}"></contact>

<overTips content="{{ message }}" showClose="{{false}}" isShowOverlay="{{isShowOverlay}}" bind:closeOverTip="closeOverTip"></overTips>

<globalPrompt model:show="{{ showGlobalPrompt }}" content="{{globalPromptMessage}}"></globalPrompt>
