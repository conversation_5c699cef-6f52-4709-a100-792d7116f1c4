// pages/component/calendar/tips/index.js
Component({
  options: {
    addGlobalClass: 'isolated',
  },
  /**
   * 组件的属性列表
   */
  properties: {
    year: Number
  },

  lifetimes: {
    attached () {
      let now = new Date()
      const nowYear = this.properties.year
      this.setData({ nowYear })
      if (nowYear <= now.getFullYear()) {
        this.setData({ isShowLArrow: false })
      }
      if (nowYear >= 2099) {
        this.setData({ isShowArrow: false })
      }
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    list: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'],
    nowYear: '',
    isShowArrow: true,
    isShowLArrow: true,
    currentI: -1
  },

  /**
   * 组件的方法列表
   */
  methods: {
    previousClickEvent () {
      const year = this.data.nowYear - 1
      this.setData({ nowYear: year, isShowArrow: true })
      let now = new Date()
      if (year <= now.getFullYear()) {
        this.setData({ isShowLArrow: false })
      }
    },
    nextClickEvent () {
      const year = this.data.nowYear + 1
      this.setData({ nowYear: year, isShowLArrow: true })
      if (year >= 2099) {
        this.setData({ isShowArrow: false })
      }
    },
    changeM (e) {
      this.setData({ currentI: e.currentTarget.dataset.index })
      var date = `${this.data.nowYear}/${this.data.currentI + 1}/1`
      this.triggerEvent('changeM', date)
    }
  }
})
