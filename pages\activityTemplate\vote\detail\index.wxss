.competeInfo {
  height: 100vh;
  width: 100vw;
  background-size: 100% 100%;
  position: relative;
}
.competeInfo .competeInfo_title {
  display: flex;
  height: 34rpx;
  font-size: 34rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #333333;
  padding: 116rpx 23rpx 28rpx 23rpx;
}
.competeInfo .competeInfo_title .title_name {
  margin: 0 auto;
  text-align: center;
  line-height: 34rpx;
}
.competeInfo .competeInfo_info {
  height: calc(100vh - 191px);
  overflow: auto;
}
.competeInfo .competeInfo_info .uerInfo {
  padding:48rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.competeInfo .competeInfo_info .uerInfo .userInfo_info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.competeInfo .competeInfo_info .uerInfo .userInfo_info .userInfo_image {
  width: 100rpx;
  height: 100rpx;
  margin-right: 24rpx;
}
.competeInfo .competeInfo_info .uerInfo .userInfo_info .userInfo_image image {
  width: 100%;
  height: 100%;
  border: 2rpx solid #fff;
  border-radius: 50rpx;
}
.competeInfo .competeInfo_info .uerInfo .userInfo_content .content_name {
  font-size: 36rpx;
  line-height: 36rpx;
  font-family: PingFang SC-粗体, PingFang SC;
  font-weight: normal;
  color: #17204D;
}
.competeInfo .competeInfo_info .uerInfo .userInfo_content .content_number {
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #555C80;
  line-height: 32rpx;
  margin-top:24rpx;
}
.competeInfo .competeInfo_info .info_pie {
  padding: 0 32rpx;
  padding-bottom: 32rpx;
}
.competeInfo .competeInfo_info .info_pie .pie_info {
  padding: 32rpx;
  background-color: #fff;
  transform: translateY(-10rpx);
  border-radius: 10rpx;
  box-shadow: 0rpx 4rpx 10rpx rgba(23,32,77,.07);
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_top .allPie {
  width: 192rpx;
  height: 108rpx;
  background: #FFF4F2;
  border-radius: 8rpx;
  text-align: left;
  position: relative;
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_top .allPie image{
  position: absolute;
  width: 68rpx;
  height: 48rpx;
  bottom: 0;
  right: 0;
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_top .allPie .allPie_num {
  height: 28rpx;
  font-size: 32rpx;
  font-family: PingFang SC-粗体, PingFang SC;
  font-weight: normal;
  color: #17204D;
  margin-top: 20rpx;
  margin-left: 16rpx;
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_top .allPie .allPie_name {
  height: 24rpx;
  margin-top: 16rpx;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #7E849F;
  margin-left: 16rpx;
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_bottom {
  margin-top: 24rpx;
  border-radius: 10rpx;
  padding: 24rpx;
  background: #FFF4F2;
  border-radius: 16rpx;
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_bottom .bottom_textarea {
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  width: 100%;
  height: 100%;
  color: #17204D;
  overflow: auto;
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_bottom .bottom_textarea span{
  font-size: 24rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #555C80;
}
.competeInfo .competeInfo_info .info_pie .pie_info .info_bottom .bottom_textarea::-webkit-scrollbar {
  display: none;
}
.competeInfo .competeInfo_info .info_pie .pie_message {
  margin-top: 24rpx;
  background-color: #fff;
  box-shadow: 0rpx 4rpx 10rpx rgba(23,32,77,.07);
  border-radius: 10px;
}
.competeInfo .competeInfo_info .info_pie .pie_message .message_top {
  padding-top: 32rpx;
  display: flex;
  align-items: center;
}
.competeInfo .competeInfo_info .info_pie .pie_message .message_top .top_icon {
  width: 6rpx;
  height: 30rpx;
  background-color: #025CEA;
  margin-left: 32rpx;
  margin-right: 8rpx;
}
.competeInfo .competeInfo_info .info_pie .pie_message .message_bottom {
  margin-top: 32rpx;
  padding: 0 0rpx 32rpx 32rpx;
  width: 622rpx;
  height: 500rpx;
}
.competeInfo .competeInfo_info .info_pie .pie_message .message_bottom video{
  width: 622rpx;
  height: 500rpx;
}
.competeInfo .competeInfo_info .info_pie .pie_message .message_bottom swiper {
  height: 100%;
  width: 100%;
}
.competeInfo .competeInfo_info .info_pie .pie_message .message_bottom swiper .image {
  width: 100%;
  height: 100%;
}
.competeInfo .item_bottom {
  position: fixed;
  width: 100%;
  background-color: #fff;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2rpx solid #025CEA;
  border-radius: 48rpx;
  bottom: 68rpx !important;
}
.competeInfo .item_bottom .bottom_cancel {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.competeInfo .item_bottom .bottom_disabled {
  color: #aaa;
  border: 2rpx solid #aaa;
}
.competeInfo .item_bottom .bottom_reset {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: none;
}
.mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 105;
  opacity: 0.7;
}
.modalDlg {
  width: 80%;
  height: 300rpx;
  position: fixed;
  top: 60%;
  left: -1%;
  z-index: 1000;
  margin: -370rpx 85rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: space-between; */
  overflow: hidden;
}
.modalDlg > view > button {
  width: 500rpx;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #FF564AFF;
  background-color: #FF564AFF;
  color: #fff;
}
.modalDlg > text {
  font-size: 30rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 180rpx;
  width: 100%;
  font-weight: bold;
}
.voteSuccess{
  width: 622rpx;
  height: 496rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  position: fixed;
  top: 50%;
  left: 64rpx;
  z-index: 1000;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  margin: -248rpx 0 0;
  text-align: center;
  background-size: 100% 100%;
}
.modal_icon{
  width:44rpx;
  height:44rpx;
  position:absolute;
  top:20rpx;
  right:20rpx;
}
.voteSuccess .title{
  font-size: 32rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #17204D;
  text-align: center;
  margin-top: 28rpx;
}
.voteSuccess .img{
  width: 104rpx;
  height: 104rpx;
  margin:94rpx auto 0;
}
.voteSuccess .desc{
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #555C80;
  position: absolute;
}
.voteSuccess .btn{
  width: 558rpx;
  height: 96rpx;
  background-color: linear-gradient(90deg, #4492FC 0%, #025CEA 100%);
  border-radius: 48rpx;
  font-size: 32rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
}
.competeInfo .competeInfo_info .saleInfo{
  /* width: 160rpx; */
  height: 48rpx;
  background: rgba(255,255,255,0.5);
  box-shadow: inset 0rpx 0rpx 6rpx 0rpx rgba(255,255,255,0.5);
  border-radius: 24rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-right: 10rpx;
}
.saleInfo image{
  width: 40rpx;
  height: 40rpx;
  margin:0 16rpx 0 4rpx;
}
.go-third-desc{
  margin-top: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  color: #555C80;
  line-height: 40rpx;
}
.go-third-btn{
  width: 558rpx;
  height: 96rpx;
  margin: 60rpx auto 0;
  background: linear-gradient(90deg, #4492FC 0%, #025CEA 100%);
  border-radius: 48rpx;
  font-size: 32rpx;
  font-family: PingFang SC-中等, PingFang SC;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
}