<!--pages/activityTemplate/components/edit-infor/index.wxml-->
<view class="mask" hidden="{{loadAnimation==false}}">
  <view class="spinner">
    <view class="bounce1"></view>
    <view class="bounce2"></view>
    <view class="bounce3"></view>
  </view>
</view>
<view class="content" style="background-image: url({{acceptAwardImg.propValue.url}}); width: {{acceptAwardImg.style.width*2}}rpx;">
  <view wx:if="{{activityTemplateType != 14}}" class="topTitle">请确认并补充信息</view>
  <view wx:else class="topTitleFilp">请您准确填写收获地址，奖品将以快递形式为您送达！</view>
    <block  wx:for="{{formItem}}" wx:key="id">

      <block wx:if="{{item.propertyCode == 'address'}}">
        <van-cell title="省市区" required="{{item.required == 1}}" title-width="100rpx" is-link>
          <picker mode="region" bindchange="bindRegionChange" value="{{region}}" data-index = "{{index}}" custom-item="{{customItem}}">
            <view class="picker" wx:if="{{region.length !== 0}}">
              {{region[0]}}-{{region[1]}}-{{region[2]}}
            </view>
            <view class="picker picker_else" wx:else>请选择省市区</view>
          </picker> 
        </van-cell>
      </block>

      <block wx:if="{{item.type == 'input'}}">
        <van-cell title="{{item.propertyName}}" required="{{item.required == 1}}">
          <input type="{{item.propertyType}}" placeholder='{{item.prompt}}'  data-name="{{item.propertyCode}}" value="{{item.defaultValue}}" data-key="{{index}}" bindblur="inputBlur" name="{{item.propertyCode}}" placeholder-style="color:#999999;"/>
        </van-cell>
      </block>
      <block  wx:if="{{item.type == 'radio'}}">
        <van-cell title="{{item.propertyName}}" required="{{item.required == 1}}">
          <van-radio-group value="{{item.defaultValue}}" bindchange="onChange"  data-key="{{index}}" name="{{item.propertyCode}}"  data-name="{{item.propertyCode}}" style="display:flex;justify-content: flex-end;">
            <block wx:for-items="{{item.value}}"   wx:for-item="radios"  wx:key="enumValue">
              <van-radio name="{{radios.enumValue}}" >{{radios.enumName}}</van-radio>
            </block>
          </van-radio-group>
        </van-cell>
      </block>
      <block  wx:if="{{item.type == 'select'}}">
        <van-cell title="{{item.propertyName}}"  required="{{item.required == 1}}" is-link>
          <picker bindchange="bindPickerChange" value="{{index}}" range="{{item.value}}"  wx:for-item="values" range-key="enumName" data-name="{{item.propertyCode}}" data-key="{{index}}">
            <input class="picker" type="text" placeholder="{{item.prompt}}" value="{{item.defaultName}}" name="{{item.propertyCode}}" data-key="{{index}}" disabled />
          </picker>
        </van-cell>
      </block>
      <block  wx:if="{{item.type == 'checkbox'}}">
        <van-cell title="{{item.propertyName}}" required="{{item.required == 1}}" is-link>
          <text bindtap="mulSelect" wx:if="{{item.defaultName}}" data-item="{{item}}" data-key="{{index}}" name="{{item.propertyCode}}">{{item.defaultName}}</text>
          <text bindtap="mulSelect" data-item="{{item}}" data-key="{{index}}" wx:else class="placeHolderText">{{item.prompt}}</text>
        </van-cell>
      </block>
      <block  wx:if="{{item.type == 'file'}}" wx:key="index">
        <view class="imgUpLoad" >
          <view>
          <text wx:if="{{item.required == 1}}" style="color: red;margin-right: 4rpx;">*</text>
          {{item.propertyName}}
          </view>
          <view style="margin: 18rpx 0 24rpx;font-size: 24rpx;color: #7E849F;">（支持附件类型：JPG、PNG大小10M以内）</view>
          <view class="mainTop">
            <com-uploader bind:uploadFile="uploadFile" accept="image" unValidateWH="{{true}}" maxSize="10240" detailPics ="{{item.defaultValue}}" count="{{item.rule*1}}" uploadUrl="{{uploadUrl}}" name="customizeImg" row="row" icon="icon" iconName="" class="compoTopImage" data-name="{{item.propertyCode}}" data-item="{{item}}" data-key="{{index}}" ></com-uploader>
           </view>
        </view>
      </block>
    </block>
   
    <!-- <van-cell-group>
      <van-field
        value="{{ remarks === null ? '' : remarks}}"
        label="备注"
        bind:input="remarksInput"
        name="remarks"
        type="textarea"
        placeholder-style="color:#999999;font-weight:400;"
        input-align="right"
        placeholder="请填写备注"
        autosize
      />
    </van-cell-group> -->
    
    <view class="footerBox">
      <button wx:if="{{acceptAwardBtn}}" bindtap='formSubmit' class='btn0' disabled="{{isYwy}}" style="height: {{acceptAwardBtn.style.height * 2}}rpx;width:{{acceptAwardBtn.style.width * 2}}rpx;border-radius: {{acceptAwardBtn.style.borderRadius*2}}rpx; background-color: {{acceptAwardBtn.style.backgroundColor}};background-image: url({{acceptAwardBtn.bgStatus === 1 ? acceptAwardBtn.imgOptions.url : null}});color: {{acceptAwardBtn.style.color}}">{{acceptAwardBtn.propValue}}</button>
      <button wx:else bindtap='formSubmit' class='btn0' disabled="{{isYwy}}" >保 存</button>
    </view>
  <!-- </form> -->
  <!-- 多选弹窗 -->
  <multi-select show="{{showSelect}}" bind:multipleData="sureData" selectedCode="{{selectedCode}}" selected="{{selectedArry}}" options="{{optionArry}}"></multi-select>
</view>
<van-dialog id="van-dialog" />
