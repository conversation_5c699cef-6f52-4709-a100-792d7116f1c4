<!-- 筛选弹窗 -->
<van-popup show="{{showParamModal}}" close-on-click-overlay bind:click-overlay="closePopup" round position="bottom" :style="{height:100%;}" z-index="100">
  <view class="popup_content"  >
    <view style="{{commdityDialog.styleStr}};">
      <view class="activity_banner">
        <custom-carousel showIcon="{{false}}" currentIndex="{{currentIndex}}" changeSize="{{true}}" isPopup="{{bannerType}}" bannerConfig="{{VSwiper}}"></custom-carousel>
      </view>
      <block wx:for="{{commdityConfig.componentData}}" wx:key="index">
        <view class="activity_price" wx:if="{{item.type=='price'}}" style="{{item.styleStr}};top:{{(item.style.top-99)*2}}rpx">
          <block  wx:if="{{itemObject.price==null||itemObject.price==0}}">
            <view style="{{limitTextConfig.styleStr}}">
              {{limitTextConfig.propValue}}
            </view>
          </block>
          <block wx:else>
            <text>¥ </text>{{itemObject.price}}
          </block>
        </view>
        <view wx:if="{{item.component=='LineShape'}}" style="{{item.styleStr}};top:{{(item.style.top-99)*2}}rpx" class="LineShape"></view>
        <view class="activity_place" wx:if="{{item.type=='place'}}" style="{{item.styleStr}};top:{{(item.style.top-99)*2}}rpx">库存：{{itemObject.place}}/{{itemObject.totalPlace}}</view>
        <view class="project_top" wx:if="{{item.type=='text'}}" style="{{item.styleStr}};top:{{(item.style.top-99)*2}}rpx">{{item.propValue}}</view>
        <block wx:if="{{item.type=='projectList'}}">
          <scroll-view  scroll-y="true" class="scrollView" style="{{item.styleStr}};top:{{(item.style.top-99)*2}}rpx;left:{{item.style.left*2}}rpx;width:{{item.style.width*2}}rpx">
            <view wx:for="{{projectArray}}" wx:for-item="list" wx:key="{{list.id}}" class="{{itemObject.id==list.id?'selectProject':''}} project_box" data-item="{{list}}" bindtap="selectProject"  style="border-color:{{itemObject.id==list.id?item.style.borderColor:''}};background-color:{{item.bgStatus==0?list.place!=0?item.style.stockBackgroundColor:item.style.outStockBackgroundColor:''}};background-image:url({{item.bgStatus==1?list.place!=0?item.imgOptions.url:item.outStockImgOptions.url:''}});width:{{item.style.width*2}}rpx">
              <image src="{{list.detailImg}}" class="project_img"/>
              <view class="project_title">{{list.title}}</view>
            </view>
          </scroll-view>
        </block>
        <view class="bottomBox"  wx:if="{{item.type=='button'}}"  bindtap="sure" style="{{item.styleStr}};background-image:url({{item.bgStatus==1?item.imgOptions.url:''}}">
          {{item.propValue}}
        </view>
      
      </block>
    </view>
  </view>
</van-popup>