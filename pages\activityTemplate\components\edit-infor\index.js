// pages/activityTemplate/components/edit-infor/index.js
const app = getApp();
const http = app.require('/utils/util.js');
const CryptoJS = app.require('/utils/CryptoJs.js');
const API = app.require('/api/request.js');
const { formatPhone, formatIdcard,identity,identityEnum,Sensitive } = app.require('/utils/phone-idcard');

import Dialog from '../../../../assets/vant/dialog/dialog';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    activityTemplateType: {
      type: Number
    },
    acceptAwardImg: {
      type: [Object, String]
    },
    acceptAwardBtn: {
      type: Object
    },
    topTitle: {
      type: String,
      required: true
    },
    attributes: {
      type: Array,
      default: [],
      observer() {
        this.initData()
      }
    },
    
    id: {
      type: [String, Number]
    },
    type: {
      type: String,
      default: "new"
    },
    cardType: {
      type: String,
      default: "ID_CARD"
    },
    category: {
      type: [String, Number]
    },
    recordId:{
      type: Number,
      default:''
    },
    activityId:{
      type: [String, Number]
    },
    dialogType:{
      type: String,
      default:'receiveInfo'
    },
    isFirst: {
      type:Boolean,
      value:''
    },
    getUrlRes:{
      type: Object,
      value: {}  
    },
    msg:{ //企微留资需要的参数
      type: String,
      default:''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    customItem: "",
    region: [],
    page:1,
    radio: '',
    name:'',
    phone:'',
    noSwitch:true,
    shoAddress:false,
    hasMore:false,
    defaultSwitch:false,
    "formItem": [],
    addressList:[],
    row:true,
    icon:true,
    form:{

    },
    // radio:'1',
    index:0,
    placeholder:'',
    isYwy:false,
    loadAnimation: false, // 加载中动画关闭
    selectedArry:'',//多选框的值
    selectedCode:'',//多选框的code
    optionArry:[],
    uploadUrl:'',
    identityType: identityEnum,
    identity:'身份证',
    identityIndex:0,
    cardType:'ID_CARD',
    regularKeys:['phone','name','cardType','cardNo']
  },



  /**
   * 组件的方法列表
   */
  methods: {
    initData(){
      console.log(8888888, JSON.stringify(this.data.attributes))
      const that = this
      let id = this.data.id*1
      let type = this.data.type||''
      let name = this.data.name||''
      let phone = this.data.phone||''
      let phoneShow = this.data.phone?  formatPhone(this.data.phone) : ''
      let idCard = this.data.idcard ||''
      let idCardShow = this.data.idcard? Sensitive(this.data.idcard,this.data.cardType) : ''
      let remarks = this.data.remarks
      let cardType = this.data.cardType
      let addressId=this.data.addressId

      let region = []
      let address1=this.data.address1
      let consignee = ''
      let receivePhone = ''
      let defaultSwitch = this.data.defaultSwitch == 1?true:false
      let defaultAddress = this.data.defaultAddress == 1?true:false
      // console.log(options,'初始值');
      // let num = this.data.num
      //初始化form
      let attribute =  this.data.attributes
      // let attribute =  wx.getStorageSync('attributes')
      let form = this.data.form
      console.log('自定义',attribute)

      attribute.forEach(item=> {
        if(item.propertyCode === 'cardType' && item.value && item.value.length) {
          item.value = item.value.sort((a, b) => +a.enumValue - +b.enumValue)
        }
      })

      let editInfos = wx.getStorageSync('editInfo')||{}
      // console.log(editInfos)
      if(type !='new' && JSON.stringify(editInfos)!='{}'){ //编辑，之前保存自定义属性
        if(editInfos != undefined && JSON.stringify(editInfos)!='{}'){
          name = editInfos.name
          phone = editInfos.phone
          phoneShow = formatPhone(editInfos.phone) 
          idCard = editInfos.idcard
          cardType = editInfos.cardType
          idCardShow = Sensitive(editInfos.idcard,editInfos.cardType)
          remarks = editInfos.remarks
          addressId = editInfos.addressId
          if(editInfos.province){
            region = [editInfos.province, editInfos.city, editInfos.area]
          }
          address1 = editInfos.address
          consignee = editInfos.consignee
          receivePhone = editInfos.receivePhone
          defaultSwitch = editInfos.defaultFlag == 1?true:false
          defaultAddress = editInfos.defaultAddress == 1?true:false
          let otherInfos = editInfos.otherInfo
          if(JSON.stringify(otherInfos) != "{}"){
            let otherInfo = wx.getStorageSync('editInfo').otherInfo //配置的自定义属性
            let keys = Object.keys(otherInfo)
            console.log(keys)
            form = otherInfo
            attribute.map((item,index)=>{
              keys.map((key,i)=>{
                if(item.propertyCode == key){
                  if(item.type == 'input'){
                    item.defaultValue = otherInfo[key]
                    item.defaultName = otherInfo[key]//默认值
                  }else if(item.type == 'checkbox'){
                    item.defaultValue = otherInfo[key]
                    let valueCode = []
                    let selections = otherInfo[key]&&otherInfo[key].split(',') || [] //要转为数组[a,b,c]
                    item.value.map((option,index)=>{
                      selections.map((list)=>{
                        if(option.enumValue == list){
                          valueCode.push(option.enumName)
                        }
                      })
                    })//找出所有多选枚举值的，中文值['选项A','选项B','选项C'].join(',')转为字符串
                    item.defaultName = valueCode.join(',')
                  }else if(item.type == 'file'){
                    // 图片回显
                    console.log( otherInfo[key],'图片')
                    item.defaultValue = otherInfo[key].split(",") || []
                  }else{
                    item.defaultValue = otherInfo[key]
                    let name = item.value.filter((value)=>{return value.enumValue == otherInfo[key]})
                    if(name.length!=0){
                      item.defaultName = name[0].enumName//默认值
                    }
                  }
                  
                }
              })
            })
            
          }
         
        }
      }
      const regularKeysData = {
        name,
        phoneShow,
        idCardShow,
        cardType,
        phone,
        idCard
      }
      attribute&&attribute.map((item,index)=>{
        form[item.propertyCode] = item.defaultValue!=null?item.defaultValue:''
        this.data.regularKeys.map((regularKeys) => {
          if(item.propertyCode === regularKeys ) {
            if (regularKeys === 'name' || regularKeys === 'cardType') {
              item['defaultValue'] = regularKeysData[`${regularKeys}`]
              item['defaultName'] = regularKeys === 'cardType' ?  item.value.filter((item) => item.enumCode === cardType)[0]?.enumName || '' : ''
              item['defaultCode'] = regularKeys === 'cardType' ?  regularKeysData[`${regularKeys}`] : ''
              form[item.propertyCode] = regularKeysData[regularKeys]
            } else {
              item['defaultValue'] = regularKeys === 'cardNo' ? regularKeysData['idCardShow'] : regularKeysData[`${regularKeys}Show`]
              item['defaultName'] = regularKeys === 'cardNo' ?  regularKeysData['idCard'] : regularKeysData[`${regularKeys}`]
              form[item.propertyCode] = regularKeys === 'cardNo' ?  regularKeysData['idCard'] : regularKeysData[regularKeys]
            }
          }
        })
        
      })

      console.log('attributeattributeattribute--✨🍎', attribute)
      that.setData({
        id,
        form,
        formItem:attribute,
        remarks,
        addressId,
        region,
        address1,
        consignee,
        receivePhone,
        type,
        placeholder:remarks!=''?remarks:"请填写备注",
        defaultSwitch,
        defaultAddress,
      })
      that.setData({
        uploadUrl: app.baseUrl+"/upload/inputFileDataFormList"
      })
    },
    bindRegionChange: function (e) {
      console.log(e)
      console.log('picker发送选择改变，携带值为', e.detail.value)
      this.setData({
        region: e.detail.value,
      })
      
    },
    selectAddr(event) {
      console.log(event)
      this.setData({
        radio: event.detail,
      });
    },
    nameInput(e){
      const value = e.detail.value
      this.setData({name: value})
    },
    icCardInput(e){
      const value = e.detail.value
      this.setData({idCard: value,idCardShow: value })
    },
    phoneInput(e){
      const value = e.detail.value
      this.setData({phone: value,phoneShow: value })
    },
    remarksInput(e){
      const value = e.detail
      this.setData({remarks: value})
    },
    defaultSwitchChange(e){
      const value = e.detail
      this.setData({defaultSwitch: value})
    },
    //输入框
    inputBlur(e){
      let value = e.detail.value
      let index = e.target.dataset.key //属性index
      let defaultValue =  e.target.dataset.name
      let formItem = this.data.formItem
      let form = this.data.form
      formItem[index].defaultValue = value //修改默认值
      formItem[index].defaultName = value //修改默认值
      form[defaultValue] = value
      this.setData({
        show: false,
        formItem,
        form
      })
    },
     //下拉框
    bindPickerChange(e){
      let index = e.target.dataset.key //属性index
      let key = e.detail.value //选项index
      let defaultValue =  e.target.dataset.name  //字段名
      console.log('携带值为', e,index,key,defaultValue)
      let formItem = this.data.formItem
      let form = this.data.form
      formItem[index].defaultName = formItem[index].value[key].enumName //修改默认值
      formItem[index].defaultValue = formItem[index].value[key].enumValue //修改默认值
      if(defaultValue === 'cardType') formItem[index].defaultCode = formItem[index].value[key].enumCode
      form[defaultValue] = formItem[index].value[key].enumValue
      if(defaultValue === 'cardType') form[defaultValue] = formItem[index].value[key].enumCode
      console.log(this.data.formItem,form);
      this.setData({
        show: false,
        formItem,
        form
      })
    },
    mulSelect(e){
      console.log(e)
      let item = e.target.dataset.item
      let index = e.target.dataset.key //属性index
      let selected = item.defaultValue&&item.defaultValue.split(',') || []
      item.value.map((list)=>{
        list['select'] = true
        selected.map((item)=>{
          if(item == list.enumValue){
            list['select'] = false
          }
        })
      })
      this.setData({
        showSelect: true,
        mulIndex:index,
        mulProperty:item.propertyCode,
        selectedArry:item.defaultValue,//字段名
        selectedCode:item.defaultName,
        optionArry:item.value
      })
    },
    //多选框
    sureData(e){
      let index = this.data.mulIndex //属性index
      let propertyCode =  this.data.mulProperty
      console.log(e.detail)
      let selectName = e.detail.value
      let selectCode = e.detail.data
  
      let formItem = this.data.formItem
      let form = this.data.form
      formItem[index].defaultName = selectName //修改默认值
      formItem[index].defaultValue = selectCode //修改默认值code
      form[propertyCode] = selectCode
      console.log(this.data.formItem,form);
      this.setData({
        showSelect: false,
        formItem,
        form
      })
    },
    //单选按钮
    onChange(e) {
      let index = e.target.dataset.key //属性index
      let value = e.detail //选项值
      let defaultValue =  e.target.dataset.name  //字段名
      console.log('携带值为',value,defaultValue)
      let formItem = this.data.formItem
      let form = this.data.form
      formItem[index].defaultValue = value //修改默认值
      form[defaultValue] = value
      this.setData({
        formItem,
        form
      });
      console.log(form,formItem);
    },
    // 图片上传
    uploadFile (e) {
      console.log('子传父', e)
      let that = this
      if (e.detail.name == 'customizeImg') {
        let index = e.target.dataset.key //属性index
        let value = e.detail.picsList //选项值
        let defaultValue =  e.target.dataset.name  //字段名
        console.log('携带值为',value,defaultValue)
        let formItem = this.data.formItem
        let form = this.data.form
        formItem[index].defaultValue = value.length!=0?value:'' //修改默认值
        form[defaultValue] = value.length!=0?value.join(','):''
        that.setData({
          formItem,
          form
        })
      } 
    },
    // 值
    onClick(event) {
      console.log(event)
      const addressObj = event.currentTarget.dataset;
      this.setData({
        radio: addressObj.id,
        addressObj
      });
    },
    //提交
    formSubmit: function (e) {
      console.log(e)
      const that = this; 
      const name = this.data.formItem.filter((item) => item.propertyCode === 'name')[0]?.defaultValue  || ''
      const idCard = this.data.formItem.filter((item) => item.propertyCode === 'cardNo')[0]?.defaultName?.trim()  || ''
      const phone = this.data.formItem.filter((item) => item.propertyCode === 'phone')[0]?.defaultName?.trim()  || ''
      const address1 = this.data.formItem.filter((item) => item.propertyCode === 'address')[0]?.defaultName?.trim()  || ''
      
      const remarks =this.data.remarks; 
      const defaultSwitch = this.data.defaultSwitch;
      const defaultAddress = this.data.defaultAddress;
      const addressId = that.data.addressId
      const consignee = that.data.consignee
      const receivePhone = that.data.receivePhone
      const cardType= this.data.formItem.filter((item) => item.propertyCode === 'cardType')[0]?.defaultCode || ''
      const isVoid = this.data.formItem.filter((i,key)=>{
        if(i.propertyCode == "address" && i.required == 1 && !this.data.region.length){
          wx.showToast({
            icon:'none',
            title: `省市区为必填信息！`,
          })
        }
        return i.required == 1 && !i.defaultValue
      })//判断是否为空
      const isTrue =  this.data.formItem.filter((item,key)=> {
        const reg = new RegExp(item.rule)
        if(item.verify == 1 && item.type!='file'&& item.required == 1){
          const valid = reg.test(item.defaultValue)
          return !valid
        }
      });
      console.log(isVoid);
      if(isVoid&&isVoid.length!=0){
        wx.showToast({
          icon:'none',
          title: `${isVoid[0].propertyName}为必填信息！`,
        })
        return
      }
      if(isTrue&&isTrue.length!=0){
        wx.showToast({
          icon:'none',
          title: `${isTrue[0].propertyName}不符合校验规则！`,
        })
        return
      }

      console.log(name,phone,idCard,cardType)
      that.setData({
        loadAnimation:true
      })
      let otherInfo = {}
      this.data.formItem.forEach(item => {
        otherInfo[item.propertyCode] = item.defaultValue
        if(item.propertyCode == 'cardType'){
          otherInfo[item.propertyCode] = item.defaultCode
        }
        if(item.propertyCode == 'cardNo' || item.propertyCode == 'phone'){
          otherInfo[item.propertyCode] = item.defaultName
        }
      })

      if(this.data.region[0]!=undefined){
        let province = this.data.region[0]
        let city = this.data.region[1]
        let area = this.data.region[2]
        otherInfo.address = `${province}${city}${area}${otherInfo.address}`
        console.log(576, otherInfo)
      }

      let dataObj = {
        id:this.data.recordId,
        form: CryptoJS.Encrypt(JSON.stringify(otherInfo)),
        msg:this.data.msg
      }
      // console.log(681, that.form)
      // console.log(667, dataObj)
      API.saveRecord(dataObj).then((res)=>{
        console.log(res)
        if(res.data.code == 200){
          that.setData({
            loadAnimation:false
          })
          
          let currentData = {
            phone:phone,
            name:name,
            idcard:idCard,
            defaultFlag:defaultSwitch == true?1:0,
            defaultAddress:defaultAddress == true?1:0,
            addressId : addressId,
            address : address1,
            consignee:consignee,
            receivePhone:receivePhone,
            remarks:remarks,
            otherInfo:that.data.form,
            cardType:cardType
          }
          let currentObj = Object.assign({},res.data.data,currentData)

          if(this.data.region[0]!=undefined){
            let data = {
              province:this.data.region[0]==undefined?undefined:this.data.region[0],
              city :this.data.region[1]==undefined?undefined:this.data.region[1],
              area :this.data.region[2]==undefined?undefined:this.data.region[2],
            }
            currentObj = Object.assign({}, currentObj, data)
          }
          wx.setStorageSync('editInfo',currentObj)
          if(this.data.isFirst){
            // 今日未跳转第三方
            this.setData({isFirst: false})
            console.log(202, this.data.getUrlRes)
            const src = encodeURIComponent(this.data.getUrlRes.returnUrl)
            wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${this.data.activityId}&src=${src}`})
          }
          this.triggerEvent("saveSuccess")
        }else{
          that.setData({
            loadAnimation:false
          })
          wx.showToast({
            icon:'none',
            title: res.data.message,
          })
        }
      })
    },
    // 选择地址
    adresstop(){
      var type="get"
      console.log(type)
      wx.navigateTo({
        url: '/pages/subpages/myaddress/myaddress?type='+type,
      })
    },
    // 编辑地址
    toedite(e) { 
      var that = this
      var addressid = that.data.addressId
      var type = e.currentTarget.dataset.type
      console.log()
      wx.navigateTo({
        url: '/pages/subpages/addaddress/addaddress?addressid='+addressid+"&type="+type,
      })
    },
    //查询所有地址
    defaultaddress(page,name,phone){
        var that=this
        const userId = wx.getStorageSync("userId")
        let data = {
          current:page,
          size:10,
          name:name,
          phone:CryptoJS.Encrypt(phone)
        }
        API.getMyAddress(data).then(res => {
          if (res.data.code == 200) {
              let addressList = res.data.data.records
              if(addressList.length == 10){
                that.setData({
                  hasMore:true
                })
              }
              console.log(addressList,'地址')
              that.setData({
                addressList,
              })
          } else {
            wx.showToast({
              title: "网络繁忙",
              icon: 'none',
              duration: 2000
            })
    
          }
    
        })
    },
    getMoreAddress(){
      let that = this
      if(!that.data.hasMore){
        return
      }
      let page = this.data.page+1
      let data = {
        current:page,
        size:10,
      }
      API.getMyAddress(data)
        .then(res => {
          if (res.data.code == 200) {
              console.log(res.data)
              if(res.data.data.records.length < 10 || res.data.data.records.length == 0){
                that.setData({
                  hasMore:false,
                  page
                })
              }else{
                that.setData({
                  hasMore:true,
                  page
                })
              }
              if(res.data.data.records.length == 0){
                return
              }
              let list = that.data.addressList
              let newList = list.concat(res.data.data.records)
              that.setData({
                addressList: newList,
              })
          } else {
            wx.showToast({
              title: "网络繁忙",
              icon: 'none',
              duration: 2000
            })
          }
        })
    },
    // 更改证件类型
    identityChange (e) {
      const that = this
      const identityType = that.data.identityType
      console.log('picker发送选择改变，携带值为', e.detail.value);//index为数组点击确定后选择的item索引
      this.setData({
        identityIndex: e.detail.value,
        identity: identityType[e.detail.value].name,
        cardType:identityType[e.detail.value].value
      })
    },
  }
})
