
<view>

</view>
<van-popup
  show="{{ show }}"
  position="bottom"
  custom-style="height: 60%;"
  bind:close="onClose"
>
  <view class="top">
    <view class="cancel" bindtap="onClose">取消</view>
    <view class="sure" bindtap="onConfirm">确定</view>
  </view>
  <view class="content">
    <!-- <scroll-view
      scroll-y 
      scroll-with-animation="true"> -->
      <van-checkbox-group value="{{ checkBoxValue }}" 
      ref="checkBoxGroup"
      bind:change="onChange">
          <van-cell-group>
            <van-cell  wx:for="{{ columns }}"
                wx:key="index"
                title="{{ item.enumName }}"
                value-class="value-class"
                clickable
                data-index="{{ index }}"
                data-id="{{ item.enumValue }}"
                bind:click="toggle">
              <view slot="right-icon"   data-index="{{ index }}">
                <van-checkbox ref="checkboxes" name="{{item.enumValue}}" catch:tap="noop"
        class="checkboxes-{{ index }}"/>
              </view>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
    <!-- </scroll-view> -->
  </view>

</van-popup>