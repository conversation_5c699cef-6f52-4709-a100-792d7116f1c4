/* pages/community/index.wxss */
Page {
  background-color: #FFFFFF;
  font-family: PingFangSC-Regular, PingFang SC;
  /* padding-bottom: calc(constant(safe-area-inset-bottom) + 98rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 98rpx); */
}
.carousel_swiper {
  width: 100%;
  height: 420rpx;
  background: #f4f4f4;
}
.skeletonImg {
  background: #f2f3f5;
}
.page_view {
  width: 100%;
  height: 100%;
}
.searchBox .serachModel {
  line-height: 60rpx;
  /* border-radius: 20rpx; */
  /* display: inline-block; */
  padding: 0;
  font-size: 28rpx;
  height: 60rpx;
  border-radius: 44rpx;
  padding-left: 10rpx;
  color: #676b81;
  font-weight: 400;
  display: flex;
  box-sizing: border-box;
}
.searchBox {
  box-sizing: border-box;
  width: 100%;
  padding: 14rpx 36rpx;
  background-color: #fff;
}
.icon {
  flex-shrink: 0;
  padding: 0 20rpx;
  background: #f2f2f2;
  border-bottom-left-radius: 44rpx !important;
  border-top-left-radius: 44rpx !important;
  height: 60rpx;
  line-height: 80rpx;
}
.search_text {
  flex: 1;
  background: #f2f2f2;
  border-bottom-right-radius: 44rpx !important;
  border-top-right-radius: 44rpx !important;
  height: 60rpx;
}
.field-icon {
  border-bottom-left-radius: 44rpx !important;
  border-top-left-radius: 44rpx !important;
}
.field-input {
  background: #f2f2f2 !important;
  border-bottom-right-radius: 20rpx !important;
  border-top-right-radius: 20rpx !important;
}
.selectCompany{
  display: flex;
  justify-content: space-between;
  padding: 10rpx 32rpx;
  font-size: 24rpx;
}
.location{
  /* display: block; */
  width: 26rpx;
  height: 34rpx;
  vertical-align: bottom;
  margin-right: 18rpx;
}
.switchTag{
  display: flex;
  align-items: flex-end;
}
.switchTag view{
  color: #025CEA;
  
}

/* .filter_wrap{
    width: 100%;
  } */
.postList_wrap {
  height: 100%;
}
.bottom {
  width: 100%;
  padding: 20rpx 9rpx;
  box-sizing: border-box;
}
.bottom_z {
  display: flex;
  justify-content: space-around;
}
.lefts,
.rights {
    width: 50%;
  padding: 0 15rpx;
  box-sizing: border-box;
flex-shrink: 0;
}

.left,
.right {
  width: 100%;
  /* border-radius: 0 0 8rpx 8rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 8rpx 0 #f6f6f6, 0 6rpx 20rpx 0 #f6f6f6;
  margin-top: 20rpx; */
  /* margin-right: 20rpx; */
  
  background-color: #fff;
}
/* .left{
    margin-right: 15rpx;
}
.right{
    margin-left: 15rpx;
} */
.post_wrap {
}
.img_wrap {
  border-radius: 10rpx 10rpx 0px 0px;
  overflow: hidden;
  position: relative;
}
.img_wrap .img {
  /* width: 336rpx; */
  width: 100%;
  /* height: 236rpx; */
}
.topic_wrap {
  position: absolute;
  bottom: 14rpx;
  left: 0rpx;
  display: flex;
  flex-wrap: wrap;
  margin-left: 12rpx;
}
.like_wrap {
  position: absolute;
  top: 16rpx;
  right: 12rpx;
  height: 39rpx;
  background-color: rgba(255,255,255,0.15);
  border-radius: 18rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.topic {
  height: 36rpx;
  background: rgba(255,255,255,0.15);
  border-radius: 18rpx;
  line-height: 20rpx;
  font-size: 16rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #000;
  padding: 8rpx 10rpx;
  margin-right: 16rpx;
  margin-bottom: 5rpx;
  box-sizing: border-box;
}
.post_wrap .title_wrap {
  padding: 12rpx 20rpx;
  box-sizing: border-box;
}
.title_wrap .title {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  font-size: 24rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 34rpx;
}
.like_num {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color:  #666666;
  line-height: 36rpx;
}
.like_icon {
  vertical-align: middle;
  margin-right: 8rpx;
}
.cropCheck {
  /* height: 100%; */
  /* height: 600rpx; */
  padding-bottom: calc(constant(safe-area-inset-bottom) + 98rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 98rpx);
}
.check_cell {
  display: flex;
  justify-content: space-between;
  line-height: 48rpx;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  color: #323233;
  box-sizing: border-box;
}
.check_icon {
  vertical-align: middle;
}
.filter_btn {
  position: fixed;
  right: 48rpx;
  bottom: calc(constant(safe-area-inset-bottom) + 160rpx);
  bottom: calc(env(safe-area-inset-bottom) + 160rpx);
  display: flex;
  flex-direction: column;
  justify-content: end;
  width: 110rpx;
  height: 110rpx;
  overflow: hidden;
  border-radius: 50%;
  box-shadow: 2rpx 4rpx 14rpx #ccc;
  
}
.filter_btn image{
    width: 110rpx;
    height: 110rpx;
    
}
/* .filter_btn image:nth-child(1) {
    margin-bottom: 50rpx;
  } */
/* .popup{
    width: 100%;
} */
.van-dropdown-item {
  position: absolute !important;
  top: 150rpx !important;
}
.van-popup--bottom {
  padding-top: 50rpx;
  box-sizing: border-box;
}
.listContent{
  padding-bottom: 160rpx;
}
.footer_text {
  text-align: center;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #676b81;
  line-height: 44rpx;
  margin: 0 auto;
  margin-top: 40rpx;
  
}
.none{
    font-size: 30rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #676B81;
    text-align: center;
    margin-top: 92rpx;
}
.none image{
  width: 240rpx;
  height: 180rpx;
  margin-bottom: 48rpx;
}
.invitationItem--content.Item__info {
  padding: 0;
  color: #f99;
  margin: 0;
}
/* 新社区样式 */
.invitation {
  height: 100%;
  overflow: auto;
  position: relative;
}

.navTitle{
  color: #000000;
  background: #FFFFFF;
}
.backImg {
  width: 100%;
  height: 983rpx;
}

.invitation_option {
  width: 456rpx;
  top: 157rpx;
  left: 50%;
  transform: translate(-50%, 0);
  position: absolute;
  display: flex;
  z-index: 5;
  justify-content: space-between;
}
.invitation_option__find{
  width: 456rpx;
  left: 50%;
  margin-top: 30rpx;
  position: relative;
  transform: translate(-50%, 0);
  display: flex;
  z-index: 5;
  justify-content: space-between;
}
.invitation_option__item {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.item--title {
  text-align: center;
  font-size: 32rpx;
  font-family: PingFang SC-中等, PingFang SC;
  color: #FFFFFF;
  white-space: nowrap;
}
.item--title--option {
  text-align: center;
  font-size: 32rpx;
  font-family: PingFang SC-粗体, PingFang SC;
  color: #FFFFFF;
  white-space: nowrap;
}
.item--title--other{
  font-family: PingFang SC-中等, PingFang SC;
  font-size: 32rpx;
  font-weight: normal;
  line-height: 34rpx;
  letter-spacing: 0rpx;
  color: #7E849F;
  white-space: nowrap;
}
.item--title--find{
  font-family: PingFang SC-粗体, PingFang SC;
  font-size: 32rpx;
  font-weight: normal;
  line-height: 34rpx;
  letter-spacing: 0rpx;
  color: #17204D;
  white-space: nowrap;
}
.item--line {
  margin-top: 16rpx;
  width: 80rpx;
  height: 6rpx;
  background: #FFFFFF;
  border-radius: 3rpx;
}
.item--line--find{
  width: 42rpx !important;
  background: #025CEA !important;
}
.invitation_info {
  position: absolute;
  bottom: 127rpx;
  left: 33rpx;
}

.invitation_title {
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #FFFFFF;
}

.invitation_intro {
  width: 684rpx;
  height: 106rpx;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.invitationBox__find{
  position: relative;
  overflow: hidden;
  width: 750rpx;
  /* background: linear-gradient(0deg, #F1F1F1, #FFFFFF); */
  border-radius: 24rpx 24rpx 0px 0px;
}
.invitationBox{
  position: relative;
  overflow: hidden;
  top: -90rpx;
  width: 750rpx;
  background: linear-gradient(0deg, #F1F1F1, #FFFFFF);
  border-radius: 24rpx 24rpx 0px 0px;
}
.invitationBox__head{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  margin:40rpx 0 30rpx 0;
}
.invitationBox__head--find{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  /* margin:27rpx 0 16rpx 0; */
  background: #FFFFFF;
}
.invitationBox__head--search{
  width: 630rpx;
  height: 72rpx;
  background: #F2F3F5;
  border-radius: 36rpx;
  position: relative;
}
.invitationBox__head--screen{
  width: 33rpx;
  height: 33rpx;
}
.invitationLabel{
  border-radius: 8rpx;
  padding: 0 32rpx;
  display: flex;
  margin-bottom: 32rpx;
  overflow-x: auto;
  overflow-y: hidden;
}
.labelItema--active{
  background: #E7EEFB !important;
  color: #025CEA !important;
}
.invitationLabel::-webkit-scrollbar {display:none}

.labelItem{
  flex-shrink: 0;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  border-radius: 8rpx;
  background: #E9E9ED;
  margin-right: 24rpx;
  font-family: PingFang SC Medium;
  font-size: 24rpx;
  font-weight: normal;
  color: #17204D;
  padding: 0 34rpx;
}
.search--icon{
  width: 32rpx;
  height: 33rpx;
  position: absolute;
  top: 50%;
  left: 30rpx;
  transform: translate(0,-50%);
}
.search--input{
  width: 100%;
  height: 100%;
  padding-left: 20rpx;
}
.search-placeholder{
  font-size: 28rpx;
  font-family: PingFang SC;
  color: #D0D2DB;
  padding-left: 69rpx;
  position: absolute;
  top: 50%;
  transform: translate(0,-50%);
}
.invitationList{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  position: relative;
  padding: 0 32rpx 30rpx;
}
.invitationItem{
  width: 333rpx;
  font-size: 0;
  /* height: 430rpx; */
  /* background: #FFFFFF; */
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0px 4rpx 24rpx 0px rgba(0,0,0,0.1);
}
.collectionImg{
  position: absolute;
  top: 0;
  width: 333rpx;
  height: 416rpx;
  left: 0;
  z-index: -1;
}
.collectionList{
  padding: 40rpx 0 32rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #17204D;
  font-family: PingFang SC;
  /* font-weight: bold; */
}
.collectionTitle{
  display: flex;
  font-size: 32rpx;
  margin-bottom: 24rpx;
  margin-left: 32rpx;
}
.collectionTitle view{
  font-weight: bold;
}
.collectionTitle image{
  width: 28rpx;
  height: 28rpx;
  overflow: hidden;
  margin-right: 10rpx;
  margin-top: 6rpx;
   
}
.invitationItem--top{
  white-space: nowrap;
  text-align: center;
  top: 0;
  left: 0;
  position: absolute;
  padding: 6rpx 16rpx;
  border-radius: 0px 0px 16rpx 0px;

  background: linear-gradient(to right, #FF5257, #FC6B37);
  font-family: PingFang SC Medium;
  font-size: 22rpx;
  color: #FFFFFF;
  display: flex;
  align-items: center;
}
.invitationItem--right{
  white-space: nowrap;
  text-align: center;
  top: 0;
  left: 0;
  position: absolute;
  padding: 6rpx 16rpx;
  border-radius: 16rpx 0px 16rpx 0px;

  background: linear-gradient(to right, #035DEA, #4492FC);
  font-family: PingFang SC Medium;
  font-size: 22rpx;
  font-weight: normal;
  letter-spacing: 0px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
}
.invitationItem--right image{
  height: 28rpx;
  width: 28rpx;
  margin-right: 8px;
  display: block;
}
.invitationItem--img{
  width: 100%;
  /* height: 240rpx; */
  border-radius: 16rpx 16rpx 0 0;
}
.redDot{
  position: absolute;
  left: 74rpx;
  top: -1rpx;
  width:16rpx;
  height: 16rpx;
  border-radius: 50%;
  background:#FF5030;
  overflow: hidden;
}
.invitationItem--likeImg{
  width: 32rpx;
  height: 30rpx;
  display: block;
  margin-right: 12rpx;
}
.info__right .invitationItem--likeImg{
  width: 34rpx;

}
.invitationItem--content{
  max-height: 74rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 284rpx;
  font-size: 28rpx;
  font-family: PingFang SC;
  /* font-weight: bold; */
  color: #17204D;
  line-height: 38rpx;
  margin: 24rpx  24rpx 22rpx;
}
.info__left,.info__right{
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: space-between;
}
.invitationItem__info{
  padding: 0 24rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* position: absolute;
  left: 0;
  bottom: 0; */
}
.invitationItem--haedImg{
  width: 46rpx;
  height: 46rpx;
  border-radius: 50%;
  display: block;
}
.info--company{
  white-space: nowrap;
  height: 46rpx;
  text-align: center;
  line-height: 46rpx;
  border-radius: 50%;
  background: rgba(2, 92, 234, 0.1);
  font-size: 18rpx;
  font-weight: normal;
  color: #025CEA;
  padding: 0rpx 8rpx;
}
.company--radius{
  border-radius: 24rpx !important;
}
.info--name{
  width: 140rpx;
  height: 34rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 22rpx;
  font-weight: normal;
  line-height: 34rpx;
  letter-spacing: 0px;
  color: #7E849F;
  margin-left: 16rpx;
}
.info--likeNum{
  font-family: PingFang SC Medium;
  font-size: 22rpx;
  font-weight: normal;
  line-height: 34rpx;
  letter-spacing: 0px;
  color: #7E849F;
  margin-right: 8rpx;
}
.backWhite{
  background: #FFFFFF;
}
.backGray{
  /* background: #F2F3F5; */
  padding: 20rpx 32rpx 20rpx;
}
.ql-editor{
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.noneContent{
  width: 320rpx;
  height: 320rpx;
  margin: 200rpx auto 0;
  text-align: center;
  color: #17204D;
  font-size: 28rpx;
}
.noneContent image{
  display: block;
  width: 100%;
  height: 100%;
}
.nav-image{
  position: absolute;
  left: 32rpx;
  top: 2rpx;
}
.tabSwitch{
  width: 44%;
  display:flex;
  justify-content: space-evenly;
}
.tabSwitch .box{
  height: 64rpx;
  line-height: 64rpx;
  position: relative;
  width: 30%;
  text-align: center;
  font-size: 34rpx;
  color: #7E849F;
  /* font-weight: bold; */
  font-family: PingFangSC-Regular, PingFang SC;
}
 .bordrLine {
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  width: 48rpx;
  height: 4rpx;
  background: #025CEA;
  bottom: -4rpx;
  border-radius: 20rpx;
}
.tabSwitch .bordrLine {
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  width: 48rpx;
  height: 6rpx;
  background: #025CEA;
  bottom: -4rpx;
  border-radius: 20rpx;
}
.tabSwitch .activeTab{
  color:#17204D;
  /* font-size: 30rpx; */
}
 .van-search{
  padding: 20rpx 32rpx;
}
 .van-field__input {
  height: 52rpx !important;
  color: #17204D !important;
}
 .van-search__label{
  color: #17204DFF;
  font-size: 28rpx;
}
.van-tabs__line{
  display: none !important;
}
.van-hairline--top-bottom:after{
  border: none !important;
}
.van-tab{
  color:#17204D !important;
  font-family: PingFangSC-Regular, PingFang SC;
}
.van-tab--active{
  /* font-weight: bold !important; */
  color:#025CEA !important;
}
.maskPopup{
  position: fixed;
  /* top: 120rpx; */
  left: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background:rgba(0,0,0,0.6);
  z-index: 9999999999999999;
}
.popupTabs{
  padding: 48rpx 32rpx;
  background: #fff;
  /* height: 288rpx; */
  font-size: 28rpx;
  /* box-sizing: border-box; */
  border-radius: 0 0 32rpx 32rpx;
}
.sort-style{
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.allTabs{
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: 24rpx;
}
.allTabs view,.selectContent view{
  width: 148rpx;
  height: 64rpx;
  border-radius: 200rpx;
  background: #F2F3F5;
  color: #17204D;
  text-align: center;
  line-height: 64rpx;
  margin-right: 30rpx;
  margin-bottom: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
}
.selectContent view{
 font-size: 24rpx;
}
.allTabs view:nth-child(4n+4){
  margin-right: 0;
}
.allTabs .currentTab,.selectContent .currentTab {
  color: #025CEA ;
  background:rgba(229, 238, 252, 1);

}
.selectContent{
  display: flex;
  height: 64rpx;
  background: #fff;
  padding: 20rpx 32rpx 10rpx;
}
.activityBtn{
  color: #FF5030;
  font-weight: bold;
  height: 64rpx;
  width: 196rpx;
  line-height: 64rpx;
  text-align: center;
  border: 1px solid #FF5030;
  border-radius: 48rpx;
  margin-top: 48rpx;
  margin-left: 48rpx;
}
.searchIcon{
  width: 44rpx;
  height: 44rpx;
  position: absolute;
  left: 17rpx;
  top: 34rpx;
}
.van-search__content{
  background: #f2f3f5 !important;
}
.van-search__content--round{
  border-radius: 100rpx !important;
}
.van-search{
  padding-left: 0 !important;
}
.left, .right {
  display: inline-block;
  vertical-align: top;
  width: 48.5%;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  overflow-x: auto;
  height: 80rpx;
}

.navItem_img {
  width: 156rpx;
  height: 56rpx;
}

.navItem {
  flex: 0 0 auto;
  margin-right: 24rpx;
}

.itemActive {
  color: #025CEA;
}