@import '../templates/detailFragment/detailFragment.wxss';
page {
  background-color: rgba(0, 0, 0, 0.5);
}

.mask{
  position:absolute;
  z-index:50;
  width:750rpx;
  height:100vh;
  background-color:rgba(131, 120, 120, 0.3);
  border:none;
  border-radius:0;
  margin: 0;
  top: 0;
  left: 0;
}
.footerHide::before {
  content: '';
  position: absolute;
  width: 103%;
  height: 103%;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}
.mymask {
  position:fixed;
  z-index:49;
  width:750rpx;
  height:100%;
  /* height: calc( 100% - 80rpx ); */
  /* background:rgb(233, 220, 220); */
  background-color:rgba(131, 120, 120, 0.4);
  /* background:transparent; */
  border:none;
  border-radius:0;
}
.mask_content{
  margin-top: 15vh;
}
.reflash_img{
  width: 590rpx;
  height: 880rpx;
  display: block;
  margin: 0 auto;
}
.reflash_text_wrap{
  width: 100%;
  position: absolute;
  top: 20%;
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 46rpx;
  text-align: center;
}
.reflash_btn_wrap{
  width: 100%;
  height: 88rpx;
  position: absolute;
  bottom: 20%;
  text-align: center;
}
.reflash_btn{
  width: 512rpx;
  height: 100%;
  background: #FFAE54;
  border-radius: 16px;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
}
.mask2{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000000;
  opacity: 0.6;
  /* background: #000; */
  z-index: 48;
  /* opacity: 0.7; */
}
.laoding_wrap{
    width: 100%;
    height: 100vh;
    background: #F5F5F5;
    /* position: relative; */
    text-align: center;
    padding-top: 164rpx;
}
.loading_img{
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 28rpx;
  /* position: absolute;
  left: 50%;
  transform: translateX(-50%); */
}
.loading_text{
  
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #676B81;
  line-height: 46rpx;
}
.loading_nav_wrap{
    background:#fff;
    position: fixed;
    width: 100%;
    top: 0;
    /* background: #fff; */
    /* opacity: 0.7; */
    color: #fff;
    z-index: 9999;
    height: 45px;
    left: 0;
}
.loading_nav_content{
  width: 100%;
  position: absolute;
  left: 0;
  /* top: 51rpx; */
  bottom: 9px;
  font-size: 37rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 45rpx;
  text-align: center;
}
.spinner {
  margin: 50vh auto;
  text-align: center;
}
.spinner > view {
  width: 40rpx;
  height: 40rpx;
  background-color: #dd2b2b;
  border-radius: 100%;
  display: inline-block;
  animation: bouncedelay 1.4s infinite ease-in-out;
  animation-fill-mode: both;
}
.spinner .bounce1 {
  animation-delay: -0.32s;
}
.spinner .bounce2 {
  animation-delay: -0.16s;
}
@keyframes bouncedelay {
  0%, 80%, 100% {
    transform: scale(0.0);
  } 40% {
    transform: scale(1.0);
  }
}
.boxxMask{
  position:absolute;
  background: rgba(0, 0, 0, 0.5);
  z-index:50;
  width:750rpx;
  height:100vh;
}
.personLogin{
  position: absolute;
  top: 24%;
  left: 14%;
  background: rgb(245, 230, 230);
  height: 486rpx;
  width: 540rpx;
  padding: 150rpx 118rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
}
.clientLogin{
  width: 308rpx;
  height: 78rpx;
  margin-bottom: 32rpx;
  box-sizing: border-box;
  line-height: 78rpx;
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #D9001A;
  background: #FFFFFF;
  border-radius: 20rpx;
}
.staffLogin{
  width: 308rpx;
  height: 78rpx;
  line-height: 78rpx;
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #FFFFFF;
  background: #D9001A;
  border-radius: 20rpx;
}

.modalDlg{
    width:80%;
    height: 300rpx;
    position: fixed;
    top: 60%;
    left: -1%;
    z-index: 1000;
    margin: -370rpx 85rpx;
    background-color: #fff;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* justify-content: space-between; */
    overflow: hidden;

}
.modalDlg>text{
  font-size:30rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height:180rpx;
  width:100%;
  font-weight: bold;
}
.modalDlg ::-webkit-validation-bubble-text-block{
    height:100rpx;
    margin:0 40rpx;
  }
.modalDlg>view{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.modalDlg>view>button{
  width:500rpx;
  height:80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  /* border-top: 1rpx solid #CCC; */
  border: 1rpx solid#FF564AFF;
  background-color:#FF564AFF;
  margin-bottom: 20rpx;
  color: #fff;
}
.chooseArea {
  width:558rpx;
  position: fixed;
  top: 50%;
  left: 64rpx;
  z-index: 1000;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  padding:48rpx 32rpx;
  height: 540rpx;
  margin: -318rpx 0 0;
}
.chooseArea .title{
  width: 100%;
  line-height: 28rpx;
  margin:0rpx auto;
  text-align: center;
  font-size: 32rpx;
  font-family: PingFangSC-Regular-, PingFangSC-Regular;
  font-weight: normal;
  color: #17204D;
}
.modal_icon{
  width:44rpx;
  height:44rpx;
  position:absolute;
  top:20rpx;
  right:20rpx;
}
.chooseArea .desc{
  font-size: 28rpx;
  font-family: PingFangSC-Regular-, PingFangSC-Regular;
  font-weight: normal;
  color: #555C80;
  margin-top: 48rpx;
}
.choose{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32rpx;
}
.choose view:first-child{
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #555C80;
}
.choose view:first-child span{
  color: #FC3E1B;
}
.chooseClick{
  width: 422rpx;
  height: 64rpx;
  background: #F7F8FA;
  border-radius: 8rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
} 
.chooseClick input {
  width: 100%;
  padding-left: 24rpx;
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #17204D;
  line-height: 64rpx;
}
.chooseArea button{
  width: 558rpx;
  height: 96rpx;
  background:rgba(252, 62, 27, 1);
  border-radius: 48rpx;
  margin-top: 48rpx;
  font-size: 32rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #FFFFFF;
  line-height: 96rpx;
}
.page_main{
  position: relative;
  height:calc(100vh - 196rpx);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
/* 支公司 */
.zgs{
    position: absolute;
    z-index: 99;
    left: 0;
    top: 0;
    height: 50rpx;
    background-color: #dd3c3c;
    border-radius: 0 0 20rpx 0;
    font-size: 20rpx;
    line-height: 50rpx;
    color: #fff;
    padding: 0 10rpx;
}
/* 支公司 */
.activity_main{
  padding:26rpx;
}
.title{
  color: #333333;
  margin-top: 20rpx;
  width:100%;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.title:first-child{
  padding-right: 26rpx;
}
.title view{
  background:#FF564AFF;
  color:#fff;
  display: inline-block;
  width: 122rpx;
  height: 42rpx;
  text-align: center;
  line-height: 42rpx;
  margin-right:10rpx;
  border: 1px solid #FF564AFF;
}
.userinfo-user {
  z-index:40;
  width:750rpx;
  height:100vh;
  background:transparent;
  border:none;
  border-radius:0;
  margin: 0;
}
.footer{
  position:fixed;
  bottom:0;
  display:flex;
  height:128rpx;
  background-color:#ffffff;
  border-top:1rpx #efefef solid;
  width:calc(100% - 64rpx); 
  align-items: center;
  padding:0 32rpx 68rpx;
  justify-content: space-between;
}
.myOrder{
  width: 328rpx;
  height: 96rpx;
  border-radius: 48rpx;
  /* border: 2rpx solid #FF5030; */
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: 400;
  color: #FF5030;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.buybtn{
  width: 328rpx;
  height: 96rpx;
  background-color: linear-gradient(90deg, #FC6B37 0%, #FC3E1B 100%);
  border-radius: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.reject{
  border:1px solid #025CEA;
  color: #025CEA;
}
.nextStep{
  width: 328rpx;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 48rpx;
  background: linear-gradient(to right, #025CEA ,#4492FC);
  font-size: 32rpx;
  font-family: 'PingFang SC-Bold';
  text-align: center;
  color: #FFFFFF;
}
/* .nextStep{
  width: 328rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  background: linear-gradient(to right,#025CEA,#4492FC);

} */
.warnbtn{
  width: 328rpx;
  height: 96rpx;
  background-color: linear-gradient(90deg, #4492FC 0%, #025CEA 100%);
  border-radius: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.buybtnbom{
  position: absolute;
  bottom: 0;
  right: 0;
  background: none;
  line-height:50rpx ;
  font-size: 24rpx;
  width:375rpx;
  z-index: 100;
  height: 100rpx;
}
.activity_time{
  text-align: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #24201D;
  position: absolute;
}
.activity_time_img{
  position: absolute;
  bottom:-90rpx;
  left:0;
  right:0;
  width: 100%;
  height: 100rpx;
}
.activity_time .time_main{
  font-size: 24rpx;
  font-weight: 600;
  color: #17204D;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.activity_time .time_main span{
  background: #17204D;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin:0 6rpx;
  min-width:40rpx;
  height:40rpx;
  display: inline-block;
  vertical-align: middle;
  line-height: 40rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* ---弹框 */
.boxx{
    width: 460rpx;
    /* height: 780rpx; */
    position: fixed;
    top: 16%;
    left:0;
    margin-left: 103rpx;
    border-radius: 20rpx;
    padding:0 34rpx 60rpx 60rpx;
    box-shadow:-2rpx 6rpx 24rpx 2rpx rgba(0,0,0,.12);
    z-index: 9000 ;
    background-color: #ffffff;
    background-image: url('https://s1.imagehub.cc/images/2020/12/04/toast.png');
    background-repeat: no-repeat;
    background-size: 110% 100%;
  }
  .calcelImg{
    position: absolute;
    right:0;
    top:-92rpx;
    width:54rpx;
    height: 92rpx;
  }
  .pay {
    width:100%;
    height:70rpx;
    font-size:30rpx;
    color:#333333;
    border-radius:0;
    text-align:center;
    line-height: 70rpx;
    font-family: PingFang SC;
    background: linear-gradient(-51deg, #CF3437, #D15F37);
    box-shadow: 0rpx 0rpx 36rpx 0rpx rgba(129, 14, 14, 0.17);
    border-radius: 35rpx;
    color:#fff;
    margin-top:36rpx;
  }
  .input-wrapper {
    display:flex;
    height:80rpx;
    font-size:30rpx;
    /* margin-top: 40rpx; */
    align-items: center;
    position: relative;
    border-bottom:rgba(0,0,0,.21) solid 1rpx;
  }
  .sale_title{
    margin-top:40rpx;
  }
  .input-wrapper text {flex:none;flex-basis:180rpx;text-align: center}
  .input-wrapper input {width:500rpx;margin-top: 2rpx;font-size: 24rpx;color: #AAAAAA;}
  .input-wrapper button {font-size:30rpx;height:46rpx;line-height:46rpx;padding:0 16rpx;flex:none;color:#ffffff;}
  .xx{width: 100rpx;height: 50rpx;font-size: 36rpx;line-height: 44rpx;color: #999999;margin-top: 10rpx;text-align: right;padding-right: 24rpx;margin-left: 564rpx}
  .dianhf{width: 100%;height: 100%}
  .hm{font-family:PingFang SC;font-size: 30rpx;color: #333333;padding: 0 20rpx;width: 70rpx;}
  .phonenum{width: 250rpx;}
  .dianhf {background:rgba(0,0,0,.3);position: fixed;top:0;left:0;z-index:103;width:100%;height:100%;}
  .phonetitle{
    width: 100%;
    font-family:PingFang SC;
    height: 108rpx;
    text-align: left;
    line-height: 108rpx;
    position: relative;
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }
  .isywy{
    width: 150rpx;
    height: 108rpx;
    line-height: 108rpx;
    font-size: 24rpx;
    color: #999999;
    position: absolute;
    right: 16rpx;
    top: 0;
    font-weight: 400;
    font-style: italic;
    color: #EE595E;
    text-decoration: underline;
    text-align: right;
  }
  .boxx_title {
    font-size: 28rpx;
    font-weight: 400;
    color: #333333;
    line-height: 60rpx;
  }
  .choosebox{
    position: absolute;
    bottom: 0rpx;
    right: 0;
    z-index: 10;
    }
    .choosebox image{
      width:30rpx;
      height:30rpx;
    }
  .itemsname{width: 100%;height: 50rpx;background: #ffffff;font-size: 20rpx;line-height: 50rpx;padding-left: 20rpx;}
  .boxxMC{
    background: rgba(0,0,0,0.5);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
  }
  .sign_box{
    width:600rpx;
    height:790rpx;
    position: fixed;
    top:10%;
    left:80rpx;
  }
  .sign_bg{
    width: 600rpx;
    height: 790rpx;
    position: absolute;
    top:0;
    left:0;
    z-index: -1;
  }
  .sign_close{
    width:58rpx;
    height:58rpx;
    position: absolute;
    bottom: -94rpx;
    left: 271rpx;
  }
  .sign_main{
    width:400rpx;
    height: 300rpx;
    margin-top:280rpx;
    margin-left:100rpx;
  }
  .sign_top{
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    font-size: 23rpx;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #D75E02;
    line-height: 35rpx;
    opacity: 0.9;
  }
  .sign_line{
    width: 100rpx;
    height: 2rpx;
    background: #D75E02;
  }
  .sign_coin{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-top:20rpx;
  }
  .sign_item{
    width: 82rpx;
    /* height: 62rpx; */
    /* border:red solid 1px; */
    margin-left:15rpx;
    margin-bottom:30rpx;
  }
  .sign_item image{
    width:82rpx;
    height:62rpx;
  }
  .sign_item view{
    font-size: 19rpx;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #BEBEBE;
    text-align: center;
  }
  .sign_btn{
    width:302rpx;
    height: 91rpx;
    font-size: 32rpx;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #D31112;
    position: relative;
    text-align: center;
    line-height: 91rpx;
    margin-left: 154rpx;
    margin-top: 50rpx;
  }
  .sign_btn image{
    width:302rpx;
    height: 91rpx;
    position: absolute;
    top:0;
    left:0;
    z-index: -1;
  }

  .sign_item:first-child{
    margin-left:0;
  }
  /* ---弹框 */
 /* 立即绑定 */
.lod{
    width: 100%;
    height: 82rpx;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    background: #FFF4DF;
    border-radius: 20rpx;
    margin-bottom:32rpx;
  }
  .dlqb{
    margin-left: 30rpx;
    line-height:82rpx ;
    font-size:24rpx;
    font-family:PingFang SC;
    font-weight: 500;
    color: #AF794A;
  }
  .phonebtnlogin{
    width: 150rpx;
    height: 44rpx;
    z-index: 10;
    right: 0rpx;
    bottom: 100rpx;
    position: absolute;
    font-size: 20rpx;
    color: #ffffff;
    line-height: 44rpx;
    background: #CF3437;
    border-radius: 20rpx;
  }
  .phonebtnlogin::after{border: none;}
  .phonebtnlogin1{
    width: 150rpx;
    height: 44rpx;
    z-index: 10;
    right: 0rpx;
    bottom: 14rpx;
    position: absolute;
    font-size: 20rpx;
    color: #ffffff;
    line-height: 44rpx;
    background: #CF3437;
    border-radius: 20rpx;
  }
  .phonebtnlogin1::after{border: none;}

  .code{
    width: 132rpx;
    height: 50rpx;
    z-index: 10;
    right: 0rpx;
    bottom: 14rpx;
    position: absolute;
  }
  .login_to{
    width:180rpx;
    height:60rpx;
    border-radius:28px;
    margin-top: 14rpx;
    position: absolute;
    right: 20rpx;
    font-size:24rpx;
    font-family:PingFang SC;
    font-weight:bold;
    color:rgba(255,255,255,1);
    line-height: 56rpx;
    text-align: center;
    text-decoration: underline;
    color: #AF794A;
  }
  /* 立即绑定 */

  /* 高尔夫活动 */
  .golf_activity{
    /* position: absolute; */
    width: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 32rpx;
    box-sizing: border-box;
    margin-bottom: 24rpx;
  }
  .golf_activity_container{
    /* position: absolute; */
    width: 100%;
    background-color: #fff;
    padding:32rpx 32rpx 16rpx;
    border-radius: 16rpx;
    box-sizing: border-box;
    margin-bottom: 24rpx;
  }
.golfContent{
  position: absolute;
}
  .golf_activity_detail{
    display: flex;
    justify-content: space-between;
    background-color: #ffffff;
    padding: 38rpx 20rpx 38rpx;
    margin-bottom: 20rpx;
    align-items: center;
    border-radius: 20rpx;
    overflow: hidden;
    position: relative;
  }
  .goGolfImg{
      width: 56rpx;
      height: 56rpx;
      /* position: absolute; */
      /* top: 0;
      left: 0; */
  }
  .golf_activity_detail_left{
    flex: 75%;
    display: flex;
    align-items: center;
    /* flex-direction: column; */
    /* justify-content: space-around; */
    /* z-index:5; */
    /* position: absolute;
    top:-14rpx;
    left: 30rpx; */

  }
  .golf_activity_title_wrap{
      flex: 1;
    /* margin-top: 31rpx; */
    display: flex;
    flex-direction: column;
    /* justify-content: space-a; */
    box-sizing: border-box;
    /* margin-bottom: 24rpx; */
    /* display: flex; */
  }
  .golf_activity_title{
    color: #17204D;
    font-size: 36rpx;
    margin-bottom: 32rpx;
    font-family: 'PingFang SC-Bold';
  }
  .golf_activity_address{
    color: #17204D;
    font-size: 28rpx;
    font-family: 'PingFang SC-Medium';
    border-bottom: 1px solid #EBEDF0;
    margin-bottom: 16rpx;
  }
  .golf_activity_address view{
    margin-bottom: 32rpx;
    /* color: #555C80; */
  }
  .golf_activity_address view text{
    margin-right: 32rpx;
  }
  .golf_activit_title_img_wrap{
      height: 56rpx;
      width: 56rpx;
      vertical-align: middle;
      margin-right: 20rpx;
  }
  .golf_activit_title_img{
      width: 100%;
      height: 100%;
  }
  .golf_activity_detail_right{
    flex: 25%;
    /* position: relative; */
    height: 100%;
    width: 44rpx;
    display: flex;
    justify-content: flex-end;
  }
  .goGolfSpace{
    font-size: 26rpx;
    font-weight: 800;
    color: #cf3437;
  }
  .imgRight{
    width: 144rpx;
    height: 64rpx;
    border-radius: 48rpx;
    vertical-align: middle;
    font-size: 26rpx;
    font-family: 'PingFang SC-Medium';
    font-weight: 500;
    color: #FFFFFF;
    text-align: center;
    line-height: 64rpx;
    padding: 0 14rpx;
    box-sizing: border-box;
  }
  .unselect{
    background: #FF564A;
  }
  .select{
    /* background: #C0C0C0; */
    border: 1px solid #FF5030;
    color: #FF5030 ;
  }
  .selects{
    background: #C0C0C0;
    color: #fff ;
  }
  .border_line{
    width: 100%;
    height: 1rpx;
    background-color: #ccc;
    opacity: 0.3;
  }
  .activity_detail_bg{
    width: 686rpx;
    height: 100vh;
    background: linear-gradient(180deg, #FFEEEB 0%, #FFFAF9 100%);
    border-radius: 16rpx;
    position:absolute;
    left: 32rpx;
    top:774rpx;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .rights{
    width: 128rpx;
    height: 36rpx;
    background-color: linear-gradient(90deg, #4492FC 0%, #025CEA 100%);
    border-radius: 0rpx 16rpx 0rpx 16rpx;
    position: absolute;
    color: #fff;
    top:870rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    /* left: 566rpx; */
  }
  .rights .sale_place{
    border-radius: 16rpx 0 0 16rpx;
    font-size: 20rpx;
    font-weight: 500;
    /* line-height:36rpx; */
    text-align: center;
    display: inline-block;
    padding: 0 10rpx;
    /* height:36rpx; */
    /* display: flex;
    justify-content: center;
    align-items: center; */
  }
  .activity_detail_time{
    width: 638rpx;
    position: absolute;
    top:812rpx;
    left: 56rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .left{
    position: absolute;
    line-height: 40rpx;
    height: 40rpx;
  }
.left .red_price{
  font-size: 36rpx;
  font-weight:500;
  color: #FF5030;
  font-family: PingFang SC-Medium, PingFang SC;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.left .red_price .wordTip{
  font-size: 28rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 400;
  color: #17204D;
}
.bla_price{
  font-size: 24rpx;
  font-family: DIN-Medium, DIN;
  font-weight: 500;
  color: #7E849F;
  text-decoration:line-through;
  margin-left: 16rpx;
}
.activity_detail_title{
  position: absolute;
  top: 918rpx;
  left: 88rpx;
  font-size: 36rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: 400;
  color: #17204D;
  line-height: 36rpx;
  width: 568rpx;
  /* overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; */
}
.activity_detail_time_title{
  position: absolute;
  top: 986rpx;
  left: 88rpx;
  font-size: 20rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 400;
  color: #17204D;
  line-height: 44rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.activity_detail_time_title view {
  width: 100rpx;
  height: 36rpx;
  border-radius: 8rpx;
  border: 2rpx solid #FC3E1B;
  font-size: 20rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 400;
  color: #FC3E1B;
  line-height: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16rpx;
}
.totalNum{
  position: absolute;
  top: 1054rpx;
  left: 88rpx;
  width: 510rpx;
  height: 48rpx;
  background: #F7F8FA;
  border-radius: 200rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding:0 32rpx;
}
.sale_free {
  font-size: 24rpx;
  font-weight: 400;
  color: #555C80;
  line-height: 48rpx;
  text-align: center;
}
.sale_free .mount{
  color: #17204D;
}
.detailBg{
  width: 686rpx;
  height: 840rpx;
  background: linear-gradient(180deg, #FFEEEB 0%, #FFFAF9 100%);
  border-radius: 16rpx;
  position: absolute;
  top: 1198rpx;
  left: 32rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.activity_detail_poster{
  width: 20rpx;
  height: 96rpx;
  background: linear-gradient(90deg, #FF5030 0%, #FC6B37 100%);
  position: fixed;
  top: 276rpx;
  left: 710rpx;
  color: #fff;
  font-family: PingFang SC-Medium;
  font-size: 20rpx;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx 10rpx;
}
.activity_detail_credit{
  position: absolute;
  padding: 8rpx;
}
.weui-input{
  padding-left: 24rpx;
}
.popup_content{
  padding: 32rpx;
  box-sizing: border-box;
}
.activity_banner{
  width: 686rpx;
  height: 368rpx;
  border-radius: 16rpx;
  margin-bottom: 48rpx;
  overflow: hidden;
}
.activity_price{
  font-size: 36rpx;
  color: #FF5030;
  font-weight: 500;
}
.activity_price text{
  font-size: 24rpx;
}
.place_box{
  padding-bottom: 24rpx;
  border-bottom: 1px solid #F2F3F5;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
}
.activity_place{
  font-size: 24rpx;
  color:#7E849F
}
.project_top{
  font-family: PingFang SC-Bold;
  font-size: 36rpx;
  color: #17204D;
  margin-bottom: 24rpx;
  font-weight: bold;
}
.project_box{
  width: 686rpx;
  height: 128rpx;
  border-radius:16rpx;
  background: #F7F7F7 ;
  font-size: 28rpx;
  color: #17204D ;
  padding: 24rpx;
  box-sizing: border-box;
  display: flex;
  margin-bottom: 24rpx;
  align-items: center;
}
.scrollView{
  overflow: hidden;
  height: 426rpx;
  padding-bottom: 24rpx;
  box-sizing: border-box;
}
.project_img{
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}
.project_title{
  width: 534rpx;
}
.bottomBox .sure{
  width: 686rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 48rpx;
  color: #fff;
  background:linear-gradient(to right,#025CEA ,#4492FC);
}