<!--pages/activityTemplate/signIn/home/<USER>
<page-meta page-style="{{ showPoster||siginInDialogShow||repairDialogShow||awardingShow ? 'overflow: hidden;' : '' }}" />

<!-- 加载动画 -->
<loading-animation wx:if="{{myLoadAnimation}}"></loading-animation>
<!-- 加载动画 -->

<!-- 刷新弹窗 -->
<view wx:if="{{reflashToast}}">
  <view class="mymask">
    <view class="mask_content">
      <image class="reflash_img" src="https://txqmp.cpic.com.cn/uploads/img/refresh.png" lazy-load="false" />
      <view class="reflash_text_wrap">
        <view>啊哦~</view>
        <view>大伙的热情过于高涨，</view>
        <view>请稍等片刻~</view>
      </view>
      <view class="reflash_btn_wrap">
        <button class="reflash_btn" disabled="{{!isReflash}}" bindtap="reGetActivity">
          刷新{{reflashBtnContent}}
        </button>
      </view>
    </view>
  </view>
  <view class="mask2" catchtouchmove="preventTouchMove"></view>
</view>
<!-- 刷新弹窗 -->
<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close"></login-box>
<!-- 登陆者身份选择 -->
<!-- 引发授权的弹窗 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler" class="userinfo-user" hidden="{{InfoShow == false}}">
      点击进入
    </button>
  </view>
</view>
<!-- 引发授权的弹窗 -->


<view class="equity-home-nav" style="height: {{navBarData.navBarHeight}}px">
  <view class="home_nav_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px">
    <view class="{{isShowTitle ? 'fade-in' : 'fade-out'}}">签到打卡</view>
    <van-icon name="arrow-left" size="1.3em" color="#fff" bind:click="back" class="home_nav_menu_icon" style="left:{{navBarData.menuRight}}px;" />
  </view>
</view>

<view class="signin-wrap" wx:if="{{!myLoadAnimation && !showModalDlg}}">
  <view class="page_main" style="background-image:url({{backgroundImg}})" scroll-y="true"></view>

  <block wx:for="{{componentData}}" wx:key="index">
    <block wx:if="{{item.type === 'poster' && activity.flagPoster == 1}}">
      <!-- 专属海报弹框 -->
      <post-popup show="{{showPoster}}" codeUrl="{{activity.qrcode}}" activityName="{{activity.title}}" btnText="{{item}}" banner="{{posterBackgroundImg.propValue.url}}" title="{{posterText.propValue}}" textStyle="{{posterText}}" bind:onClickHide="onClickHide" bind:clickDownPoster="downPoster" audit="{{audit}}" mode="scaleToFill"></post-popup>
      <!-- 专属海报弹框 -->
    </block>
    <block wx:if="{{item.btEvent === 'rule'}}">
      <view class="signin-btn rule-btn" style="{{item.styleStr}}" bindtap="goRule">{{item.propValue}}</view>
    </block>

    <block wx:if="{{item.btEvent === 'awarding' && isShowAwardBtn}}">
      <view class="signin-btn awarding-btn" style="{{item.styleStr}};" bindtap="awardingClick">{{item.propValue}}</view>
    </block>
    <block wx:if="{{item.btEvent === 'signIn'}}">
      <view class="signin-btn signin-btn-sign" style="{{item.styleStr}};padding-right: 48rx;" bindtap="signInClick">{{item.propValue}}</view>
    </block>
    <block wx:if="{{item.type === 'text'}}">
      <view class="repair-num" style="{{item.styleStr}}">您当前剩余补卡次数：{{fixTimes}}次</view>
    </block>
    <view class="calendar-wrap" wx:if="{{item.type === 'calendar'}}" style="top: {{calendarSet.style.top*2}}rpx;left: {{calendarSet.style.left*2}}rpx;">
      <s-calendar initDisplayDate="{{initDisplayDate}}" beginTime="{{activeTimeStart}}" endTime="{{activeTimeEnd}}" class="s-calendar" binddateTipClick="dateTipClick" showTitle="{{false}}" dateText="{{dateText}}" showMark="{{false}}" fixRow="{{true}}" signTimes="{{signTimes}}" calendarSet="{{calendarSet}}"></s-calendar>
    </view>
    <view class="prize-table-wrap" wx:if="{{item.type === 'signInPrize'}}">
      <view class="prize-img">我的奖品</view>
      <prize-table table-header-title="{{tableHeaderTitle}}" table-data="{{tableData}}" signInPrizeStyle="{{signInPrizeStyle}}"></prize-table>
    </view>
    <block wx:if="{{item.type === 'titleText'}}">
      <view class="calendar-bottom-tip" style="{{item.styleStr}}">
        {{item.propValue.after}}
        <text class="calendar-bottom-tip-day">{{signTimes}}</text>
        {{item.propValue.before}}
      </view>
    </block>
  </block>
</view>

<!-- 签到奖励弹窗开始 -->
<van-popup custom-style="overflow: visible;height: {{isHasSigninPrize ? '646rpx' : '548rpx'}};" show="{{ siginInDialogShow }}" round closeable bind:close="siginInDialogShowClose">
  <view class="dialog-container" style="background-image:url({{signIndialogBgImg.propValue.url}})">
    <block wx:for="{{signIndialogConfig.componentData}}" wx:key="index">
      <block wx:if="{{item.type === 'icon'}}">
        <view class="img-icon" style="background-image:url({{item.propValue.url}})"></view>
      </block>
      <block wx:if="{{item.type === 'text'}}">
        <view class="sign-success-text" style="{{item.styleStr}};left:0;">{{item.propValue}}</view>
      </block>
      <block wx:if="{{item.type === 'signInTipPrize' && isHasSigninPrize}}">
        <view class="sign-prize-img" style="background-image:url({{signinPrizeInfo.image}})"></view>
        <view class="sign-prize-title">{{signinPrizeInfo.name}}</view>
        <!-- <view class="sign-prize-desc" style="font-size:{{item.style.fontSize*2}}rpx;font-weight:{{item.style.fontWeight}};color: {{item.style.color}};">{{activity.activitySign.receiveType=='1' ? '点击下方按钮参与问卷后，联系营销员获取奖励' : '点击下方按钮阅读内容后，联系营销员获取奖励'}}</view> -->
        <view class="sign-prize-desc" style="font-size:{{item.style.fontSize*2}}rpx;font-weight:{{item.style.fontWeight}};color: {{item.style.color}};">{{signinPrizeInfo.content}}</view>
      </block>
      <block wx:if="{{item.type === 'button'}}">
        <view class="sign-prize-btn" style="{{item.styleStr}};top: {{isHasSigninPrize ? '502rpx' : '400rpx'}};" bindtap="{{isHasSigninPrize ? 'signInReceiveClick' : 'signIndialogClose'}}">{{item.propValue}}</view>
      </block>
    </block>
  </view>
</van-popup>
<!-- 签到奖励弹窗结束 -->

<!-- 补卡弹窗开始 -->
<van-popup custom-style="overflow: visible;" show="{{ repairDialogShow }}" round closeable bind:close="repairDialogShowClose">
  <view class="dialog-container repair-container" style="background-image:url({{repairDialogBgImg.propValue.url}})">
    <view class="img-icon"></view>
    <view class="repair-title">补卡</view>
    <block wx:for="{{repairDialogConfig.componentData}}" wx:key="index">
      <block wx:if="{{item.type === 'text'}}">
        <view class="repair-desc" style="{{item.styleStr}};">{{item.propValue}}</view>
      </block>
      <block wx:if="{{item.type === 'button' && item.btEvent === 'cancel'}}">
        <view class="repair-btn repair-left-btn" style="{{item.styleStr}};" bindtap="repairDialogclose">{{item.propValue}}</view>
      </block>
      <block wx:if="{{item.type === 'button' && item.btEvent === 'confirm'}}">
        <view class="repair-btn repair-right-btn" style="{{item.styleStr}};" bindtap="repairClick">{{item.propValue}}</view>
      </block>
    </block>
  </view>
</van-popup>
<!-- 补卡弹窗结束 -->

<!-- 连续签到奖励弹窗开始 -->
<van-popup custom-style="overflow: visible;" show="{{ awardingShow }}" round closeable bind:close="awardingShowClose">
  <view class="dialog-container awarding-dialog-container" style="background-image:url({{awardDialogBgImg.propValue.url}})">
    <image class="img-icon" src="{{awarPrizeInfo.image}}" />
    <view class="sign-success-text">{{awarPrizeInfo.prizeName}}</view>
    <block wx:for="{{awardDialogConfig.componentData}}" wx:key="index">
      <block wx:if="{{item.type === 'text'}}">
        <!-- <view class="awarding-prize-desc" style="{{item.styleStr}};left: 0;">{{activity.activitySign.receiveType=='1' ? '点击下方按钮参与问卷后，联系营销员获取奖励' : '点击下方按钮阅读内容后，联系营销员获取奖励'}}</view> -->
        <view class="awarding-prize-desc" style="{{item.styleStr}};left: 0;">{{awarPrizeInfo.content}}</view>
      </block>
      <block wx:if="{{item.type === 'button'}}">
        <view class="sign-prize-btn" style="{{item.styleStr}};" bindtap="awardReceiveClick">{{item.propValue}}</view>
      </block>
    </block>
  </view>
</van-popup>
<!-- 连续签到奖励弹窗结束 -->

<view wx:if="{{audit}}" class="audit-space"></view>
<audit wx:if="{{audit}}" bind:reject="reject" activityId="{{ids}}" activityType="{{category}}"></audit>
<reject-reason showDialog="{{showReject}}" activityId="{{ids}}" rejectReason="{{rejectReason}}" audit="{{activity.auditStatus}}" financeStatus="{{activity.financeStatus}}" bind:rejectButton="triggerEvent"></reject-reason>
<contact module="活动参与" pageName="集卡首页" pageUrl="{{pageUrl}}" businessData="{{activity}}" isShow="{{isShow}}"></contact>