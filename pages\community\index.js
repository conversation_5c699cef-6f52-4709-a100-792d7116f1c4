// pages/community/index.js
const API = require('../../api/request.js')
const app = getApp();
Page({
  data: {
    showTab: true,
    isClearText: false,
    // { id: 0, name: '频道' },
    // tabSwitch:[{ id: 0, name: '频道' },{ id: 1, name: '推荐' }, { id: 2, name: '本地' }],
    tabSwitch: [{ id: 1, name: '推荐' }, { id: 2, name: '本地' }],
    tabId: 1,
    tabs: [],
    allTabs: [
      { id: 9, name: '玩转太享趣' },
      { id: 0, name: '推荐' }, { id: 1, name: '文化艺术' }, { id: 2, name: '休闲娱乐' },
      { id: 3, name: '户外旅游' },
      { id: 4, name: '亲子教育' },
      { id: 5, name: '茶饮美食' },
      { id: 6, name: '运动锻炼' },
      { id: 7, name: '爱心公益' },
      { id: 8, name: '保险服务' },
    ],
    isJurisdiction: false, // 是否有玩转太享趣发帖权限
    active: '玩转太享趣',
    subsume: [{ id: 0, name: "全部", params: null }, { id: 1, name: "点赞最多", params: 1 }, { id: 2, name: "最近发布", params: 0 }, { id: 3, name: "热度最高", params: 2 }],
    subsumeId: 0,
    keyword: '',
    collection: [],
    show: false,
    activityList: [], // 社区顶部轮播图
    fineList: [], // 发现list
    postList: [],
    leftList: [],
    rightList: [],
    page: 1,  //发现页数
    size: 10,  // 发现条数
    total: 0,
    optionPage: 1,  // 其他活动页数
    optionSize: 10,  // 其他活动条数
    pages: -1,
    loading: false,
    showSelectCompany: true,
    popupShow: false,
    showNomore: false,
    isDisLogo: false,//isDisLogo 默认不传,只有筛选的时候才传
    checks: [],
    order: '1',
    checkAll: true,
    checkAllLabel: true,   // 标签全选
    orderOptions: [
      { text: '最近发表优先', value: '0' },
      { text: '点赞从高到低', value: '1' },
    ],
    item2_title: '点赞从高到低',
    cropOptions: [],
    userType: 0,
    loadding: false,
    isScroll: false,
    scrollTop: -1,
    height: app.globalData.height,
    isOther: false,   //默认展示发现页面
    isShowFind: false,   //默认不展示发现
    currentActivity: {},   // 当前展示的活动对象
    isLoading: false,   // 默认不在加载中
    isLoadingOption: false,   // 其他活动 默认不在加载中
    isMore: false,   // 默认还有更多
    isMoreOption: false,   // 其他活动 默认还有更多
    optionInvitationList: [],   // 其他活动的帖子数据(发现右边的)
    labelList: [],//全部标签
    selectLabelList: [],//已选标签
    isIssueJurisdiction: false,//是否有发帖权限
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function (options) {
    this.getHeight()
    let headImgUrl = wx.getStorageSync('headImgUrl')
    let companyId = wx.getStorageSync('fgsId')
    let corpName = wx.getStorageSync('fgsName')
    // if (wx.getStorageSync('companyList')) {
    //   this.setData({
    //     cropOptions: wx.getStorageSync('companyList')
    //   })
    // } else {
    //   this.getCompanyList()
    // }

    this.setData({
      userType: wx.getStorageSync('userType'),
      userId: wx.getStorageSync('userId'),
      headImgUrl,
      companyId,
      corpName,
      tabs: this.data.allTabs
    })
    this.setData({ postList: [], leftList: [], rightList: [] })
    // await this.getPostList(1, true)
  },

  onShow: async function () {
    let selectCompanyId = wx.getStorageSync('selectCompanyId')
    let selectCompanyName = wx.getStorageSync('selectCompanyName').replace('分公司', '')
    let companyId = wx.getStorageSync('fgsId')
    let corpName = wx.getStorageSync('fgsName').replace('分公司', '')
    this.setData({
      companyId: selectCompanyId ? selectCompanyId : companyId,
      corpName: selectCompanyName ? selectCompanyName : corpName
    })
    this.getFineList(1)// 发现列表
    this.findByCode()
    this.getActivityList()// 社区活动
    // this.getLabel()// 获取标签
    // this.issueJurisdiction() // 查询是否有发帖权限
    this.addAndEdit() //manager==1 并且是业务员
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1, //选中效果 当前tabBar页面在list中对应的下标，
        currentText: '社区',
        list: app.globalData.list
      })
    }
    // const communityCurrent = wx.getStorageSync('communityCurrent')
    this.setData({ postList: [], leftList: [], rightList: [] })
    // await this.getPostList(1, true)


    // this.setData({ postList: [], leftList: [], rightList: []})
    // if(communityCurrent){
    //     await this.getPostList(communityCurrent)
    // }else{
    // await this.getPostList(1, true)
    // }
    // this.getActivity() //活动列表  社区顶部轮播图

    const userType = wx.getStorageSync('userType')
    const active = userType == 2 ? '玩转太享趣' : '推荐'
    this.setData({ active })
  },
  // 点击频道，推荐或者本地
  selectactivitytab (e) {
    console.log(e);
    let that = this
    var tabId = e.currentTarget.dataset.id
    that.setData({
      page: 1,
      tabId,
      keyword: '',
      subsumeId: 0,
      fineList: []
    })
    if (tabId == 1) {
      that.setData({
        showTab: true
      })
    }
    this.getFineList(1)
  },
  // 点击标签
  clickTab (e) {
    console.log(e);
    const active = e.currentTarget.dataset.name
    this.setData({
      active,
      subsumeId: 0,
      page: 1,
    })
    this.getFineList(1)
  },
  // 显示标签
  showTabs () {
    this.setData({
      show: true
    })
  },
  hiddenTabs () {
    this.setData({
      show: false
    })
  },
  showCompanys () {
    wx.navigateTo({
      url: `/pages/fuseSpecial/selectCompany/selectCompany?companyName=${this.data.corpName}&companyId=${this.data.companyId}`,
    })
  },
  sieve (e) {
    console.log(e);
    let item = e.currentTarget.dataset.item
    this.setData({
      subsumeId: item.id,
      fineList: [],
      page: 1,
    })
    if (item.id != 0) {
      this.setData({
        order: item.params,
        isDisLogo: true
      })
    }
    this.getFineList(1)
  },
  // 弹出框蒙层截断touchmove事件
  preventTouchMove: function () {
    return
  },
  focus () {
    this.setData({
      // showTab: false,
      // fineList: [],
      showSelectCompany: false
    })
  },
  // 标签排行前三
  labelTopThree () {
    API.categoryTop().then(res => {
      if (res.data.code == 200) {
        let topThreeList = res.data.data
        if (topThreeList.length) {
          const fineList = this.data.fineList.map(item => {
            const data = topThreeList.find(item1 => item1.id == item.id)
            if (data) item.ranking = `${data.category}TOP${data.point_num}`
            return item
          })
          this.setData({ fineList })
        }
      } else {
        app.showToast(res.data.message)
      }
    })
  },
  addAndEdit () {
    let userType = wx.getStorageSync('userType')
    let manager = wx.getStorageSync('manager')
    if (manager == 1 && userType == 3) {
      let companyId = wx.getStorageSync('companyId')
      let salesCode = wx.getStorageSync('salesCode')
      let saleName = wx.getStorageSync('saleName')
      let data = {
        "companyId": companyId,
        "createTime": Date.parse(new Date()),
        "name": saleName,
        "salesCode": salesCode,
        "salesStatus": 1
      }
      API.addAndEdit(data).then(res => { })  // 创造一条数据
    }
  },
  // 查询是否有发帖权限
  issueJurisdiction () {
    API.addCommunityLimit().then(res => {
      console.log(res.data, '发帖权限');
      if (res.data.code === 200) {
        if (res.data.data) {
          this.setData({ isIssueJurisdiction: true })
        }
      }
    })
  },
  //  获取标签
  // getLabel(){
  //   API.getLabel().then(res => {
  //     if (res.data.code == 200) {
  //       if(!res.data.data.length) return
  //       let labelList = res.data.data.map(item=>{
  //         item = {
  //           name:item,
  //           check:false
  //         }
  //         return item
  //       })
  //       this.setData({labelList})
  //     }
  //   })
  // },
  // 单个标签选中
  // handleLabelCheck(e){
  //   let index = e.currentTarget.dataset.index
  //   let labelList = this.data.labelList.map((item,indexs) => {
  //     if(indexs == index){
  //       item.check = !item.check
  //     }
  //     return item
  //   })
  //   this.setData({
  //     labelList,
  //     checkAllLabel:false
  //   })
  // },
  // 标签全选
  // handleCheckAllLabel(){
  //   if(!this.data.checkAllLabel){
  //     let labelList = this.data.labelList.map((item,indexs) => {
  //       item.check = false
  //       return item
  //     })
  //     this.setData({labelList})
  //   }
  //   this.setData({
  //     checkAllLabel : !this.data.checkAllLabel
  //   })
  // },
  // 社区标签活动
  getActivityList () {
    API.activityList().then(res => {
      if (res.data.code == 200) {
        console.log("社区标签", res.data.data)
        // let findShowObj = res.data.data.findIndex(item => item.showHomePage === 1)
        let data = res.data.data.map(item => {
          item.sortTime = item.updateTime.replace(new RegExp(/-/gm), "/") //适配苹果时间格式
          item.sortTime = Math.round(new Date(item.sortTime) / 1000)
          return item
        })
        data = data.sort((x, y) => y.sortTime - x.sortTime)
        let newArray = data.slice(0, 3)
        newArray.map((item) => {
          if (item.title.length > 8) {
            item.title = item.title.slice(0, 8) + '...'
          }
        })
        if (data.length > 3) {
          this.setData({
            collection: newArray,
          })
        } else {
          this.setData({
            collection: data
          })
        }
        // if (findShowObj != -1) {  //代表有在首页显示的
        // let count = 0
        // for (const item of res.data.data) {
        //   if (item.showHomePage === 1) count++
        // }
        // if (count > 1) { //代表有多个显示在首页 比较时间排序
        //   let data = res.data.data.map(item => {
        //     item.sortTime = item.updateTime.replace(new RegExp(/-/gm), "/") //适配苹果时间格式
        //     item.sortTime = Math.round(new Date(item.sortTime) / 1000)
        //     return item
        //   })
        //   data = data.sort((x, y) => y.sortTime - x.sortTime)
        //   this.setData({
        //     currentActivity: data[0],
        //     invitationLabelList: data,
        //     collection:data.slice(0,3),
        //     isShowFind: true
        //   })
        // } else {
        //   let currentActivity = res.data.data[findShowObj]
        //   this.setData({ 
        //     currentActivity,
        //     isShowFind: true
        //    })
        // }

      } else {
        wx.showToast({
          title: res.data.message,
          icon: 'none',
          duration: 2000
        })
      }
    })
  },
  // 列表数据
  getFineList (page) {
    wx.showLoading({ title: '加载中' })
    let data = {
      current: page || this.data.page,
      pageSize: this.data.size
    }
    if (this.data.isDisLogo && this.data.subsumeId != 0) {
      data.disLogo = this.data.order - 0
    }
    if (this.data.keyword) {
      data.title = this.data.keyword
    }
    if (this.data.tabId == 2) {
      data.companyId = this.data.companyId
      console.log(data.companyId);
    }
    if (this.data.tabId == 0) {
      // 频道
      data.type = 1
      data.activityId = 0
    }
    if (this.data.active != '推荐' && this.data.tabId == 1) {
      // 推荐下的标签筛选
      data.categoryList = this.data.active
    }
    console.log(data, '参数');
    API.getPostList(data).then(res => {
      wx.hideLoading()
      if (res.data.code == 200) {
        console.log("发现社区", res.data.data)
        let records = res.data.data.page.records
        let newPointStatus = res.data.data.newPointStatus
        this.setData({
          newPointStatus
        })
        if (records.length > 0) {
          let newFineList = records.map(item => {
            if (item && item.companyHomePageConfig && item.companyHomePageConfig.name && item.companyHomePageConfig.name.length === 2) {
              item.nameLength = 2
            } else {
              item.nameLength = 3
            }
            return item
          })
          if (page === 1) {
            this.setData({
              fineList: newFineList,
              total: res.data.data.page.total * 1
            })
          } else {
            let fineList = [...this.data.fineList, ...newFineList]
            // fineList.push(...newFineList)
            // console.log( [...this.data.fineList, ...newFineList],fineList.length);

            this.setData({
              fineList,
              total: res.data.data.page.total * 1
            })
          }
          this.labelTopThree()///标签排名
          // this.data.isMore = this.data.fineList.length === res.data.data.total
        }
        if (records.length == 0 && page == 1) {
          this.setData({
            fineList: records,
            total: res.data.data.page.total * 1
          })
        }
      } else {
        // this.data.isLoading = false
        wx.showToast({
          title: res.data.message,
          icon: 'none',
          duration: 2000
        })
      }
    })
  },

  // 去分公司详情  1业务员 0分公司
  toCompanyDetail (e) {
    let item = e.currentTarget.dataset.item
    if (item.salesCommunity == 0) {
      item.nameLength = item.companyHomePageConfig && item.companyHomePageConfig.name && item.companyHomePageConfig.name.length == 2 ? 2 : 3
    }
    let companyDetail = encodeURIComponent(JSON.stringify(item))
    wx.navigateTo({
      url: '/pages/subpages/companyDetails/companyDetails?companyDetail=' + companyDetail,
    });
  },
  // 去合集列表
  collectionList () {
    wx.navigateTo({
      url: `/pages/subpages/eventSquare/eventSquare`,
    });
  },
  // 去帖子详情
  toInvitationDetail (e) {
    let item = e.currentTarget.dataset.item
    console.log(item);
    let id = item.id

    API.communitytap({ id }).then(res => {
      console.log("浏览量+1")
    })
    // isCommon 普通帖子进去1
    if (this.data.tabId == 0) {  //合集帖子
      wx.navigateTo({
        url: `/pages/subpages/postDetail/postDetail?id=${id}`,
      });
    } else {  // 普通帖子
      wx.navigateTo({
        url: `/pages/subpages/postDetail/postDetail?id=${id}&isCommon=1`,
      });
    }
  },
  getHeight () {
    let systemInfo = wx.getSystemInfoSync()
    let menuButton = wx.getMenuButtonBoundingClientRect()
    let menuHeight = menuButton.height
    let menuRight = systemInfo.screenWidth - menuButton.right
    let menuBotton = menuButton.top - systemInfo.statusBarHeight
    let navBarHeight = (menuButton.top - systemInfo.statusBarHeight) * 2 + menuHeight + systemInfo.statusBarHeight
    const navBarData = {
      navBarHeight,
      menuRight,
      menuBotton,
      menuHeight
    }
    this.setData({ navBarData })
  },
  // 跳转banner详情
  bannerDetails (e) {
    if (e.currentTarget.dataset.id == '') return
    wx.navigateTo({
      url: `/pages/subpages/invitationDetails/invitationDetails?id=${e.currentTarget.dataset.id}`,
    });
  },
  goDetail (e) {
    console.log(e)
    let id = e.currentTarget.dataset.id
    let voteEndTime = e.currentTarget.dataset.voteendtime
    wx.navigateTo({
      url: `/pages/subpages/eventSquareDetail/eventSquareDetail?id=${id}&voteEndTime=${voteEndTime}`,
    });
  },
  // 获取机构
  // getCompanyList () {
  //   API.getCompanyList().then(res => {
  //     console.log(res.data)
  //     const { code, data, message } = res.data
  //     if (code === 200) {
  //       data.map(element => {
  //         element.check = false
  //       });
  //       wx.setStorageSync('companyList', data)
  //       this.setData({
  //         cropOptions: data
  //       })
  //     } else {
  //       wx.showToast({
  //         title: message,
  //         icon: 'none',
  //         duration: 1500,
  //         mask: false,
  //       });
  //     }
  //   }).catch(err => {
  //     console.log(err, 'err')
  //     wx.showToast({
  //       title: '请求超时，请稍后再试',
  //       icon: 'none',
  //       duration: 1500,
  //     })
  //   })
  // },
  handleScrollTop () {
    if (wx.getStorageSync('communityTop') > 0) {
      this.setData({ isScroll: true, scrollTop: wx.getStorageSync('communityTop') })
    }
  },
  async isLeft (myPostList, isFirst) {
    const { leftList, rightList } = this.data
    const myLeftList = isFirst ? [] : leftList
    const myRightList = isFirst ? [] : rightList
    if (isFirst) {
      rightHeight = 0, leftHeight = 0
      leftnum = -1, rightnum = -1, jia = -1
    }
    // query = wx.createSelectorQuery();
    for (const [index, item] of myPostList.entries()) {
      // leftHeight <= rightHeight ? leftnum++ : rightnum++;
      // leftHeight <= rightHeight ? jia = 1 : jia = 0;
      // leftHeight <= rightHeight ? myLeftList.push(item) : myRightList.push(item);
      index % 2 == 0 ? leftnum++ : rightnum++;
      index % 2 == 0 ? jia = 1 : jia = 0;
      index % 2 == 0 ? myLeftList.push(item) : myRightList.push(item);
      // await this.getBoxHeight(myLeftList, myRightList);
      this.setData({ leftList: myLeftList, rightList: myRightList })
    }
  },
  async getBoxHeight (leftList, rightList) { //获取左右两边高度
    query = wx.createSelectorQuery();
    return new Promise((resolve, reject) => {
      this.setData({ leftList, rightList }, () => {
        if (jia == 1) {
          query.select('#left').boundingClientRect()
          query.exec((res) => {
            leftHeight = res[0].height,
              resolve();
          });
        } else {
          query.select('#right').boundingClientRect();
          query.exec((res) => {
            rightHeight = res[0].height
            resolve();
          });
        }
      });
    })
  },
  // 搜索
  toSearch () {
    // wx.navigateTo({
    //   url: '/pages/subpages/communitySearch/communitySearch',
    // });
    let keyword = this.data.keyword
    if (keyword.trim().length <= 0) {
      wx.showToast({
        title: '请输入关键词再搜索',
        icon: 'none',
        duration: 1500,
        mask: false,
      });

    }
    this.setData({ isClearText: true, page: 1 })
    this.getFineList(1)
  },
  changeInput (e) {
    this.setData({ keyword: e.detail })
    if (e.detail == '') {
      this.setData({
        isClearText: false
      })
    }

  },
  //   清空关键词
  clearKeyWord () {
    this.setData({
      isClearText: false,
      keyword: '',
      showTab: true,
      page: 1,
      fineList: [],
    })
    this.getFineList(1)
  },
  blurClick () {
    let keyword = this.data.keyword
    console.log('keyword--✨🍎', keyword)
    // wx.showToast({
    //   title: '请输入关键词再搜索',
    //   icon: 'none',
    //   duration: 1500,
    //   mask: false,
    // });

    this.setData({ page: 1, showSelectCompany: true, showTab: false, fineList: [] })

    if (keyword.trim().length <= 0) {
      this.setData({ showTab: true })
    }
    this.getFineList(1)

    // if(this.data.keyword == '' &&this.data.isClearText==true){
    //   this.setData({
    //     showTab:true,
    //   })
    // }
  },
  showFilter () {
    this.setData({ popupShow: true })
    this.selectComponent('#item').toggle(true)
  },
  onClick (event) {
    const { item } = event.currentTarget.dataset
    this.setData({ item2_title: item.text, order: item.value, isDisLogo: true }) //isDisLogo 默认不传,只有筛选的时候才传
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload () {
    console.log('页面卸载')
    wx.removeStorage({
      key: 'selectCompanyId',
    })
    wx.removeStorage({
      key: 'selectCompanyName',
    })

  },
  // 筛选关闭
  // async onClose () {
  //   this.selectComponent('#item').toggle(false)
  //   this.selectComponent('#item2').toggle(false)
  //   let checkFlag = this.data.cropOptions.some(item => item.check)
  //   let labelList = this.data.labelList.some(item => item.check)
  //   if (!checkFlag && !this.data.checkAll) {
  //     wx.showToast({
  //       title: '请选择筛选机构',
  //       icon: 'none',
  //       duration: 1500,
  //       mask: false,
  //     });
  //     return
  //   }
  //   if(!labelList && !this.data.checkAllLabel){
  //     wx.showToast({
  //       title: '请选择筛选标签',
  //       icon: 'none',
  //       duration: 1500
  //     });
  //     return
  //   }
  //   let selectLabelList = []
  //   if(!this.data.checkAllLabel){
  //     selectLabelList = this.data.labelList.filter(item => item.check === true ).map(item => item.name)
  //   }
  //   let selIds = []
  //   if (!this.data.checkAll) {
  //     this.data.cropOptions.map(element => {
  //       if (element.check) selIds.push(element.id)
  //     })
  //   }
  //   this.setData({
  //     selectLabelList,
  //     popupShow: false,
  //     checks: selIds
  //   })

  //   if (this.data.isOther) {
  //     this.setData({
  //       optionInvitationList:[],
  //       isMoreOption:false,
  //       optionPage:1
  //     })
  //     this.optionInvitation(1,'clearInfo')// 获取其他活动
  //   } else {
  //     this.setData({
  //       fineList:[],
  //       isMore:false,
  //       page:1
  //     })
  //     this.getFineList(1)// 获取社区
  //   }
  // },
  //   点赞
  handleLike (e) {
    if (!wx.getStorageSync('salesCode')) {
      this.showTips('请绑定业务员工号')
      return
    }
    const { like, side, index, id, pointnum, title } = e.currentTarget.dataset
    let data = {
      communityId: id,
      status: like ? 0 : 1, //1已点赞，0取消点赞
      userId: this.data.userId
    }
    API.changeLike(data).then(res => {
      if (res.data.code == 200) {
        this.showTips('操作成功')
        if (this.data.isOther) {
          let changeIsPoint = "optionInvitationList[" + index + "].isPoint"
          let changePointNum = "optionInvitationList[" + index + "].pointNum"
          this.setData({
            [changeIsPoint]: !like,
            [changePointNum]: like ? pointnum - 1 : pointnum + 1,
          })
        } else {
          let changeIsPoint = "fineList[" + index + "].isPoint"
          let changePointNum = "fineList[" + index + "].pointNum"
          this.setData({
            [changeIsPoint]: !like,
            [changePointNum]: like ? pointnum - 1 : pointnum + 1,
          })
        }
        // this.setData({
        //     [`postDeatil[${TopIndex}].isPoint`]: !like,
        //     [`${side}[${index}].isPoint`]: !like,
        //     [`postDeatil[${TopIndex}].pointNum`]: !like ? pointnum + 1 : pointnum - 1,
        //     [`${side}[${index}].pointNum`]: !like ? pointnum + 1 : pointnum - 1,
        // })
        // const myNum = this.data.postDeatil[TopIndex].pointNum
        // this.setData({
        //     [`postDeatil[${TopIndex}].pointNumShow`]: http.numFormatW(myNum),
        //     [`${side}[${index}].pointNumShow`]: http.numFormatW(myNum)
        // })
        // if (!like) {
        //     this.onBuriedPoint('likePost', title, id)
        // }
        // console.log('点赞状态', this.data.postDeatil[TopIndex].isPoint)
      } else {
        this.showTips(res.data.message)
      }
    })
  },
  showTips (title, duration = 2000) {
    wx.showToast({
      title,
      duration,
      icon: "none",
      mask: true
    })
  },
  // 埋点事件
  onBuriedPoint (name, title, id) {
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let time = new Date().toLocaleString()
    app.sensors.track(name, {
      title: title,
      postId: id,
      personScan: nickName,
      scanTime: time,
      userId: userId
    });
  },
  goSelfCommuinty () {
    wx.navigateTo({
      url: `/pages/subpages/selfCommunity/selfCommunity`,
    })

  },

  async findByCode () {
    try {
      const res = await API.getfindByCode({ code: 'wz_p13_key' })
      const { code, data, message } = res.data
      if (code !== 200) return
      const { account } = wx.getStorageSync('getInfoByIdData')

      const p13List = data ? data.split(',') : []
      const p13ListUpper = p13List.map(item => item.toUpperCase())
      if (account) {
        this.setData({
          isJurisdiction: p13ListUpper.includes(account.toUpperCase())
        })
      }
    } catch (error) {

    }
    // this.h5Code = res.data.data
  },

  // 跳转编辑页
  toEdit () {
    if (!wx.getStorageSync('salesCode')) {
      wx.showToast({
        title: '请绑定业务员工号',
        icon: 'none',
        duration: 1500,
      });
      return
    }
    if (this.data.tabId == 0) {  //康养秀
      wx.navigateTo({
        url: `/pages/subpages/postEdit/postEdit?videoSize=300&isApply=1&id=${this.data.currentActivity.id}`,
      })
    } else {   //发现

      wx.navigateTo({
        url: `/pages/subpages/postEdit/postEdit?videoSize=60&label=${this.data.active}`,
      })
    }
  },
  // 跳转详情页
  // toDetail (e) {
  //   const { id } = e.currentTarget.dataset
  //   wx.navigateTo({
  //     url: `/pages/subpages/postDetail/postDetail?id=${id}&communityEnter=1`,
  //   })
  // },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    this.setData({ isDisLogo: false })
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },
  onPageScroll (e) {
    wx.setStorageSync('communityTop', e.scrollTop)
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: async function () {
    if (!this.data.loading) {
      // this.getFineList(1)// 获取社区
      // 处理完成后，终止下拉刷新
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: async function () {
    console.log('触底');
    if (this.data.fineList.length >= this.data.total) return
    this.setData({ page: this.data.page + 1 })
    this.getFineList()// 获取社区


  }
})