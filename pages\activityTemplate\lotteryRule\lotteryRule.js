const app = getApp()
const API = require('../../../api/request')
Page({

  /**
   * 页面的初始数据
   */
  data:{
    list:[],
    rejectReason:'',
    backgroundImg:'',
    height:'',
    backgroundImgHeight:'',
    prizeList:[],
    activityId:'',
    start:'',
    end:'',
    qtnIdType:true,
    audit:'', //活动审核判断
    showReject:false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options)
    this.setData({
      activityId:options.id,
      qtnIdType:options.qtnIdType,
      audit:options.audit || '',
      drawMode:options.mode
    })
    console.log(this.data.qtnIdType)
    this.getBasicInfo()
   
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    let list = wx.getStorageSync('lotteryRule')
    const maxElementsFromArray = (array, number = 1) => [...array].sort((x, y) => y.style.top -x.style.top).slice(0, number)
    console.log(maxElementsFromArray(list))
    let selectDOM = maxElementsFromArray(list)[0].type === 'rulePrize' ? 'reward' : maxElementsFromArray(list)[0].type === 'ruleTime' ? 'rule_time' : 'rule_text'
    console.log(selectDOM)
    setTimeout(() => {
      let query = wx.createSelectorQuery();
      query.select(`.${selectDOM}`).boundingClientRect(rect=>{
          let clientHeight = rect.height
          let clientWidth = rect.width
          let ratio = 750 / clientWidth
          let height = clientHeight * ratio
          let top = rect.top * ratio
          let totalHeight = height + top
          this.setData({
            height:totalHeight.toFixed(0),
            backgroundImgHeight:totalHeight.toFixed(0)<1334?1334:totalHeight.toFixed(0)
          })
          console.log(height,top);
          }).exec();
      }, 500)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.setData({
      height:1236+400+158
    })
  },
  //获取基本信息
  async getBasicInfo(){
    let that = this
    let params = {
      id:this.data.activityId,
      drawMode:this.data.drawMode
    }
    let response = null
    if(that.data.audit == 1){
      response = await API.auditDetail({id:this.data.activityId})
    }else {
      response = await API.getNewDrawInfo(params)
    }
    let activityPageConfig = JSON.parse(response.data.data.activityPageConfig)
    console.log(activityPageConfig)
    if(response.data.code === 200){
      let rule = activityPageConfig.find((item) => item.title === '规则介绍')
      let backgroundImg = rule.componentData.find((item) => item.type === 'backgroundImg')
      var reg=new RegExp('TXQImgPath/','ig');
      let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
      let drawPrizesData  =  JSON.stringify(response.data.data.drawConfig.drawPrizes)
      let prizeData = JSON.parse(drawPrizesData.replace(reg,dynamicDomainName))
      // let prizeData = response.data.data.drawConfig.drawPrizes 
      let rewardImg = [] //活动奖品列表
       for(var i=0;i<prizeData.length;i++){
        if(prizeData[i].rightsSrc != 'NOTHING'){
          rewardImg.push(prizeData[i])
        }

      }
      
      wx.setStorageSync('rewardImg', rewardImg)
      that.setData({
        backgroundImg:backgroundImg.propValue.url.replace(reg,dynamicDomainName),
        list:rule.componentData,
        prizeList:rewardImg,
        content:response.data.data.contents,
        start:response.data.data.startTime,
        end:response.data.data.endTime,
        auditStatus:response.data.data.auditStatus,
        financeStatus:response.data.data.financeStatus,
        qtnIdType: response.data.data.flagConnect == 0 ? 'false':'true'
      })

    } else {
      wx.showToast({
        title: response.data.message,
        icon: 'none',
        duration: 2000
      })
    }
  },
  reject() {
    console.log('触发了')
    this.setData({
      showReject:true,
      rejectReason:'',
    })
  },



  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

})