
  <view class="rule_main">
    <block wx:for="{{activityPageConfig.componentData}}" wx:key="index">
      <image wx:if="{{item.type == 'backgroundImg'}}" mode="widthFix" src="{{item.propValue.url}}" style="{{item.styleStr}}" class="imageStyle">
      </image>
      <view class="rule_time" wx:if="{{item.type == 'ruleTime'}}" style="{{item.styleStr}}">
        <view class="rule_desc_title" style="margin-bottom: 10rpx;">活动时间 :
        <view class="line"></view>
        </view> 
        <view style="color: #17204D;">{{item.propValue[0]}} - {{item.propValue[1]}}</view>
      </view>
      <view class="rule_text" wx:if="{{item.type == 'rule'}}"  style="{{item.styleStr}}">
        <view class="rule_desc_title" style="margin-top: 8rpx;">活动规则 :<view class="line"></view></view>
        <view class="jj_content" wx:if="{{item.propValue !== ''}}">
          <rich-text nodes="{{item.propValue}}"  class="ql-editor" style="color: #17204D;"></rich-text>
        </view>
      </view>
    </block>
  </view>

<!-- 活动审核显示底部操作按钮 -->
<audit wx:if="{{audit}}" bind:reject="reject" activityId="{{activityId}}" activityType="7"></audit>
<!-- 活动审核显示底部操作按钮 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" activityType="7" audit="{{auditStatus}}" financeStatus="{{financeStatus}}" rejectReason="{{rejectReason}}" linkTimes='2'></reject-reason>
