// pages/activityTemplate/lotteryReward.js
const API = require('../../../api/request')
import { formatDate2 } from '../../../utils/util';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    attributes:[],
    isInfo:false,//弹框是否显示录入联系方式
    isShow:false,
    rewardsObj:{},//中奖页面配置
    nothing:{},//未中奖配置
    acceptAward:{},//
    prizeRecordList:[],
    page:'我的奖品',
    dialogData:{},
    prizeObj:{},
    pageSize:10,
    current:1,
    total:null,
    scrollMargin: 0,
    scrollHeight:0,
    acceptAwardImg: {},
    acceptAwardBtn: null,
    activityTemplateType: null,
    awardSuccessComs: {},
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options);
    let id = options.id
    this.setData({
      id,
      drawMode: options.mode,
      qtnId: options.qtnId,
      productId: options.productId || '',
      acceptAwardImg: wx.getStorageSync('acceptAwardImg'),
      activityTemplateType: wx.getStorageSync('activityTemplateType'),
      msg:options.msg?options.msg:''//企微领奖留资字段
    })
    this.getBasicInfo(options.id)
    this.getList(options.id)
  },
  onClose(){
    this.setData({isInfo: false})
  },
  async getList(id){
    let params = {
      id:id,
      current:this.data.current,
      pageSize:this.data.pageSize,
      type:1
    }
    let res = await API.getPageRecordsList(params)
    console.log(res)
    if(res.data.code === 200){
      res.data.data.records.forEach((item)=>{
        item.createTime = item.createTime //formatDate2( item.createTime)
      })
      var reg=new RegExp('TXQImgPath/','ig');
      let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
      let activityData  = JSON.stringify(res.data.data.records)
      let activityPageData = activityData&&activityData.replace(reg,dynamicDomainName)
      let records = JSON.parse(activityPageData)
      this.setData({
        prizeRecordList: [...this.data.prizeRecordList, ...records], 
        total: res.data.data.total
      })
    }else{
      wx.showToast({
        title: res.data.message,
        icon: 'none',
        duration: 3000
      })
    }
    console.log(res)
  },
  async getBasicInfo(id){
    let that = this
    let params = {
      id:id,
      drawMode: this.data.drawMode,//'DRAW'
      productId: this.data.productId, //投票作品id
      qtnId: this.data.qtnId
    }
    const response = await API.getNewDrawInfo(params)
    console.log(response.data.data)
    var reg=new RegExp('TXQImgPath/','ig');
    let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
    let activityData  =  response.data.data
    let activityPageData = activityData.activityPageConfig&&activityData.activityPageConfig.replace(reg,dynamicDomainName)
    let activityPageConfig = JSON.parse(activityPageData)
    console.log(activityPageConfig)
    if(response.data.code === 200){
      let nothing = activityPageConfig.find((item) => item.title === '未中奖弹窗')
      let rewardsObj = activityPageConfig.find((item) => item.title === '中奖弹窗')
      let acceptAward = activityPageConfig.find((item) => item.title === '领奖弹窗')
      that.setData({
        rewardsObj,//中奖页面配置
        nothing,//未中奖配置
        linkUrl:rewardsObj.componentData.find(item => item.type == 'button'),
        acceptAward,//领奖页面配置
        acceptAwardBtn: acceptAward.componentData.find(item => item.type == 'button'),
        attributes:response.data.data.attributes,
        receiveType:response.data.data.drawConfig.receiveType
      })
    }
  },
  onReady: function () {
    this.dialog = this.selectComponent("#dialog");
  },
   async showDialog(e){
    console.log(e.currentTarget.dataset.item)

    let item = e.currentTarget.dataset.item
    let index = e.currentTarget.dataset.index
    if(item.receiverStatus == 1) {
      return
    }
    // const receiveType = this.data.receiveType
    // console.log(699, receiveType)
    // if(receiveType){
    //   const activityId = this.data.id
    //   const backUrl = encodeURIComponent(`#/results?activityType=DRAW&activityId=${activityId}`)
    //   const res = await API.signInGetReceiveUrl({activityId, backUrl, receiveType, type: "DRAW"})
    //   // console.log(380, res.data)
    //   if(res.data.code == 200){
    //     this.getUrlRes = res.data.data
    //     this.setData({isFirst: this.getUrlRes.isNot === 'n', getUrlRes: res.data.data})
    //   }else{
    //     wx.showToast({
    //       title: res.data.message,
    //       duration: 2000,
    //       icon: "none",
    //     })
    //   }
    // }
    if(item.flagReceiveInfo == 1){
      this.setData({
        index,
        isShow:false,
        isInfo:true,
        recordId:item.id
      })
      this.setData({
        acceptDialogData:this.data.acceptAward
      })
    }else{
      let json ={
        id:item.id,
      }
      let res = await API.saveRecord(json)
      if(res.data.code == 200){
       
        let prizeRecordList = this.data.prizeRecordList
        prizeRecordList[index].receiverStatus = 1
        this.setData({
          isShow:false,
          prizeRecordList
        })
        if (this.data.linkUrl&&this.data.linkUrl.setting &&this.data.linkUrl.setting.jumpUrl&&this.data.activityTemplateType===7) {
          // 企微砸金蛋
          const src = encodeURIComponent(this.data.linkUrl.setting.jumpUrl)
          wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?src=${src}&mode=${this.data.drawMode}` })
        }
        if(this.data.isFirst){
          // 今日未跳转第三方
          const src = encodeURIComponent(this.getUrlRes.returnUrl)
          wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${this.data.id}&src=${src}` })
        }else{
          // 跳转第三方则不提示用户领取成功
          wx.showToast({title: "领取成功",icon: "none",duration: 1000})
        } 
      }else{
        wx.showToast({
          title: `${res.data.message}!`,
          icon: 'none',
          duration: 2000
        })
        if (this.data.linkUrl&&this.data.linkUrl.setting &&this.data.linkUrl.setting.jumpUrl&&this.data.activityTemplateType===7) {
          const src = encodeURIComponent(this.data.linkUrl.setting.jumpUrl)
          wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?src=${src}&mode=${this.data.drawMode}` })
        }
      }
    }
  },
   //取消事件
   _cancelEvent(){
    console.log('你点击了关闭');
    this.setData({
      isInfo:false,
      isShow:false
    })

  },
  //确认事件
  async _confirmEvent(){
    console.log('你点击了领取');
    if(this.data.item.flagReceiveInfo == 1){
      this.setData({
        isShow:false,
        isInfo:true
      })
      this.setData({
        acceptDialogData:this.data.acceptAward
      })
    }else{
      // 抽奖活动如有配置【奖励领取条件】，则所有用户每日第一次中奖时，则点击领奖弹窗中的“去领取”按钮，直接跳转进入第三方对应ID的文章/问卷页面；同时自动做奖励领取操作（记录领取奖励名称、领取时间）
      let json ={
        id:this.data.item.id,
      }
      let res = await API.saveRecord(json)
      console.log(res)
      if(res.data.code == 200){
        if(this.data.isFirst){
          // 今日未跳转第三方
          const src = encodeURIComponent(this.getUrlRes.returnUrl)
          wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${this.data.id}&src=${src}` })
        }else{
          // 跳转第三方则不提示用户领取成功
          wx.showToast({title: "领取成功",icon: "none",duration: 1000})
        }
        let prizeRecordList = this.data.prizeRecordList
        prizeRecordList[this.data.index].receiverStatus = 1
        this.setData({
          isShow:false,
          prizeRecordList
        })
       
      }else{
        wx.showToast({
          title: `${res.data.message}!`,
          icon: 'none',
          duration: 2000
        })
      }
    }
  },
    //确认保存事件
    _saveEvent(e){
      // console.log('保存',e.detail);
      let that = this
      let prizeRecordList = this.data.prizeRecordList
      prizeRecordList[this.data.index].receiverStatus = 1
      wx.showToast({
        title: `领取成功!`,
        icon: 'none',
        duration: 1000
      })
      this.setData({
        prizeRecordList,
        isInfo:false
      })
      setTimeout(function () {
        let flag = that.data.acceptAward&&that.data.acceptAward.componentData.find((item)=> item.type == 'button')
        if(flag&&flag.setting.jumpUrl&&that.data.activityTemplateType===7){
          let src = encodeURIComponent(flag.setting.jumpUrl)
          wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${that.data.id}&src=${src}&mode=${that.data.drawMode}` })
        }
      },300)

    },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  onReachBottom: function () {
    console.log(this.data.total)
    if (this.data.prizeRecordList.length >= this.data.total) return
    this.setData({ current: this.data.current += 1 })
    this.getList(this.data.id)

  },

  
})