<import src="../templates/detailFragment/detailFragment" />
<!-- 加载动画 -->
<loading-animation wx:if="{{myLoadAnimation}}"></loading-animation>
<!-- 加载动画 -->
<!-- 没有同意协议显示弹窗 -->
<privacy-popop showPrivacy="{{disagree}}" bind:agree="agreePrivacy" jumpType="{{jumpType}}"></privacy-popop>
<!-- 没有同意协议显示弹窗 -->
<!-- 刷新弹窗 -->
<view wx:if="{{reflashToast}}">
  <view class="mymask">
    <view class="mask_content">
      <image class="reflash_img" src="https://txqmp.cpic.com.cn/uploads/img/refresh.png" lazy-load="false" />
      <view class="reflash_text_wrap">
        <view>啊哦~</view>
        <view>大伙的热情过于高涨，</view>
        <view>请稍等片刻~</view>
      </view>
      <view class="reflash_btn_wrap">
        <button class="reflash_btn" disabled="{{!isReflash}}" bindtap="reGetActivity">
          刷新{{reflashBtnContent}}
        </button>
      </view>
    </view>
  </view>
  <view class="mask2" catchtouchmove="preventTouchMove"></view>
</view>
<!-- 刷新弹窗 -->
<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close"></login-box>
<!-- 登陆者身份选择 -->
<!-- 引发授权的弹窗 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler" class="userinfo-user" hidden="{{InfoShow == false}}">
      点击进入
    </button>
  </view>
</view>
<!-- 引发授权的弹窗 -->
<!-- 活动详情内容 -->
<scroll-view class="page_main" scroll-y="true" style="background-image:url({{backgroundImg}});height: {{(audit == 1||checkDetail==1) ? '100vh':showPoster?'100vh':'calc(100vh - 188rpx)'}}">
  <block wx:for="{{componentData}}" wx:key="index">
    <block wx:if="{{item.type === 'VSwiper'}}">
      <custom-carousel title="{{activity.title}}" bannerConfig="{{VSwiper}}" heightTop="{{heightTop}}" showHomeMenu="{{isShare|| refer=='kege'}}"></custom-carousel>
    </block>
    <block wx:if="{{item.type === 'textDisplayMode'}}">
      <view style="{{item.styleStr}}" class="activity_detail_textDisplayMode">
        <rich-text nodes="{{item.propValue}}" class="ql-editor"></rich-text>
      </view>

      <!-- <view class="rights" wx:if="{{activity.companyName}}" style="{{item.styleStr}}">
        <view class="sale_place">{{activity.companyName}}</view>
      </view> -->
    </block>
    <block wx:if="{{item.type === 'activeBackgroundImg' || item.type === 'detailBackgroundImg' || item.type === 'activeBgWhiteBox' || item.type === 'activeBgBox'}}">
      <view class="activity_detail_bg" style="{{item.styleStr}};background-image:url({{item.propValue.url}}"></view>
    </block>
    <block wx:if="{{item.type === 'cropTag'}}">
      <view class="rights" wx:if="{{activity.companyName}}" style="{{item.styleStr}}">
        <view class="sale_place">{{activity.companyName}}</view>
      </view>
    </block>
    <block wx:if="{{item.type === 'activeTitle'}}">
      <view class="activity_detail_title" style="{{item.styleStr}}">{{activity.title}}</view>
    </block>
    <!-- 多项目选择 -->
    <block wx:if="{{category == 3 && project.length>1&&!activity.oldCustomFlag}}">
      <block wx:if="{{item.type === 'commodityMain'}}" style="{{item.styleStr}}" class="commodityMain">
        <view class="activity_detail_bg" style="{{item.styleStr}};background-image:url({{item.bgStatus==1?item.imgOptions.url:''}}"></view>
      </block>
      <block wx:if="{{item.type === 'commodityTitle'}}">
        <view style="{{item.styleStr}}" class="commodityTitle">{{item.propValue}}</view>
        <view class="project_box" style="top:{{item.style.top*2+62}}rpx;left:{{item.style.left*2}}rpx;" >
        <scroll-view  scroll-x="true" style="width:85%;padding-right: 20rpx;box-sizing: border-box;">
          <view  class="project_content">
            <!-- commodityMain.style.borderColor -->
            <view wx:for="{{project}}" class="projectImgBox" style="border-color:{{selectIndex===indexs?commodityMain.style.borderColor:''}}" wx:for-index="indexs" wx:key="indexs" wx:for-item="list" >
              <image src="{{list.detailImg}}" class="project_img" bindtap="selectProgram" data-selectIndex="{{indexs}}"/>
            </view>
          </view>
        </scroll-view>
        <view class="project_title">已选择：{{project[selectIndex].title}}<view wx:if="{{activity.isCharge !== 0}}">,￥{{project[selectIndex].price}}</view></view>
          <view style="width: 102rpx;text-align: center;">共{{ project.length }}款</view>
        </view>
      </block>
    </block>
    <!-- 多项目选择 -->
    <block wx:if="{{item.type === 'detail-area'}}">
      <template is="detail_Fragment" data="{{item,activityTime}}"></template>
    </block>
    <block wx:if="{{item.type === 'stock' && category != 5}}">
      <view class="totalNum" style="{{item.styleStr}}">
        <view class="sale_free" style="{{item.optionsStyleStr}}">
          已售：
          <div class="mount" style="color:{{item.optionsStyle.timeColor}};font-size: {{item.optionsStyle.timeFontSize}};font-weight: {{item.optionsStyle.timeFontWeight}};">
            {{activity.totalPlace-activity.place}}
          </div>
        </view>
        <view class="sale_free" style="{{item.optionsStyleStr}}">
          剩余：
          <div class="mount" style="color:{{item.optionsStyle.timeColor}};font-size: {{item.optionsStyle.timeFontSize}};font-weight: {{item.optionsStyle.timeFontWeight}};">
            {{activity.place}}
          </div>
        </view>
      </view>
    </block>
    <block wx:if="{{item.type === 'poster' && activity.flagPoster == 1}}">
      <!-- <root-portal wx:if="{{activity.flagPoster == 1}}" enable="{{true}}"> -->
      <!-- 专属海报弹框 -->
      <post-popup show="{{showPoster}}" codeUrl="{{activity.qrcode}}" activityName="{{activity.title}}" btnText="{{item}}" banner="{{posterBackgroundImg.propValue.url}}" title="{{posterText.propValue}}" textStyle="{{posterText}}" bind:onClickHide="onClickHide" bind:clickDownPoster="downPoster" audit="{{audit}}"></post-popup>
      <!-- 专属海报弹框 -->
      <!-- </root-portal> -->
    </block>
    <block wx:if="{{item.type === 'applyTime'}}">
      <view class="activity_detail_time_title" style="{{item.styleStr}};color:{{item.optionsStyle.timeColor}};font-size: {{item.optionsStyle.timeFontSize}};font-weight: {{item.optionsStyle.timeFontWeight}};">
        <view style="{{item.optionsStyleStr}};border-color:{{item.optionsStyle.color}}">报名时间</view>
        {{applyStartTime}} ~ {{applyEndTime}}
      </view>
    </block>
    <block wx:if="{{item.type === 'limitText' || item.type === 'registrationPrice'}}">
      <view class="left" style="{{item.styleStr}}">
        <block wx:if="{{item.type === 'registrationPrice'}}">
          <view class='red_price' wx:if="{{(activity.price != 0 && activity.price !== null)}}">
            <text class="wordTip">{{registrationPrice.propValue}}</text>
            <text style="font-size:24rpx;color:{{registrationPrice.style.priceColor}}">¥</text>
            <text style="color:{{registrationPrice.style.priceColor}}">{{activity.price}}</text>
            <view class='bla_price' style="color:{{registrationPrice.style.originalPriceColor}}">
              ￥{{activity.originalPrice}}
            </view>
          </view>
        </block>
        <block wx:else>
          <view class='red_price' wx:if="{{activity.price == 0 || activity.price == null}}" style="{{limitTextConfig.styleStr}}">
            {{limitTextConfig.propValue}}
          </view>
        </block>
      </view>
    </block>
    <block wx:if="{{item.type === 'credit'}}">
      <view class="activity_detail_credit" style="{{item.styleStr}}" decode="{{true}}" wx:if="{{activity.tokenMax > 0}}">
        积分可抵扣{{activity.tokenMax}}元
      </view>
    </block>
    <block wx:if="{{item.type === 'countDown'}}">
      <view class="activity_time" style="{{item.styleStr}}">
        <view class="time_main" style="color:{{item.style.textColor}}">
          {{countDownConfig.textStart}}
          <span style="color:{{item.style.numberColor}}">{{timeObj.day}}</span>
          天
          <span style="color:{{item.style.numberColor}}">{{timeObj.hour}}</span>
          时
          <span style="color:{{item.style.numberColor}}">{{timeObj.points}}</span>
          分
          <span style="color:{{item.style.numberColor}}">{{timeObj.seconds}}</span>
          秒
          {{countDownConfig.textEnd}}
        </view>
      </view>
    </block>
    <!-- 高尔夫活动场次/批次选择 0-时段预约；1-批次预约；2-机构预约gaokeMain detail-area； -->
    <view wx:if="{{item.type === 'gaokeMain'}}" class="golfContent" style="{{item.styleStr}}">
      <block wx:if="{{batchFlag != 0}}">
        <view style="background-image:url({{item.batchOptions.propValue.url}}" class="golf_activity">
          <view class="golf_activity_container" wx:for="{{golfActivityArea}}" wx:for-index="indexs" wx:key="indexs" wx:for-item="list" data-id="{{list.id}}" data-list="{{list}}" data-selindex="{{index}}">
            <view class="golf_activity_title" style="color:{{item.batchOptions.style.color}};text-align:{{item.batchOptions.style.textAlign}};font-size:{{item.batchOptions.style.fontSize*2}}rpx;font-weight:{{item.batchOptions.style.fontWeight}};">
              {{list.field}}
            </view>
            <block wx:if="{{batchFlag == '1'}}">
              <view class="golf_activity_address" style="color:{{item.batchOptions.timeStyle.color}};text-align: {{item.batchOptions.timeStyle.textAlign}};font-size: {{item.batchOptions.timeStyle.fontSize*2}}rpx;font-weight: {{item.batchOptions.timeStyle.fontWeight}};">
                <view>
                  <text>开团时间:</text>
                  {{list.appointTimes.startTime}}
                </view>
                <view>
                  <text>截止时间:</text>
                  {{list.appointTimes.endTime}}
                </view>
                <view>
                  <text>剩余库存:</text>
                  {{list.place}}
                </view>
              </view>
            </block>
            <block wx:if="{{batchFlag == '2'}}">
              <view class="golf_activity_address" style="color:{{item.batchOptions.timeStyle.color}};text-align: {{item.batchOptions.timeStyle.textAlign}};font-size: {{item.batchOptions.timeStyle.fontSize*2}}rpx;font-weight: {{item.batchOptions.timeStyle.fontWeight}};">
                <view>
                  <text>剩余库存:</text>
                  {{list.place}}
                </view>
              </view>
            </block>
            <block wx:if="{{batchFlag == '1'||(batchFlag == '2' && list.buyable)}}">
              <block wx:if="{{list.selFlag}}">
                <view class="imgRight select">已选择</view>
              </block>
              <block wx:if="{{!list.selFlag && list.place!=0}}">
                <view class="imgRight unselect" style="background-image:{{item.batchOptions.btnOptions.bgStatus==1?item.batchOptions.btnOptions.imgOptions.url:item.batchOptions.btnOptions.style.backgroundColor}};color:{{item.batchOptions.btnOptions.style.color}}" data-id="{{list.id}}" data-item="{{list}}" data-selindex="{{indexs}}" bindtap="{{list.selFlag? '' : 'selectBatch'}}">
                  {{item.batchOptions.btnOptions.propValue}}
                </view>
              </block>
              <block wx:if="{{!list.selFlag && list.place==0}}">
                <view class="imgRight {{isAttentionAgainFlag?'select':'unselect'}}" catchtap="{{isAttentionAgainFlag? '' : 'wantToJoin'}}">
                  我想参加
                </view>
              </block>
            </block>
            <block wx:if="{{batchFlag == '2' &&!list.buyable}}">
              <view class="imgRight selects">不可预约</view>
            </block>
          </view>
        </view>
      </block>
      <template is="detail_Fragment" data="{{item,activityTime}}"></template>
    </view>
  </block>
</scroll-view>
<!-- 底部按钮 -->
<view wx:if="{{!checkDetail}}" class="footer {{showPoster? 'footerHide' : '' }}">
  <block wx:for="{{componentData}}" wx:key="index">
    <block wx:if="{{item.type === 'button' && item.btEvent === 'myOrder'}}">
      <view class="myOrder" style="{{item.styleStr}}left:24rpx;" bindtap="goMyorder">{{item.propValue}}</view>
    </block>
    <block wx:if="{{item.type === 'button' && item.btEvent === 'buyNow'}}">
      <view class="buybtn" style="{{item.styleStr}}" wx:if="{{category != 6 && category != 5 && activity.place != 0 }}" bindtap="{{InfoShow? '' : 'tobuy'}}">
        {{item.propValue}}
      </view>
      <view class="buybtn" style="{{item.styleStr}}" wx:if="{{category != 6 && category != 5 && activity.place == 0}}" bindtap="{{InfoShow || isAttentionAgainFlag? '' : 'wantToJoin'}}">
        我想参加
      </view>
      <view class="warnbtn" wx:if="{{sub_time1 < 0 ?true:false}}" style="color: {{warnInfo === '提醒我' ? '#000' : ''}};{{item.styleStr}}" bindtap="handleWarnMe">
        {{warnInfo}}
      </view>
      <!-- <view class="buybtn" style="{{item.styleStr}}" bindtap="{{InfoShow? '' : 'tobuy'}}">
        {{item.propValue}}
      </view> -->
    </block>
    <block wx:if="{{item.type == 'button' && item.btEvent == 'signUp'}}">
      <view class="buybtn" style="{{item.styleStr}}" wx:if="{{ category == 5 && batchFlag != '0'}}" bindtap="{{InfoShow? '' : 'tobuy'}}">
        {{item.propValue}}
      </view>
      <view class="buybtn" style="{{item.styleStr}}" wx:if="{{ category == 5 && batchFlag == '0'}}" bindtap="{{InfoShow? '' : 'jumpToGolf'}}">
        {{item.propValue}}
      </view>
    </block>
  </block>
</view>
<!-- 底部按钮 -->
<!-- 活动详情内容 -->
<!-- 缺少身份证号后完善身份信息 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{chooseArea}}"></view>
<view class='{{chooseArea ? "chooseArea" : ""}} ' wx:if="{{chooseArea}}">
  <view class="modal_icon" catchtap="closeChooseAreaModal">
    <van-icon size="44rpx" color="#D0D2DB" name="cross" />
  </view>
  <view class="title">温馨提示</view>
  <view class="desc">请完善您的身份证号信息</view>
  <view class="choose">
    <view>
      <span>*</span>
      姓名
    </view>
    <view class="chooseClick">
      <input type="text" placeholder="请输入" disabled="disabled" value="{{trueName}}" />
    </view>
  </view>
  <view class="choose">
    <view>
      <span>*</span>
      证件类型
    </view>
    <view class="chooseClick">
      <picker class="truename1s" bindchange="identityChange" range="{{identityType}}" value="{{identityIndex}}" range-key="name">
        <view class="weui-input">{{identity}}</view>
      </picker>
    </view>
  </view>
  <view class="choose">
    <view>
      <span>*</span>
      证件号
    </view>
    <view class="chooseClick">
      <input type="text" placeholder="请输入18位身份证号码" value="{{idCard}}" bindinput="idCardValue" />
    </view>
  </view>
  <view>
    <button catchtap="confrimChooseArea">保存</button>
  </view>
</view>
<!-- 缺少身份证号后完善身份信息 -->
<!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" salesCode="{{changeSalesCode}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" bind:sureSwitch="confirmChangeTieModal" bind:close="closeChangeTieModal"></switch-sale>
<!-- 分享页面是否切换业务员 -->
<audit wx:if="{{audit}}" bind:reject="reject" activityId="{{ids}}" activityType="{{category}}" financeStatus="{{activity.financeRecheckStatus}}"></audit>
<reject-reason showDialog="{{showReject}}" activityId="{{ids}}" rejectReason="{{rejectReason}}" audit="{{activity.auditStatus}}" financeStatus="{{activity.financeStatus}}" bind:rejectButton="triggerEvent"></reject-reason>
<!-- 多商品活动选择 -->
<!-- <select-project projectArray="{{project}}" showParamModal="{{showParamModal}}" bind:selectProject="selectProject" bind:sure="sureProject" VSwiper='{{bannerImg}}' bannerType='{{bannerType}}' itemObject="{{itemObject}}" commdityConfig="{{commdityConfig}}" commdityDialog="{{commdityDialogBg}}" limitTextConfig="{{limitTextConfig}}"></select-project> -->
<!-- 一键报障 -->
<contact module="活动参与" pageName="报名/商品活动详情" pageUrl="{{pageUrl}}" businessData="{{activity}}" isShow="{{isShow}}"></contact>


<overTips content="{{ message }}" showClose="{{false}}" isShowOverlay="{{isShowOverlay}}" bind:closeOverTip="closeOverTip"></overTips>

<globalPrompt model:show="{{ showGlobalPrompt }}" content="{{globalPromptMessage}}"></globalPrompt>
