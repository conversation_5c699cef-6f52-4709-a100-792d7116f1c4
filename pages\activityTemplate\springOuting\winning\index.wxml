<!-- pages/activityTemplate/springOuting/winning/index.wxml -->
<view class="winning">
  <scroll-view scroll-y="{{ isLaunch ? true : false }}" scroll-top="{{ scrollTop }}" scroll-with-animation class="winning_scroll">
    <image class="winning_bgImg" src="{{winningPage.bgUrl}}"></image>
    <!-- 内容 -->
    <view class="winning_content" style="{{contentStyle}}">
      <view class="winning_content_info" style="overflow: {{ isLaunch ? 'visible' : 'hidden' }}">
        <image class="winning_content_img" src="{{winningPage.descUrl}}"></image>
      </view>
      <view class="winning_content_btn" bind:tap="changeLaunch" style="color:{{winningPage.fliptextColor}}">
        {{ isLaunch ? '收起' : '展开' }}
        <van-icon class="winning_content_btn_triangle {{ isLaunch ? 'winning_content_btn_triangle_up' : '' }}" class-prefix="i" name="xiasanjiaoxing" size="15px" color="{{winningPage.fliptextColor}}" />
      </view>
    </view>
  </scroll-view>
  <!-- 底部 -->
  <view class="winning_bottom">
    <image class="winning_bottom_bgImg" src="{{winningPage.pictureUrl}}"></image>
    <view class="winning_bottom_info">
      <image class="winning_bottom_img" src="{{winningData.prizeImg}}"></image>
      <view class="winning_bottom_title" style="{{winningPage.plainText.styleStr}}">
        {{winningPage.plainText.propValue}}
      </view>
    </view>
    <div class="winning_bottom_btns">
      <view wx:for="{{winningPage.btnsList}}" class="winning_bottom_btns_item" data-item="{{item}}" bind:tap="onclickBtn" style="{{item.styleStr}}">
        {{item.propValue}}
      </view>
    </div>
  </view>
</view>
<!-- 领奖信息弹窗 -->
<van-popup show="{{isShowInfo}}" closeable="{{false}}" close-on-click-overlay="{{true}}" round overlay-style="z-index: 1" bind:close="onClose" custom-style="background-color: {{acceptAwardImg&&acceptAwardImg.propValue ? 'transparent' : '#fff'}}">
  <edit-infor activityTemplateType="{{winningData.activityTemplateType}}" acceptAwardImg="{{acceptAwardImg}}" acceptAwardBtn="{{acceptAwardBtn}}" dialogType="receiveInfo" attributes="{{attributes}}" recordId="{{winningData.recordId}}" topTitle="领奖信息" bindsaveSuccess="saveSuccess" receiveType="{{winningData.receiveType}}" isFirst="{{winningData.isFirst}}" getUrlRes="{{winningData.getUrlRes}}"></edit-infor>
</van-popup>
<!-- 成功提示 -->
<van-overlay show="{{ showTipsPage }}" z-index="99999999">
  <view class="wrapper">
    <view class="wrapper_content">
      <view wx:for="{{tipsPage}}" wx:key="index">
        <view wx:if="{{!item.isHide}}">
          <view class="wrapper_content_item {{ item.component === 'VButton' ? 'wrapper_content_item_btn' : '' }}" style="{{item.styleStr}}">
            <image wx:if="{{item.component === 'Picture'}}" class="wrapper_content_item_img" src="{{item.propValue.url}}"></image>
            <view wx:if="{{item.component === 'VButton'}}" data-item="{{item}}" bind:tap="closeTipsPage" class="wrapper_content_item_btn_info">
              {{item.propValue}}
            </view>
            <view wx:if="{{ item. component === 'plainText' }}">{{item.propValue}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</van-overlay>