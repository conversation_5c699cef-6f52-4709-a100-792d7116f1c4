/* pages/activityTemplate/vote/pk-result/index.wxss */

.equity-home-nav {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 10;
}
.equity-home-nav .home_nav_menu {
  position: absolute;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.equity-home-nav .home_nav_menu .home_nav_menu_icon {
  position: absolute;
  left: 0;
}

.mymask {
  position:fixed;
  z-index:49;
  width:750rpx;
  height:100%;
  border:none;
  border-radius:0;
}
.mask_content{
  margin-top: 15vh;
}
.reflash_img{
  width: 590rpx;
  height: 880rpx;
  display: block;
  margin: 0 auto;
}
.reflash_text_wrap{
  width: 100%;
  position: absolute;
  top: 20%;
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 46rpx;
  text-align: center;
}
.reflash_btn_wrap{
  width: 100%;
  height: 88rpx;
  position: absolute;
  bottom: 20%;
  text-align: center;
}
.reflash_btn{
  width: 512rpx;
  height: 100%;
  background: #FFAE54;
  border-radius: 16px;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
}

.contain{
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.btn{
  position: absolute;
  box-sizing: border-box;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.img-com{
  position: absolute;
}

.activity-img{
  position:absolute;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.work-swiper{
  position: absolute;
}
.work-img{
  height: 100%;
  width: 100%;
}

.work-video{
  position: absolute;
}
.carousel_video{
  width: 100%;
  height: 100%;
}

.vote-pk-progress-wrap{
  position: absolute;
  padding: 28rpx 40rpx 81rpx;
  box-sizing: border-box;
  border-radius: 32rpx;
  background-color: #fff;
}
.vote-pk-progress{
  overflow: hidden;
  border-radius: 10rpx;
  width: 100%;
  height: 100%;
  position: relative;
}
.pk-progress{
  height: 100%;
  position: absolute;
  top: 0;
}
.separate-img{
  position: absolute;
  z-index: 2;
  top: 24rpx;
  width: 72rpx;
  height: 66rpx;
}
.left-progress{
  left: 0;
  width: 325rpx;
  background-size: auto 100%;
  clip-path: polygon(0 0, 100% 0, calc( 100% - 40rpx ) 100%, 0 100%);
}
.right-progress{
  right: 0;
  width: 325rpx;
  background: linear-gradient(91deg, #6076FF 6%, #6EDBFB 93%);
  clip-path: polygon(40rpx 0, 100% 0, 100% 100%, 0 100%);
}
.progress-num{
  position: absolute;
  font-family: DIN Alternate;
  font-size: 36rpx;
  font-weight: bold;
  top: 19rpx;
}
.left-progress-num{
  left: 16rpx;
}
.right-progress-num{
  right: 16rpx;
}
.progress-text{
  position: absolute;
  bottom: 21rpx;
  font-family: Alibaba PuHuiTi 3.0;
  font-size: 23rpx;
}
.left-progress-text{
  left: 16rpx;
}
.right-progress-text{
  right: 16rpx;
}

.rick-text{
  position: absolute;
}