.wrapper{
  display: flex;
  align-items: center;
 
  justify-content: center;
  height: 100%;
}
.block {
  width: 628rpx;
  /* height: 946rpx; */
  padding: 0 32rpx;
  padding-bottom: 48rpx;
  border-radius: 32rpx;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

image {
  width: 100%;
  height: 100%;
  display: block;
}
.close{
  position: relative;
  z-index: 99;
  text-align: right;
  padding-top: 32rpx;
  box-sizing: border-box;
}
.header{
  position: relative;
  z-index: 99;
  font-family: PingFangSC-Regular, PingFangSC-Regular;
  font-weight: 400;
  font-size: 32rpx;
  color: #17204D;
  text-align: center;
}
.tip{
  font-weight: 400;
  font-size: 28rpx;
  color: #555C80;
  line-height: 40rpx;
  text-align: left;
  margin-top: 30rpx;
}
.selectRadio{
  margin-top: 34rpx;
  display: flex;
  padding-right: 150rpx;
  justify-content: space-between;
}
.selectRadio .van-radio{
  margin-right: 72rpx;
}
.choose{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32rpx;
}
.choose view:first-child{
  /* width: 128rpx; */
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #555C80;
}
.choose view:first-child span{
  color: #FC3E1B;
}
.chooseClick{
  width: 400rpx;
  height: 64rpx;
  background: #F7F8FA;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 24rpx;
  margin-left: 30rpx;
  box-sizing: border-box;
}

.swiper-list{
  width: 100%;
  /* height: 510rpx; */
  overflow: hidden;
}
.dSteps{
  margin-top: 32rpx;
  padding: 10rpx 0 58rpx;
  border-top:1px solid #F7F8FA;
}
.footer-content{
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  width: 558rpx;
  height: 96rpx;
  text-align: center;
  line-height: 96rpx;
}
.saveBtn{
  width: 558rpx;
  height: 96rpx;
  line-height: 96rpx;
  color: #fff;
  font-family: PingFang SC, PingFang SC;
  font-weight: normal;
  font-size: 32rpx;
  margin: 0 auto;
  background: linear-gradient( -90deg, #4492FC 0%, #025CEA 100%);
  border-radius: 48rpx;
}
.repair{
  font-family: PingFangSC-Medium, PingFangSC-Medium;
  font-weight: 400;
  font-size: 32rpx;
  color: #025CEA;
  width: 264rpx;
  margin-right: 30rpx;
  height: 96rpx;
  border-radius: 48rpx;
  border: 2rpx solid #025CEA;
}
.saveShare{
  font-family: PingFangSC-Medium, PingFangSC-Medium;
  font-weight: 400;
  font-size: 32rpx;
  color: #FFF;
  width: 264rpx;
  height: 96rpx;
  line-height: 96rpx;
  background: linear-gradient( -90deg, #4492FC 0%, #025CEA 100%);
  border-radius: 48rpx;
}
.footer-content button{
  padding: 0;
}
.noContent{
  text-align: center;
  font-family: PingFangSC-Regular, PingFangSC-Regular;
  font-weight: 400;
  font-size: 28rpx;
  color: #555C80;
  line-height: 140rpx;

}
