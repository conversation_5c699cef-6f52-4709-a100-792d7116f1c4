/* pages/activityTemplate/components/saveImage/saveImage.wxss */
.canvas-poster {
  position: fixed;
  width: 343px;
  height: 400px;
  top: 100%;
  left: 100%;
  background: #fff;
  overflow: hidden;
  background-color: transparent;
}
.canvasContent{
  position: fixed;
  /* width: 160rpx; */
}
.fixedBg{
  position: fixed;
  width: 100%;
  left: 0;
  height: 100%;
  top: 0;
  z-index: 9999;
  background: #000;
}
.downLoadBtn{
  width: 20rpx;
  
  height: 96rpx;
  background-color: linear-gradient(90deg, #FF5030 0%, #FC6B37 100%);
  position: fixed;
  top: 276rpx;
  left: 710rpx;
  color: #fff;
  font-family: PingFang SC-Medium;
  font-size: 20rpx;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  line-height: 28rpx;
  /* writing-mode: vertical-rl; */
}
.contentPop{
  position: fixed;
  z-index: 99;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #fff;
  height: 100vh;
  background: rgba(0,0,0,.5);
}
.expert{
  width: 100%;
  height: 800rpx;
  border-radius: 32rpx 32rpx 0 0;
  display: block;
}
.ecode{
  width: 108rpx;
  height: 108rpx;
  /* margin-left: 20rpx; */
}
.containter{
  display: flex;
  justify-content: space-between;
  padding-left: 34rpx;
  padding-right: 50rpx;
  padding-top: 20rpx;
}
.headImg{
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}
.saleInfo{
  display: flex;
  font-size: 24rpx;
  color: #555C80;
  padding-top: 64rpx;
  font-family: PingFang SC-Medium;
  text-align: left;
}
.saleInfo text{
  color: #555C80;
  margin-left: 20rpx;
}
.saleaName{
  margin-bottom: 6rpx;
  color: #17204D;
  font-size: 32rpx;
  font-weight: 500;
}
.wrapper{
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-y: auto;
  position: fixed;
  top: 50%;
  left: 50%;
  transform:translateX(-50%) translateY(-50%);
  min-height:100vh;
 
}
.wrapper-content{
  position: relative;
  width: 686rpx;
  height: 1000rpx;
  text-align: center;
  background: #fff;
  color: #17204D;
  font-size: 32rpx;
  overflow: hidden;
  border-radius: 32rpx;
}
.popBox{
  width: 686rpx;
  height: 362rpx;
  background:linear-gradient( 180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.88) 50%, #FFFFFF 100%);
  position: absolute;
  top: 490rpx;
  left: 0;
  z-index: 2;
}
.saleContent{
  position: absolute;
  z-index: 55;
  top: 482rpx;
  width: 622rpx;

  left: 50%;
  transform:translateX(-50%);
}
.saleContent .bg{
  width: 100%;
  height: 200rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.bottomContent{
  position: absolute;
  top: 710rpx;
  left: 0;
  padding: 0 26rpx 0rpx;
  z-index: 22;
}
.detail{
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.event{
  width: 138rpx;
  height: 108rpx;
  position: relative;
  font-size: 20rpx;
  color: #7E849F;
  text-align: center;
  padding-top: 14rpx;
  box-sizing: border-box;
  margin-right: 26rpx;
  z-index: 4;
}

.numText{
  font-size: 32rpx;
  color: #17204D;
  font-weight: 600;
  margin-bottom: 16rpx;
}
.badgeTitle{
  width: 190rpx;
  height: 28rpx;
  display: block;
  margin: 0 auto;

}
.badge .event{
  margin: 0 26rpx 22rpx 0;
}
.badge .event:nth-child(4n){
  margin-right: 0;
}
.detail .event:nth-child(4n){
  margin-right: 0;
}
.badge{
  width: 100%;
  
  display: flex;
  justify-content:start;
  margin-top: 28rpx;
  box-sizing: border-box;
  flex-wrap: wrap;
}
.boxBg{
  width: 138rpx;
  height: 108rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.badgeImg{
  width: 82rpx;
  height: 96rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 1;
  transform:translateX(-50%) translateY(-48%);

}
.close{
  width: 64rpx;
  height: 64rpx;
  display: block;
  margin-top: 30rpx;
  /* position: absolute;
  top: 50%;
  left: 50%; */
  /* transform:translateX(-50%) translateY(-50%); */
}
