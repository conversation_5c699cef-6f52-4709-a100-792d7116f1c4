<view class="content" style="{{contentStyle}}">

  <view class="content_header" style="height: {{navBarData.navBarHeight}}px">
    <view class="content_header_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px">
      <van-icon name="arrow-left" size="1.3em" color="#fff" bind:click="goBack" class="content_header_menu_icon" style="left:{{navBarData.menuRight}}px;" />
      <view class="content_header_menu_title">问卷活动</view>
    </view>
  </view>

  <view class="content_main">
    <view wx:for="{{componentData}}" wx:key="index">
      <view wx:if="{{!item.isHide}}">
        <view class="content_main_item {{ item.component === 'VButton' ? 'content_main_item_btn' : '' }}" style="{{item.styleStr}}">
          <image wx:if="{{item.component === 'Picture'}}" class="content_main_item_img" src="{{item.propValue.url}}"></image>
          <view wx:if="{{item.component === 'VButton'}}" data-item="{{item}}" bind:tap="inquireSubmit" class="content_main_item_btn_info">
            {{item.propValue}}
          </view>
          <view wx:if="{{item.component === 'rule'}}" class="content_main_item_rule">
            <rich-text nodes="{{item.propValue}}"></rich-text>
          </view>
          <view wx:if="{{ item. component === 'plainText' }}">{{item.propValue}}</view>
          <view wx:if="{{ item. component === 'ruleText' }}">{{item.propValue}}</view>
        </view>
      </view>
    </view>

    <view class="questions" >
      <view class="questions_main">
        <block wx:for="{{list}}" wx:key="index">
          <!-- 输入框 -->
          <view class="questionType" wx:if="{{item.type == 'INPUT'}}">
            <view class="property">
              <view class="question_title">
                <view wx:if="{{item.needed == 1}}" class="question_title_required">*</view>
                <view>Q{{index+1}}</view>
              </view>
            </view>
            <textarea type="text" bindinput="inputBlur" placeholder="请输入" value="{{item.defaultValue}}" data-key="{{item.id}}" placeholder-style="font-size:28rpx;color:#C0C0CA" data-index='{{index}}' disabled="{{isEdit}}" />
          </view>
          <!-- 多选项 -->
          <view class="questionType" wx:if="{{item.type == 'MUTISELECT'}}">
            <view class="property">
              <view class="question_title">
                <view wx:if="{{item.needed == 1}}" class="question_title_required">*</view>
                <view>Q{{index+1}}</view>
              </view>
              <view class="question_key">(多选) {{item.title}}</view>
            </view>
            <van-checkbox-group bind:change="onChange" value="{{item.defaultValue}}" data-key="{{item.id}}" data-index='{{index}}'>
              <block wx:for="{{item.options}}" wx:for-item="option" wx:key="id">
                <!-- 有图片的选项结构 -->
                <view class="imageUrl {{option.select?'selectType':''}}" wx:if="{{item.imageUrl}}">
                  <view class="optionImg">
                    <image src="{{option.url}}" />
                  </view>
                  <van-checkbox name="{{option.id}}" icon-size="18px" checked-color="linear-gradient(to right,#025CEA,#4492FC)" label-class="{{option.select?'colorType':''}}" disabled="{{isEdit}}">
                    {{option.content}}
                  </van-checkbox>
                </view>
                <!-- 没有图片的选项 -->
                <van-checkbox wx:else name="{{option.id}}" icon-size="18px" checked-color="linear-gradient(to right,#025CEA,#4492FC)" label-class="{{option.select?'colorType':''}}" disabled="{{isEdit}}">
                  {{option.content}}
                </van-checkbox>
              </block>
            </van-checkbox-group>
          </view>
          <!-- 单选框 -->
          <view class="questionType" wx:if="{{item.type == 'RADIO'}}">
            <view class="property">
              <view class="question_title">
                <view wx:if="{{item.needed == 1}}" class="question_title_required">*</view>
                <view>Q{{index+1}}</view>
              </view>
            </view>
            <van-radio-group  bind:change="onChanges" value="{{item.defaultValue}}" data-key="{{item.id}}" data-index='{{index}}'>
              <block wx:for="{{item.options}}" wx:for-item="option" wx:key="id">
                <!-- 有图片的选项结构 -->
                <view class="imageUrl {{option.select?'selectType':''}}" wx:if="{{item.imageUrl}}">
                  <view class="optionImg">
                    <image src="{{option.url}}" />
                  </view>
                  <van-radio name="{{option.id}}" icon-size="18px" checked-color="linear-gradient(to right,#025CEA,#4492FC)" label-class="{{option.select?'colorType':''}}" disabled="{{isEdit}}">
                    {{option.content}}
                  </van-radio>
                </view>
                <!-- 没有图片的选项 -->
                <van-radio wx:else name="{{option.id}}" data-content="{{option.content}}" icon-size="18px" checked-color="linear-gradient(to right,#025CEA,#4492FC)" label-class="{{option.select?'colorType':''}}" disabled="{{isEdit}}">
                  {{option.content}}
                </van-radio>
              </block>
            </van-radio-group>
          </view>
        </block>
      </view>
    </view>
  </view>

</view>
<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close" allShow='{{isClose}}'></login-box>
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler">点击进入</button>
  </view>
</view>
<view wx:if="{{audit}}" class="jinzhi"></view>
<!-- 活动审核显示底部操作按钮 -->
<audit wx:if="{{audit}}" bind:reject="reject" activityId="{{activityId}}" activityType="13"></audit>
<!-- 活动审核显示底部操作按钮 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" audit="{{auditStatus}}" activityType="13" financeStatus="{{financeStatus}}" rejectReason="{{rejectReason}}" linkTimes='2'></reject-reason>
<contact module="活动参与" pageName="问卷填写" pageUrl="{{pageUrl}}" businessData="{{activity}}" isShow="{{isShow}}"></contact>