<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close" allShow='{{isClose}}'></login-box>
<save-image show="{{showPop}}" btnText="{{btnText}}" bind:clickDown="onClickHide"></save-image>
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler">点击进入</button>
  </view>
</view>
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg1}}"></view>
<view class="modalDlgPop" wx:if="{{showModalDlg1}}">
  <block wx:if="{{cardIdLQ ? false : true}}">
    <!-- //抽卡弹窗 -->
    <block wx:for="{{dialogPageConfig.componentData}}" wx:key="index">
      <view wx:if="{{item.type=='text'}}" class="dialogTitle" style="{{item.styleStr}}">
        <view>{{item.propValue}}</view>
      </view>
      <image class="modaImg1" wx:if="{{item.type=='cardCollectingImg'}}" src="{{imgjk}}" style="top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx" />
      <view wx:if="{{item.type=='button'}}" style="{{item.styleStr}}" class="sureBtn" bindtap="onToAccept">
        {{item.propValue}}
      </view>
    </block>
  </block>
  <block wx:else>
    <!-- 赠卡弹窗 -->
    <block wx:for="{{presentPageConfig.componentData}}" wx:key="index">
      <view wx:if="{{item.type=='text'}}" class="dialogTitle" style="{{item.styleStr}}">
        <view>{{item.propValue}}</view>
      </view>
      <image class="modaImg1" wx:if="{{item.type == 'cardCollectingImg'}}" src="{{imgjk}}" style="top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx" />
      <view wx:if="{{item.type=='button'}}" style="{{item.styleStr}}" class="sureBtn" bindtap="onToAccept2">
        {{item.propValue}}
      </view>
    </block>
  </block>
</view>
<view class="mask1" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg2}}"></view>
<view class="modalDlgPop" wx:if="{{showModalDlg2}}">
  <!-- 节日卡弹窗 -->

  <block wx:for="{{festivalPageConfig.componentData}}" wx:key="index">
    <view wx:if="{{item.type=='text'}}" class="dialogTitle" style="{{item.styleStr}}">
      <view>{{item.propValue}}</view>
    </view>
    <image class="modaImg1" wx:if="{{item.type == 'cardCollectingImg'}}" src="{{imgday}}" style="top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx" />
    <view wx:if="{{item.type=='button'}}" style="{{item.styleStr}}" class="sureBtn" bindtap="onToAccept3">
      {{item.propValue}}
    </view>
  </block>
</view>
<!-- 助力 -->
<view class="mask1" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg4}}"></view>
<view class="modalDlgPop1" wx:if="{{showModalDlg4}}">
  <view class="helpBox">
    <block wx:for="{{helpPageConfig.componentData}}" wx:key="index">
      <image wx:if="{{item.type=='backgroundImg'}}" class="helpImg" style="{{item.styleStr}}" src="{{item.propValue.url}}" />
      <view wx:if="{{item.type=='text'}}" class="dialogTitle" style="{{item.styleStr}}">
        <view>{{item.propValue}}</view>
      </view>
      <view wx:if="{{item.type=='button'}}" style="{{item.styleStr}}" class="sureBtn" bindtap="onToAccept4">
        {{item.propValue}}
      </view>
    </block>
  </view>
</view>
<!-- 合成卡 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg3}}"></view>
<view class="modalDlgPop1" wx:if="{{showModalDlg3}}">
  <block wx:for="{{dialogPageConfig.componentData}}" wx:key="index">
    <view wx:if="{{item.type=='text'}}" class="dialogTitle" style="{{item.styleStr}}">
      <view>{{item.propValue}}</view>
    </view>
    <image class="modaImg1" wx:if="{{item.type == 'cardCollectingImg'}}" src="{{composeCardConfig.cardImg}}" style="top:{{item.style.top*2}}rpx;left:{{item.style.left*2}}rpx" />
    <view wx:if="{{item.type=='button'}}" style="{{item.styleStr}}" class="sureBtn" bindtap="onHeTapClone">
      {{item.propValue}}
    </view>
  </block>
</view>
<!-- 页面结构 -->
<scroll-view scroll-y lower-threshold='100' style="margin-bottom: 200rpx;height: 100vh;">
  <!-- <view class="backArrow" style="top:{{height+20}}px;z-index:99; color: #000;" >
      <van-icon name="arrow-left" bindtap="goBack"/>
    </view> -->
  <block wx:for="{{componentData}}" wx:key="index">
    <!-- 图片 -->
    <image wx:if="{{item.type == 'topBackgroundImg'}}" mode="widthFix" src="{{item.propValue.url}}" style="{{item.styleStr}}" class="imageStyle"></image>
    <!-- 跳转按钮 -->
    <view wx:if="{{item.type === 'button'&&(item.btEvent=='助力记录'||item.btEvent=='活动细则'||(item.btEvent=='中奖记录'&&composeCardConfig.isDraw == 1))}}" style="{{item.styleStr}}" class="rule" data-btevent="{{item.btEvent}}" bindtap="jumpPage">
      {{item.propValue}}
    </view>
    <!-- 抽奖按钮 -->
    <view wx:if="{{item.type === 'button' && item.btEvent=='立即抽卡'}}" style="{{item.styleStr}}" class="rule" bindtap="onCard">
      {{item.propValue}} X {{leftNum}}
    </view>
    <block wx:if="{{item.type =='text'}}">
      <!-- wx:if="{{giveNum> 0 && shareNum> 0 ? true :false}}" -->
      <view class="explain" style="{{item.styleStr}}">
        每天登录获得
        <text class="fff">{{loginNum}}</text>
        次抽卡机会,
          助力和赠卡获额外
        <text class="fff">{{giveNum+shareNum}}</text>
        次机会
      </view>
      <!-- <view class="explain" style="{{item.styleStr}}" wx:elif="{{giveNum > 0 ? true : false}}">
          每天登录获得<text class="fff">{{loginNum}}</text>次抽卡机会,
          赠卡获额外<text class="fff">{{giveNum}}</text>次机会
        </view>
        <view class="explain" style="{{item.styleStr}}" wx:elif="{{shareNum > 0 ? true : false}}">
          每天登录获得<text class="fff">{{loginNum}}</text>次抽卡机会,
          分享可获额外<text class="fff">{{shareNum}}</text>次机会
        </view>
        <view class="explain" style="{{item.styleStr}}" wx:elif="{{loginNum > 0 ? true: false}}">
          每天登录获得<text class="fff">{{loginNum}}</text>次抽卡机会
        </view> -->
    </block>
    <image wx:if="{{item.type == 'backgroundImg'}}" mode="widthFix" src="{{item.propValue.url}}" style="{{item.styleStr}}" class="imageStyle"></image>
    <view class="card" wx:if="{{item.type == 'cardCollectingCards'}}" style="{{item.styleStr}}">
      <block wx:for="{{cardCfgs}}" wx:key="id" wx:for-item="items">
        <view class="card-img">
          <text class="txt" wx:if="{{items.cardHold > 0 ? true: false}}" style="background-color:{{numberBackgroundColor}};color:{{numberColor}}">{{items.cardHold}}</text>
          <image wx:if="{{items.cardHold > 0 ? true: false}}" class="card-img-content" bindtap='preview' data-src='{{items.cardImg}}' src="{{items.cardImg}}" />
          <image wx:else class="card-img-content" src="{{items.defaultImg}}" />
          <button class="giveBtn" bindtap='onGiveImg' wx:if="{{items.cardHold > 1}}" style="background-color:{{buttonStyle.backgroundColor}};color:{{buttonStyle.color}};background-image:{{buttonStyle.backgroundImage}}" data-img="{{items.cardImg}}" data-cardid='{{items.id}}' open-type="share">
            {{buttonStyle.propValue}}
          </button>
        </view>
      </block>
      <view class="card-img">
        <text class="txt" wx:if="{{mergeCards > 0 ? true: false}}" style="background-color:{{numberBackgroundColor}};color:{{numberColor}}">{{mergeCards}}</text>
        <image class="card-img-content" wx:if="{{mergeCards > 0  ? true : false}}" src="{{composeCardConfig.cardImg}}" bindtap='preview' data-src='{{composeCardConfig.cardImg}}' />
        <image class="card-img-content default" wx:else src="{{composeCardConfig.defaultImg}}" />
      </view>
    </view>
    <block wx:if="{{item.type =='cardCollectingTime'}}">
      <view class="time_main" style="{{item.styleStr}}" wx:if="{{sub_time1 > 0 && sub_time2 > 0  ?true:false}}">
        {{item.propValue}}
        <span style="color: {{item.style.numberColor}};">{{timeObj.day}}</span>
        天
        <span style="color: {{item.style.numberColor}};">{{timeObj.hour}}</span>
        时
        <span style="color: {{item.style.numberColor}};">{{timeObj.points}}</span>
        分
        <span style="color: {{item.style.numberColor}};">{{timeObj.seconds}}</span>
        秒
      </view>
   
      <view wx:elif="{{sub_time2 < 0}}" class="time_main" sstyle="{{item.styleStr}}">
        活动结束啦，敬请期待
      </view>
    </block>
    <block wx:if="{{item.type === 'button' && item.btEvent=='合成'}}">
      <!-- 合成 -->
      <view wx:if="{{isShowImg}}" bindtap='onHeTap' style="{{item.styleStr}}" class="rule">
        {{item.propValue}}
      </view>
      <!-- 去抽奖 -->
      <!-- mergeCards合成卡次数 -->
      <view bindtap='onLuckDraw' wx:if="{{ mergeCards > 0 && hasDraw == true && composeCardConfig.isDraw == 1 ? true : false }}" style="{{item.styleStr}}" class="rule">
        抽奖
      </view>
    </block>

    <block wx:if="{{item.type === 'button' && item.btEvent=='助力'}}">
      <!-- <block wx:if="{{isShowImg &&  mergeCards > 0 && hasDraw == false ? true :false}}">
          <button class="shareBtn" disabled="{{!isShow}}"	 style="{{item.styleStr}}"  open-type='share' data-zhuli='ACTIVITY_SHARE' >{{item.propValue}}</button>
        </block>
        <block wx:elif="{{(mergeCards > 0 && hasDraw == false) ||  isShowImg ? true : false}}">
          <button class="shareBtn" style="{{item.styleStr}}"  open-type='share' data-zhuli='ACTIVITY_SHARE' >{{item.propValue}}</button>
        </block> -->
      <block>
        <button class="shareBtn" style="{{item.styleStr}}" open-type='share' data-zhuli='ACTIVITY_SHARE'>
          {{item.propValue}}
        </button>
      </block>
    </block>
    <view wx:if="{{item.type == 'cardCollectingPrize'}}" style="{{item.styleStr}}" class="prizeBox">
      <view style="height:586rpx;width: 100%;overflow-y: scroll;display: flex;flex-wrap: wrap;justify-content: space-between;">
        <view wx:for="{{rewardImg}}" wx:for-item="list" wx:key="id" class="content-prize {{item.format == 2?'marginBotton':''}}">
          <block wx:if="{{item.format == 2}}">
            <image class="prizeImg" src="{{item.regionImgOptions.url}}" mode="aspectFill"></image>
            <image src="{{list.url}}" class="formatImg2" mode="aspectFill"></image>
            <view class="prizeAttribute">{{list.value}}</view>
            <view class="formatName2" style="background-image:{{item.nameImgOptions.backgroundColor}} url({{item.nameImgOptions.bgStatus==1?item.nameImgOptions.url:null}})">
              {{list.name}}
            </view>
          </block>
          <block wx:if="{{item.format == 1}}">
            <image src="{{list.url}}" class="formatImg1" mode="aspectFill"></image>
            <view class="prizeAttribute1" style="color:{{item.style.textColor}}">
              {{list.name}}
            </view>
          </block>
        </view>
      </view>
    </view>
    <block wx:if="{{item.type === 'poster' && flagPoster == 1}}">
      <!-- <root-portal wx:if="{{flagPoster == 1}}" enable="{{true}}"> -->
        <!-- 专属海报弹框 -->
        <post-popup show="{{showPoster}}" codeUrl="{{qrcode}}" activityName="{{title}}" btnText="{{item}}" banner="{{posterBackgroundImg.propValue.url}}" title="{{posterText.propValue}}" textStyle="{{posterText}}" bind:onClickHide="onClickHide" bind:clickDownPoster="downPoster" audit="{{audit}}"></post-popup>
        <!-- 专属海报弹框 -->
      <!-- </root-portal> -->
    </block>
  </block>
</scroll-view>
<!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" salesCode="{{changeSalesCode}}" bind:close="closeChangeTieModal"></switch-sale>
<!-- 分享页面是否切换业务员 -->
<!-- 活动审核显示底部操作按钮 -->
<audit wx:if="{{audit}}" bind:reject="reject" activityId="{{activityId}}" linkType="{{linkType}}"></audit>
<!-- 活动审核显示底部操作按钮 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" audit="{{auditStatus}}" financeStatus="{{financeStatus}}" rejectReason="{{rejectReason}}"  rejectButton="triggerEvent"></reject-reason>
<contact module="活动参与" pageName="集卡首页" pageUrl="{{pageUrl}}" isShow="{{isShowBtn}}"></contact>