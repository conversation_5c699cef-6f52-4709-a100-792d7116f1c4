page {
  background-color: #fff !important;
  // padding-bottom: calc(constant(safe-area-inset-bottom));
  // padding-bottom: calc(env(safe-area-inset-bottom));
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.winning {
  width: 100%;
  height: 100%;

  .winning_scroll {
    width: 100%;
    height: 100%;
    position: relative;
  }

  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    color: transparent;
    height: 0;
  }

  .winning_bgImg {
    height: 2293rpx;
  }

  .winning_content {
    position: absolute;
    width: 100%;
    height: calc(100% - 700rpx - 366rpx - 128rpx);
    top: 700rpx;
    padding: 0 50rpx;
    box-sizing: border-box;
    transition: all 0.5s;

    .winning_content_info {
      height: 100%;
      overflow: hidden;
    }

    .winning_content_btn {
      position: absolute;
      bottom: -32rpx;
      color: #005A87;
      left: 50%;
      transform: translate(-50%, 100%);
      display: flex;
      align-items: center;
      justify-content: center;

      .winning_content_btn_triangle {
        margin-left: 10rpx;
        transition: all 0.6s;

        &.winning_content_btn_triangle_up {
          transform: rotate(-180deg);
        }
      }
    }

    .winning_content_img {
      height: 1078rpx;
    }
  }

  .winning_bottom {
    width: 100%;
    height: 366rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;

    .winning_bottom_bgImg {
      position: absolute;
    }

    .winning_bottom_info {
      display: flex;
      height: 202rpx;
      padding: 35rpx 46rpx;
      box-sizing: border-box;
      position: relative;
      z-index: 2;
    }

    .winning_bottom_img {
      width: 133rpx;
      height: 133rpx;
    }

    .winning_bottom_title {
      padding-left: 26rpx;
      padding-top: 20rpx;
    }

    .winning_bottom_btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      height: calc(100% - 202rpx);
      position: relative;

      .winning_bottom_btns_item {
        background-repeat: no-repeat;
        background-size: 100% 100%;
        position: absolute;
        display: flex;
        z-index: 1;
        align-items: center;
        justify-content: center;
      }
    }
  }

  image {
    width: 100%;
    height: 100%;
    display: block;
  }
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  display: flex;

  .wrapper_content {
    position: relative;
    width: 500rpx;
    height: 550rpx;

    .wrapper_content_item {
      position: absolute;

      &.wrapper_content_item_btn {
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }

      .wrapper_content_item_btn_info {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }

      .wrapper_content_item_img {
        width: 100%;
        height: 100%;
        display: block;
      }
    }
  }
}
