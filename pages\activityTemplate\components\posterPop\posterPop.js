

var drawing = false; /*避免多次点击保存按钮*/
const API = require('../../../../api/request')
const CryptoJS = require('../../../../utils/CryptoJs.js');
import Dialog from '../../../../assets/vant/dialog/dialog'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: ''
    },
    isShowBtn:{
      type: Boolean,
      value: true
    },
    btnText: {
      type: Object,
      value: {}
    },
    codeUrl: {
      type: String,
      value: 'https://txqmp.cpic.com.cn/uploads/img/expertHead.png'
    },
    banner: {
      type: String,
      value: ''
    },
    audit:{
      type: String||Number,
      value: 0
    },
    // backgroundImgUrl:{
    //   type:String,
    //   value:''
    // },
    activityName: {
      type: String,
      value: ''
    },
    textStyle: {
      type: Object,
      value: {}
    },
    mode: {
      type: String,
      value: "aspectFit"
    }
   
  },

  /**
   * 组件的初始数据
   */
  data: {
    bannerImg: '',
    codeImg: '',
    baseInfo:{},//个人信息
    occupationalInfo:{
      // occupationalShowFlag:'1'
    },//职业信息
    unit:'人',
    height:500, //400,500,556,615
    salesImg:'https://txqmp.cpic.com.cn/uploads/img/postBg.png',
    boxImg:'/image/template/box.png',
    titleImg:'/image/template/title.png',
    headUrl:'https://txqmp.cpic.com.cn/uploads/img/salesHeadImg.jpg',//专家默认头像
    list:[{name:'从业经验/年',num:22},{name:'服务客户/人',num:8},{name:'客户保障/',num:999},{name:'保单件数/',num:76}],
    //荣誉信息列表
    honorList:[],
    badgeList:[
      {"medalName":"康养服务代言人","medalImageUrl":"/image/badgeImg/KYDYR.png"},
      {"medalName":"CG-CLUB","medalImageUrl":"/image/badgeImg/cgClub.png"},
      {"medalName":"蓝鲸协会","medalImageUrl":"/image/badgeImg/blueWhale.png"},
      {"medalName":"诚信服务大使","medalImageUrl":"/image/badgeImg/CXFWDS.png"},
      {"medalName":"保险规划师","medalImageUrl":"/image/badgeImg/salePlan.png"},
      {"medalName":"星级业务员","medalImageUrl":"/image/badgeImg/saleStar.png"},
      {"medalName":"数字化顾问","medalImageUrl":"/image/badgeImg/numConsultant.png"}
    ]
  },
  lifetimes: {
    // 加载事件
    async attached () {
      // const res = await API.queryByEmpNo()
      // if(res.data.code === 200){
      //   let {baseInfo,occupationalInfo,honorList} = res.data.data.content
      //   let newArray =  honorList.filter(item=> item.medalReachFlag === '1')
      //   newArray.map((item)=>{
      //     wx.getImageInfo({
      //       src: item.medalImageUrl,
      //       success: (res) => {
      //         console.log(res.path)
      //         item.medalImageUrl = res.path
      //       }
      //     });
      //   })
      //   baseInfo.phone = baseInfo.phone?CryptoJS.Decrypt(baseInfo.phone):''
      //   if(occupationalInfo.occupationalShowFlag==='1'){
      //     if(honorList.length!=0){
      //       if(honorList.length<4||honorList.length===4){
      //         this.setData({
      //           height:500
      //         })
      //       }else if(honorList.length>4&&honorList.length<9){
      //         this.setData({
      //           height:556
      //         })
      //       }else if(honorList.length>8){
      //         this.setData({
      //           height:615
      //         })

      //       }
      //     }else{
      //       this.setData({
      //         height:400
      //       })
      //     }
      //   }else{
      //     if(honorList.length!=0){
      //       if(honorList.length<4||honorList.length===4){
      //         this.setData({
      //           height:420
      //         })
      //       }else if(honorList.length>4&&honorList.length<9){
      //         this.setData({
      //           height:490
      //         })
      //       }else if(honorList.length>8){
      //         this.setData({
      //           height:566
      //         })

      //       }
      //     }else{
      //       this.setData({
      //         height:400
      //       })
      //     }

      //   }
       
      //   this.setData({
      //     baseInfo,
      //     occupationalInfo,
      //     honorList:newArray
      //   })
      // } else {
      
      // }
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    previewImage: function (e) {  
      var current=e.target.dataset.src;
      console.log(current);
      wx.previewImage({
        showmenu:true,
          current: current, // 当前显示图片的http链接
          urls: [current] // 需要预览的图片http链接列表
      })
    },

    async  onClickShow (e) {
      console.log('--✨🍎', 12321321312321)
      const that = this;
      if(this.data.audit=='1') return
      let userType = wx.getStorageSync('userType')
      let salesCode = wx.getStorageSync('salesCode')
      that.setData({
        userType,
        salesCode,
      })
      if (userType===2 && !salesCode) {
        // this.triggerEvent('showSalesPop')
        Dialog.confirm({
          context: this,
          selector:'#myDialog',
            title: '提示',
            message: '请先绑定营销员',
          }).then(() => {
            //  this.triggerEvent('onClickHide')
              wx.navigateTo({
                url: '/pages/userinfo/userinfo',
              })
          }).catch(() => {
            // this.triggerEvent('onClickHide')
        });
        return 
      }
      const res = await API.queryByEmpNo()
      console.log(res);
      if(res.data.code === 200){
        let {baseInfo,occupationalInfo,honorList} = res.data.data.content
        let newArray =  honorList&&honorList.filter(item=> item.medalReachFlag === '1')
        newArray&&newArray.map((item,index)=>{
   
          // this.data.badgeList.map((list)=>{
          //   if(item.medalName===list.medalName){
          //     item['url'] = list.medalImageUrl
          //   }
          // })
          wx.getImageInfo({
            src: item.medalImageUrl,
            success: (res) => {
              console.log(res.path)
              item.medalImageUrl = res.path
            }
          });
        
        })
        console.log(newArray);
        baseInfo.phone = baseInfo.phone?CryptoJS.Decrypt(baseInfo.phone):''
        if(occupationalInfo&&occupationalInfo.occupationalShowFlag==='1'){
          if(honorList.length!=0){
            if(honorList.length<4||honorList.length===4){
              this.setData({
                height:550
              })
            }else if(honorList.length>4&&honorList.length<9){
              this.setData({
                height:606
              })
            }else if(honorList.length>8){
              this.setData({
                height:665
              })
            }
          }else{
            this.setData({
              height:450
            })
          }
        }else{
          if(honorList&&honorList.length!=0){
            if(honorList.length<4||honorList.length===4){
              this.setData({
                height:470
              })
            }else if(honorList.length>4&&honorList.length<9){
              this.setData({
                height:540
              })
            }else if(honorList.length>8){
              this.setData({
                height:566
              })

            }
          }else{
            this.setData({
              height:450
            })
          }
        }
        this.setData({
          baseInfo,
          occupationalInfo,
          honorList:newArray||[]
        })

        //获取相册授权
        wx.getSetting({
          success: (result) => {
            // console.log(result, 'result')
            if (!result.authSetting['scope.writePhotosAlbum']) {
              wx.authorize({
                scope: 'scope.writePhotosAlbum',
                success: (e) => {
                  //这里是用户同意授权后的回调
                  that.drawPoster()
                },
                fail () {
                  // console.log('这里是用户拒绝授权后的回调')
                  wx.showModal({
                    title: '提示',
                    content: '若不打开授权，则无法将图片保存在相册中！',
                    showCancel: true,
                    cancelText: '去授权',
                    cancelColor: '#000000',
                    confirmText: '暂不授权',
                    confirmColor: '#3CC51F',
                    success: function (res) {
                      if (res) {
                        wx.openSetting({
                          //调起客户端小程序设置界面，返回用户设置的操作结果。
                        })
                      } else {
                        // console.log('用户点击取消')
                      }
                    }
                  })
                }
              })
            } else {
              that.drawPoster();
            }
          },
          fail: () => { },
          complete: () => { }
        });
        this.triggerEvent('clickDownPoster')
      } else{


      }
      
    },
    getCodeImg () {
      const that = this
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: that.data.codeUrl,
          success: (res) => {
            console.log(res);
            resolve(res.path)
          }
        });
      })
    },
    // 获取头像
    getHeadImg () {
      const that = this
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: that.data.baseInfo.photo||that.data.headUrl,
          success: (res) => {
            console.log(res);
            resolve(res.path)
          }
        });
      })
    },
     // 获取奖品框
    getBoxImg () {
      const that = this
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: that.data.boxImg,
          success: (res) => {
            // console.log(res);
            resolve(res.path)
          }
        });
      })
    },

    getBannerImg () {
      const that = this
       console.log('海报',that.data.banner);
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: that.data.banner,
          success: (res) => {
             console.log(res);
            resolve(res.path)
          }
        });
      })
    },
    drawPoster () {
      if (drawing) {
        wx.showToast({
          title: '绘制中……',
          icon: 'loading',
          duration: 3000
        });
      } else {
        drawing = true;
        setTimeout(() => {
          this.drawPosterOne()
        }, 400)
      }
    },
    async drawPosterOne () {
     
      wx.showLoading({
        title: '绘制中...',
      })
      
      let ctx = wx.createCanvasContext('canvasposter', this);
      ctx.clearRect(0, 0, 343, 613);
     
      // 绘制背景色
      ctx.fillStyle = "#fff";
      ctx.fillRect(0, 0, 343, 613);


      ctx.fillStyle = "#fff";
      ctx.fillRect(0, 400, 343, this.data.height-330);

      // console.log('开始绘制banner图片--✨🍎')

      // 绘制banner
      if (this.data.banner !== '') {
        const path = await this.getBannerImg()
        console.log('banner图片--✨🍎', path)
        ctx.drawImage(path, 0, 0, 343, 400)
      } else {
        ctx.fillStyle = "#fff";
        ctx.fillRect(0, 0, 343, 400);
      }
      //绘制渐变色  
      let grd = ctx.createLinearGradient(0,245,0,400);//开始的坐标、结束坐标
      grd.addColorStop(0, 'rgba(255, 255, 255, 0.1)') //渐变开始颜色
      grd.addColorStop(1, 'rgba(255, 255, 255, 1)')//渐变结束的颜色
      ctx.setFillStyle(grd)
      ctx.fillRect(0,245,343,155)
      
     
      // 业务员信息背景图
     ctx.drawImage(this.data.salesImg, 16, 266, 311, 100)
      //绘制二维码
      if (this.data.codeUrl != '') {
        const res = await this.getCodeImg()
        ctx.drawImage(res, 242, 276, 60, 60)
      }

      // 绘制客户经理工号
      ctx.setFontSize(16);
      ctx.setFillStyle('#17204D');
      ctx.font = 'normal bold 16px PingFang SC'
      ctx.textAlign = "left";
      ctx.fillText(this.data.baseInfo.empName, 77, 320)

      ctx.setFontSize(12);
      ctx.setFillStyle('#555C80');
      ctx.textAlign = "left";
      ctx.fillText(this.data.baseInfo.phone, 77, 343)
      ctx.save()

      // 客户经理头像
      const path = await this.getHeadImg();
      ctx.arc(51, 326, 18, 0, 2 * Math.PI) // 将图片绘制到圆上
      ctx.fill(); //填充背景
      ctx.clip() 
      console.log('头像图片--✨🍎', path)
      ctx.drawImage(path, 33,308, 36, 36)
      ctx.restore()
      if(this.data.occupationalInfo&&this.data.occupationalInfo.occupationalShowFlag==='1'){
        // 业绩框
        for (let j = 0;j < this.data.list.length;j++) {
          let img = this.data.boxImg
          await ctx.drawImage(img,16+(j*81),382,69,54);
          ctx.restore();
        }
        // 数量
        ctx.setFontSize(16);
        ctx.textAlign = "center";
        ctx.setFillStyle('#17204D');
        ctx.fillText(this.data.occupationalInfo.workYears,51,406);
        // 从业经验
        ctx.setFontSize(10);
        ctx.setFillStyle('#7E849F');
        ctx.textAlign = "center";
        ctx.fillText(this.data.list[0].name, 51,426);

        // 数量
        ctx.setFontSize(16);
        ctx.textAlign = "center";
        ctx.setFillStyle('#17204D');
        ctx.fillText(this.data.occupationalInfo.serviceCustomeNum,133,406);
        ctx.setFontSize(10);
        ctx.setFillStyle('#7E849F');
        ctx.textAlign = "center";
        ctx.fillText(this.data.list[1].name, 133,426);

        ctx.setFontSize(16);
        ctx.setFillStyle('#17204D');
        ctx.textAlign = "center";
        ctx.fillText(this.data.occupationalInfo.insuredAmount,215,406);
        ctx.setFontSize(10);
        ctx.setFillStyle('#7E849F');
        ctx.textAlign = "center";
        ctx.fillText(this.data.list[2].name+this.data.occupationalInfo.insuredUnit, 215,426);

        ctx.setFontSize(16);
        ctx.setFillStyle('#17204D');
        ctx.textAlign = "center";
        ctx.fillText(this.data.occupationalInfo.policyNum,293,406);
        ctx.setFontSize(10);
        ctx.setFillStyle('#7E849F');
        ctx.textAlign = "center";
        ctx.fillText(this.data.list[3].name+this.data.occupationalInfo.policyUnit, 293,426);
      }
      console.log(this.data.honorList)
      if(this.data.honorList&&this.data.honorList.length!=0&&this.data.occupationalInfo&&this.data.occupationalInfo.occupationalShowFlag==='1'){
        // 徽章标题
        ctx.drawImage(this.data.titleImg,124,440, 95, 14)
        // 徽章列表
        for (let i = 0;i < this.data.honorList.length;i++) {
          let img = this.data.boxImg
          let logo = this.data.honorList[i].medalImageUrl

          if( i<4 ){
            ctx.drawImage(img,16+(i*82),481,69,54);
            ctx.restore();
            ctx.drawImage(logo,30+(i*82.5),486,41,48);
            ctx.restore();
          }else if( i>3&&i<8 ){
            ctx.drawImage(img,16+((i-4)*82),546,69,54);
            ctx.restore();
            ctx.drawImage(logo,30+((i-4)*82.5),551,41,48);
            ctx.restore();
          }else if(i>7){
            ctx.drawImage(img,16+((i-8)*82),611,69,54);
            ctx.restore();
            ctx.drawImage(logo,30+((i-8)*82.5),616,41,48);
            ctx.restore();

          }
        }
      }

      if(this.data.honorList&&this.data.honorList.length!=0&&(!this.data.occupationalInfo||this.data.occupationalInfo.occupationalShowFlag!='1')){
        // 徽章标题
        ctx.drawImage(this.data.titleImg,124,380, 95, 14)
        // 徽章列表
        for (let i = 0;i < this.data.honorList.length;i++) {
          let img = this.data.boxImg
          let logo = this.data.honorList[i].medalImageUrl
          if(i<4){
            ctx.drawImage(img,16+(i*82),410,69,54);
            ctx.restore();
            ctx.drawImage(logo,30+(i*82.5),415,41,48);
            ctx.restore();
          }else if(i>3&&i<8){
            ctx.drawImage(img,16+((i-4)*82),475,69,54);
            ctx.restore();
            ctx.drawImage(logo,30+((i-4)*82.5),480,41,48);
            ctx.restore();
          }else if(i>7){
            ctx.drawImage(img,16+((i-8)*82),540,69,54);
            ctx.restore();
            ctx.drawImage(logo,30+((i-8)*82.5),545,41,48);
            ctx.restore();
          }
        }

      }
      

      ctx.draw();
      wx.hideLoading()

      const that = this
      setTimeout(() => {
        wx.canvasToTempFilePath({
          canvasId: 'canvasposter',
          success: function (res) {
            var tempFilePath = res.tempFilePath;
            that.setData({
              loadImagePath: tempFilePath,
            });
            that.saveImageToPhotosAlbum(res.tempFilePath)
          },
          fail: function (err) {
            console.log('err', err);
          }
        }, this);
      }, 700);
     
    },

    saveImageToPhotosAlbum: function (imgUrl) {
      let that = this
      console.log(imgUrl,'保存成功');
      if (imgUrl) {
        wx.saveImageToPhotosAlbum({
          filePath: imgUrl,
          success: (res) => {
            wx.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 2000
            })
            drawing = false
          },
          fail: (err) => {
            wx.showToast({
              title: '保存失败',
              icon: 'none',
              duration: 2000
            })
            drawing = false
          }
        })
      } else {
        wx.showToast({
          title: '绘制中……',
          icon: 'loading',
          duration: 3000
        })
      }
    },
    onClickHide () {
      this.triggerEvent('onClickHide')
      
    },
  }
})
