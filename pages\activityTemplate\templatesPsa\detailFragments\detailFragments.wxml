<template name="detail_Fragments">
  <view class="{{item.type=='detail-area'?'activity_detail_contentPsa':'activity_detail_contentsPsa'}}" style="{{item.type=='detail-area'?item.styleStr:''}}">
    <!-- <view class="jj">
      <view>
        <image src="{{item.titleOptions.leftIcon ? item.titleOptions.leftIcon : 'https://txqmp.cpic.com.cn/uploads/img/activity_bargain_detail_left.png'}}"></image>
      </view>
      <view>活动详情</view>
      <view>
        <image src="{{item.titleOptions.rightIcon ? item.titleOptions.rightIcon : 'https://txqmp.cpic.com.cn/uploads/img/activity_bargain_detail_right.png'}}"></image>
      </view>
    </view> -->
    <view class="activity_detail_timeOptions">
      <view wx:for="{{item.timeOptions}}" class="activity_detail_timeOptions_item" wx:key="index" wx:for-item="ele">
        <view wx:if="{{ele.type === 'activity-time'}}" style="color: {{ele.style.color}};font-size: {{ele.style.fontSize}}px;" class="activity_detail_timeOptions_item_time">活动时间: {{ activityTime }}</view>
        <rich-text nodes="{{ele.propValue}}" class="ql-editor activity_detail_timeOptions_item_text" wx:else></rich-text>
      </view>
    </view>
    <block wx:for="{{item.options}}" wx:key="index">
      <block wx:if="{{item.type === 'detail-text'}}">
        <view class="activity_detail_editor">
          <rich-text nodes="{{item.propValue}}" class="ql-editor"></rich-text>
        </view>
      </block>
      <block wx:if="{{item.type === 'detail-picture'}}">
        <image class="goods_list" mode="widthFix" src="{{item.url}}"></image>
      </block>
      <block wx:if="{{item.type === 'detail-video'}}">
        <video id="myVideo" autoplay object-fit='fill' wx:if="{{item.url !== ''}}" src="{{item.url}}" show-center-play-btn='true' show-play-btn="true" controls></video>
      </block>
    </block>
  </view>
</template>