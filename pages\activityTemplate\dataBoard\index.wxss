
  .table_content{
    margin: 20rpx 0;
  }
  page{
    background: #F2F3F5;
  }
  .content--back{
    height: 620rpx;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }

  .navTitle .nav_menu{
    color:#fff;
    font-family:PingFang SC-Medium;
    font-weight: 400;
    font-size: 34rpx;
  }
  .tabBar{
    width: 544rpx;
    height: 72rpx;
    border-radius: 614rpx 614rpx 614rpx 614rpx;
    border: 2rpx solid rgba(255,255,255,0.2);
    margin: 0 auto 34rpx;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .tabBar view{
    width: 160rpx;
    height: 68rpx;
    border-radius: 614rpx 614rpx 614rpx 614rpx;
    line-height: 68rpx;
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #FFFFFF;
  }
  .closeOverlay{
    position: absolute;
    left: 50%;
    margin-left: -26rpx;
    bottom: -94rpx;
    z-index: 9999;
    color: #fff;
  }
  .tabBar view:nth-child(2){
    margin: 0 30rpx;
  }
  .tabBar .activeBar{
    background: #FFFFFF;
    font-weight: 400;
    font-size: 32rpx;
    color: #025CEA;
  }
  .attention{
    color: #025CEA;
  }
  .dataContent{
    position: relative;
    padding: 24rpx 32rpx;
    background: #fff;
    background: linear-gradient( 180deg, #F8FAFF 0%, #F2F3F5 27%);
    border-radius: 32rpx 32rpx 0rpx 0rpx;

  }
  .contentTopImg{
    position: absolute;
    width: 100%;
    height: 300rpx;
    top: 0;
    left: 0;
    z-index: -1;
  }
  .filterBox{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64rpx;
    margin-bottom: 24rpx;
    color: #17204D;
  }
  .dataTime{
    display: flex;
    font-size: 28rpx;
    line-height: 32rpx;
    font-family: PingFang SC, PingFang SC;
  }
  .yearMonth{
    margin-right: 10rpx;
  }
  .selectYear{
    vertical-align: middle;
    margin-left: 8rpx;
    margin-top: -6rpx;
  }
  .activeTopic{
    width: 240rpx;
    height: 64rpx;
    line-height: 64rpx;
    font-size: 24rpx;
    font-family: PingFang SC, PingFang SC;
    padding-left: 20rpx;
    position: relative;
    border-radius: 1276rpx 1276rpx 1276rpx 1276rpx;
    border: 2rpx solid #E9E9ED;
    box-sizing: border-box;
  }
  .activeTopic span{
    display: inline-block;
    width: 170rpx;
    white-space: nowrap; 
    overflow: hidden;
    text-overflow:ellipsis;
  }
  .arrowDown{
    position: absolute;
    top: 20rpx;
    right: 20rpx;
  }
  .companyBox{
    min-width: 188rpx;
    position: relative;
    height: 64rpx;
    line-height: 64rpx;
    font-size: 24rpx;
    font-family: PingFang SC, PingFang SC;
    background: linear-gradient( 90deg, rgba(68,146,252,0.1) 0%, rgba(2,92,234,0.1) 100%);
    font-weight: 400;
    border-radius: 1276rpx 1276rpx 1276rpx 1276rpx;
    color: #025CEA;
    box-sizing: border-box;
    padding:  0 20rpx;
    display: flex;
    justify-content: space-between;
    
  }
  .companyBox span{
   min-width: 100rpx;
   margin-right: 10rpx;

  }
  .companyBox .arrowRight{
    vertical-align: middle;
  }
  .centerContent{
    width: 622rpx;
    height: 124rpx;
    background: #F8FAFF;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    margin-bottom: 32rpx;
    padding: 24rpx 0;
    box-sizing: border-box;
    display: flex;
  }
  .registration, .verification{
    width: 310rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #7E849F;
    box-sizing: border-box;
  }
  .line{
    width: 2rpx;
    height: 100%;
    background: #d6d6d6;
    
  }
  .registration{
    padding-left: 24rpx;
  }
  .verification{
    padding-left: 42rpx;
  }
  .peopleNum{
    font-weight: 400;
    font-size: 28rpx;
    color: #17204D;
    margin-top: 16rpx;
  }
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .scrollView{
    overflow: hidden;
    height: 630rpx;
  }
  .block {
    width: 622rpx;
    height: 732rpx;
    background-color: #fff;
    border-radius: 32rpx;
    padding: 28rpx 0 48rpx;
    font-family: PingFang SC-Medium;
    box-sizing: border-box;
    position: relative;
    /* overflow-y: scroll; */
  }
  .showData{
    width: 686rpx;
    height: 410rpx;
    background: linear-gradient( 180deg, #E6EEFF 0%, #FFFFFF 32%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 2rpx solid rgba(248.11742931604385, 250.73937982320786, 255, 1);

    padding: 32rpx;
    box-sizing: border-box;
    margin-bottom: 32rpx;
    font-size: 24rpx;
  }
  .showDataTop{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

  }
  .showDataTopTitle{
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #7E849F;
    margin-bottom: 24rpx;
  }
  .activityNum{
    font-family: D-DIN, D-DIN;
    font-weight: 700;
    font-size: 48rpx;
    color: #17204D;
    display: flex;
    align-items: center;
  }
  .activityNum span{
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #555C80;
    display: inline-block;
    margin-left: 8rpx;
    height: 30rpx;
    line-height: 30rpx;
  }
  .activityNum span .playIcon{
    margin-left: 2rpx;
    vertical-align:bottom;
  }
.showTableData{
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;

}
.tableTitle{
  width: 160rpx;
  height: 36rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: #17204D;
  line-height: 36rpx;
  margin-bottom: 24rpx;
}




