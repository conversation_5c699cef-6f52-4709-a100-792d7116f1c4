
<view wx:for="{{tree}}" wx:key="index" class="tree">
    <view class="tree-item tree-item-select">
      <view class="tree-item-onOff" wx:if="{{item.children && item.children.length > 0}}"  catchtap="isOpen" data-index="{{index}}"> 
        <image src="/image/triangle.png" class="{{item.open ? 'tree-item-onOff-open' : 'tree-item-onOff-closed'}}"></image>
      </view>
      <view class="tree-item-onOff" wx:else>
      </view>
      <view class="tree-item-name {{selectKey == item.id ? 'tree-item-name-select' : '' }}" catchtap="select" data-item="{{item}}" data-index="{{index}}">
        <view class="name">{{item.name}}</view>
        <view class="img">
          <!-- <image wx:if="{{selectKey == item.id }}" src="/assets/icon/u435.svg"></image> -->
        </view>
      </view>
    </view>
    <c-tree
      wx:if="{{item.children && item.children.length > 0 && item.open }}"
      dataTree='{{ item.children }}'
      selectKey="{{selectKey}}"
      isSelectLastNode="{{isSelectLastNode}}"
      isOpenAll="{{isOpenAll}}"
    >
    </c-tree>
  </view>
