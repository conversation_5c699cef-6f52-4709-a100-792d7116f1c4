<view class="lottery_rule" style="background-image: url({{backgroundImg}});height:{{totalHeight.toFixed(0)<1334?1334:totalHeight.toFixed(0)}}rpx">
  <view class="rule_main" style="height:{{height}}rpx">
    <block wx:for="{{list}}" wx:key="index">
      <view class="rule_time" wx:if="{{item.type == 'ruleTime' && qtnIdType === 'false'}}" style="top:{{item.style.top*2}}rpx;left: {{item.style.left*2}}rpx;">
        <view class="rule_desc_title" style="margin-bottom: 10rpx;">{{qtnIdType === 'false' ? '1':''}}.活动时间:</view> 
        <view>{{start}} - {{end}}</view>
      </view>
      <view class="rule_text" wx:if="{{item.type == 'rule'}}" style="top:{{item.style.top*2}}rpx;left: {{item.style.left*2}}rpx;">
        <view class="rule_desc_title" style="margin-top: 8rpx;">{{qtnIdType === 'false' ? '2':'1'}}.活动规则</view>
        <view class="jj_content" wx:if="{{content !== ''}}">
          <rich-text nodes="{{content}}"  class="ql-editor"></rich-text>
        </view>
      </view>
      <view wx:if="{{item.type == 'rulePrize'}}" style="top:{{item.style.top*2}}rpx;left: {{item.style.left*2}}rpx;" class="reward">
        <view class="rule_desc_title" style="margin-top: 20rpx;">{{qtnIdType === 'false' ? '3':'2'}}.活动奖品</view>
        <view class="reward_list">
          <view class="reward_item" wx:for="{{prizeList}}" wx:key="index">
            <view class="reward_img"><image src="{{item.url}}"></image></view>
            <view>{{item.prizeName}}</view>
            <!-- <view>份数：{{item.place}}</view> -->
          </view>
        </view>
      </view>
    </block>
  </view>
</view>
<!-- 活动审核显示底部操作按钮 -->
<audit activityId="{{activityId}}" activityType="12" wx:if="{{audit}}" bind:reject="reject"></audit>
<!-- 活动审核显示底部操作按钮 -->
<!-- 活动审核驳回弹框 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" activityType="12" rejectReason="{{rejectReason}}" audit="{{auditStatus}}" financeStatus="{{financeStatus}}" linkTimes='2'></reject-reason>
<!-- 活动审核驳回弹框 -->