// pages/activityTemplate/submitSuccess/submitSuccess.js
Page({
  /**
   * 组件的属性列表
   */
  data: {
    drawTrigger:0,
    activityId:0

  },
  onLoad(options) {
    console.log(options);
    let list = JSON.parse(wx.getStorageSync('result')) 
     this.setData({
      list,
      qtnId: options.qtnId*1,
      activityId:options.drawId*1,
      drawTrigger:options.type //是否关联抽奖
    })
   
  },
  onShow(){

  },
  _clickEvent(){
    if(this.data.drawTrigger == '1'){
      wx.navigateTo({
        url: `/pages/activityTemplate/lottery/lottery?activityId=${this.data.activityId}&qtnId=${this.data.qtnId}&mode=QUES`,
      })
    }else{
      wx.reLaunch({
        url: '/pages/home/<USER>',
      })
    }

  },



})
