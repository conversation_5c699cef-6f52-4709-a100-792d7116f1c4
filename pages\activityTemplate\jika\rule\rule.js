const app = getApp();
const API = require('../../../../api/request.js')
const CryptoJS = require('../../../../utils/CryptoJs')
const http = require('../../../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    rejectReason: '',
    showReject: false,

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad (options) {
    console.log(options);
    this.setData({
      audit: options.audit,
      activityId: options.id
    })
    this.getCollectDetail(options.id)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow () {

  },
  //活动详情
  async getCollectDetail (activityIds) {
    const that = this
    let res = null
    if (this.data.audit) {
      res = await API.auditCollectDetail({
        id: activityIds
      })
    } else {
      res = await API.cardCollectDetail({
        activityId: activityIds
      })

    }
    if (res.data.code == 200) {
      let { data } = res.data
      console.log(res, JSON.parse(data.activity.activityPageConfig), 'res1221123456');
      that.setData({
        financeStatus: data.activity.financeStatus || '',
        auditStatus: data.activity.auditStatus || '',
      })
      const activityPageConfig = data.activity.activityPageConfig ? JSON.parse(data.activity.activityPageConfig).find((item) => item.title === '活动细则') : null

      if (activityPageConfig !== undefined && activityPageConfig !== null) {
        const reg = new RegExp('TXQImgPath/', 'ig');
        const dynamicDomainName = wx.getStorageSync('dynamicDomainName')
        const _activityPageConfig = JSON.stringify(activityPageConfig).replace(reg, dynamicDomainName)

        const activityPageData = JSON.parse(_activityPageConfig)
        if (activityPageData.componentData && activityPageData.componentData.length > 0) {
          activityPageData.componentData.map(item => {
            item['styleStr'] = that.handlerStyle(item.style)
            if (item.type == 'backgroundImg') {
              if (typeof (item.style.height) == 'string')
                item['styleStr'] = item['styleStr'] + `height:${item.style.height}`
            }
          })
        }
        that.setData({
          activityPageConfig: activityPageData
        })
      }

    } else {
      wx.showToast({
        title: res.message,
        icon: "none"
      })
    }

  },
  reject () {
    this.setData({
      showReject: true,
      rejectReason: ''
    })
  },
  handlerStyle (style) {
    console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom () {

  }


})