
.backArrow{
  position: fixed;
  width: 100%;
  /* text-align: left; */
  top: 20rpx;
  left:40rpx;
  color: #fff;
  font-size: 36rpx;
  display: inline-block;
  line-height: 27.2px;
  z-index: 98;
}
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 105;
  opacity: 0.7;
}
.mask1{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 106;
  opacity: 0.7;
}
.dliag-view{
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  position: fixed;
  width: 100%;
  height: 100%;
  align-items: center;
}
.contain{
  background-size: 100% auto;
  background-repeat: no-repeat;
}
.imageStyle{
  position: absolute;
  top:0;
  left: 0;
  background-size: 100% auto;
  background-repeat: no-repeat;
}
.img0{
  animation: myfirst1 3s linear;
  opacity: 0;
}
.img1{
  animation: myfirst2 2s linear;
  opacity: 1;
}
@keyframes myfirst1
        {
        0% {
            opacity: 1;
        }
        50%{
            opacity: 0.5;
        }
        100% {
            opacity: 0;
        }
}
@keyframes myfirst2
        {
        0% {
            opacity: 0;
        }
        50%{
            opacity: 0.5;
        }
        100% {
            opacity: 1;
        }
}
.dliagImg{
  margin: 10rpx;
  width: 102rpx;
  height: 150rpx;
}
.modalDlg{
  width: 80%;
  height: 300rpx;
  position: fixed;
  top: 55%;
  left: -1%;
  z-index: 1000;
  margin: -370rpx 85rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: space-between; */
  overflow: hidden; 
}
.modalDlg>text{
  font-size:30rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height:180rpx;
  width:100%;
  font-weight: bold;
}
.modalDlg>view>button{
  width:500rpx;
  height:80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #FF564AFF;
  background-color:#FF564AFF;
  color: #fff;
}
.modalDlgPop{
  /* width: 80%; */
  position: absolute;
  width: 100%;
  height: 100vh;
  z-index: 111;
  text-align: center;
  display: flex;
  justify-content: center;
  /* align-items: center; */
}
.modalDlgPop1{
  /* width: 80%; */
  position: absolute;
  width: 100%;
  height: 100vh;
  z-index: 111;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.modalDlg1{
  /* width: 80%;
  top: 52%;
  left: -1%;
  z-index: 1001;
  margin: -370rpx 85rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden; */
  position: fixed;
  width: 100%;
  height: 1010vh;
}
.modaImg{
  width: 70%;
  margin-top: 6%;
}
.modaImg1{
  width: 560rpx;
  height: 780rpx;
  position: absolute;
  /* display: flex;
  justify-content: center;
  align-items: center; */
  /* margin-top: 60rpx; */
}
.ql-editor{
  height: auto !important;
  text-align: center;
  font-size: 32rpx;
}
.modaImgclose{
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 58rpx;
}
.sureBtn{
  position: absolute;
  color: #fff;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  
}
.modabtn{
  position: absolute;
  margin-top: 40rpx;
  width: 60%;
  color: #fff;
  background: linear-gradient(to bottom, #EC6363,#BC2E2E);
}
.helpImg{
  /* position: absolute; */
}
.helpBox{
display: flex;
justify-content: center;
align-items: center;
position: relative;
width: 560rpx;
height: 540rpx;
/* margin: 400rpx auto 0; */

}
.dialogTitle{
  position: absolute;
  /* color: #fff; */
  font-size: 20px;
}
.modabtns{
  position: absolute;
  margin-top: 109%;
  width: 60%;
  color: #fff;
  background: linear-gradient(to bottom, #EC6363,#BC2E2E);
}
.modalDlg2{
  width: 80%;
  position: fixed;
  top: 52%;
  left: -1%;
  z-index: 1000;
  margin: -370rpx 85rpx;
  /* background-color: #fff; */
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* overflow: hidden;  */
}
.dialog-img {
  display: flex;
  justify-content: center;
  text-align: center;
  flex-wrap: wrap;
}
.dialog-img-v{
  position: relative;
}
.dialog-img-v-t{
  position: absolute;
  top: 7rpx;
  right: 13rpx;
  color: #fff;
  background-color: red;
  border-radius: 48%;
  margin: 4rpx 8rpx;
}
.giveBtn{
  margin-top: 14rpx;
  width: 88rpx;
  height: 44rpx;
  font-size: 24rpx;
  line-height: 42rpx;
  /* color: #fff !important; */
  border-radius: 22rpx;
  /* background-color: #298f82 ; */
  /* box-shadow: #fff 0px 0px 2px 1px inset; */
  background-color: transparent;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.dialog-img image{
  width: 130rpx;
  display: inline-block;
  margin: 20rpx;
  height: 260rpx;
}
page{
  /* background-color: #ccc; */
}
.top-img image{
  width: 100%;
  height: 780rpx;
  position: relative;
  z-index: 1;
}

.rule {
  position: absolute;
  text-align: center;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  /* color: #EFEFFF; */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
/* .onCard{
  z-index: 2;
  position: relative;
  top: -174rpx;
  text-align: center;
}
.onCard image{
  width: 423rpx;
  height: 131rpx;
}
.onCard .onCard-txt{
  display: flex;
  justify-content: center;
  text-align: center;
  margin-top: -106rpx;
  font-size: 37rpx;
  color: #fff;
} */
.explain{
  position: absolute;
  z-index: 1;
  text-align: center !important;
  font-size: 27rpx;
  /* color: #FCFCFA; */
}
.fff{
  /* color:#fff; */
  font-size: 30rpx;
}
.footer-img{
  position: relative;
}
.img-footer{
  z-index: -1;
  /* margin-top: -50rpx; */
  width: 100%;
  height: 1039.02px;
}

.card {
  position: absolute;
  width: 100%;
  top: 0rpx;
  display: flex;
  /* padding-left: 10rpx; */
  padding-right: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

}
.card .card-img{
  position: relative;
  width: 102rpx;
  margin: 34rpx 8rpx 10rpx 10rpx;
  text-align: center;
}
.card-img-zs{
  width: 88rpx;
  height: 44rpx;
  margin-top: 20rpx;
}
.card-img .txt{
  border: 2rpx solid #fff;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  align-items: center;
  position: absolute;
  right: -6px;
  top: -8px;
  z-index: 3;
  background-color: linear-gradient(to bottom, #FF8A79,#E7373F);
  color: #fff;
  font-size: 24rpx;
  border-radius: 50%;
}
.card .card-img .card-img-content{
  width: 100%;
  height: 142rpx;
}
.active{
  padding: 2rpx;
  border: 2rpx solid #000;
}
.time{
  text-align: center;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.mt20{
  margin-top: 20rpx;
}
.time{
  color: #F82420 ;
  font-size: 24rpx;
  margin-top: 12rpx;
  font-weight: 100;
}
.time_main{
  height: 60rpx;
  /* line-height: 80rpx; */
  /* color: #fFF; */
  text-align: right;
  padding-right: 20rpx;
  text-align: center;
  /* margin-bottom: 20rpx; */
  position: absolute;
  top: 300rpx;
  width: 100%;
  font-size: 24rpx;

}
.time_main span{
  background: #fff;
  border-radius: 6rpx;
  font-size: 24rpx;
  /* font-weight: 600; */
  /* color: #E73833; */
  margin:6rpx;
  min-width:46rpx;
  height:46rpx;
  display: inline-block;
  vertical-align: middle;
  line-height: 46rpx;
  text-align: center;
}

.btn_img{
  width: 230rpx;
  height: 92rpx;
}
.shareBtn{
  position: absolute;
  width: 251rpx;
  top: 100px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;


}
.btn{
  position: absolute;
  width: 100%;
  top: 412rpx;
  display: flex;
  justify-content: center;
}
.btn1{
    position: absolute;
    left: 50%;
    margin-left: -126rpx;
    FONT-WEIGHT: 500;
    width: 100%;
    display: flex;
    justify-content: center;
}
.assist{
  position: fixed;
  padding: 14rpx 20rpx 15rpx 24rpx;
  right: 0;
  background-color: #ccc;
  opacity: 0.75;
  border-top-left-radius: 27rpx;
  border-bottom-left-radius: 27rpx;
  z-index:2;
}
.ml50{
  margin-left: 50rpx;
}
.prizeBox{
  /* overflow: scroll; */
  position: absolute;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  padding:180rpx 60rpx 40rpx 70rpx;
  box-sizing: border-box;
  left: 50%;
  margin-left: -351rpx;
 
}
.prizeImg{
  width: 100%;
  height: 100%;
}
.content-prize{
  width: 246rpx;
  height: 308rpx;
  /* margin-bottom: 40rpx; */
  position: relative;
}
.marginBotton{
  margin-bottom: 40rpx;

}
.prizeAttribute{
  position: absolute;
  top: 0;
  left: 58rpx;
  width: 126rpx;
  text-align: center;
  font-size: 24rpx;
  color: #FFE7BA;
}
.prizeAttribute1{
  font-size: 28rpx;
  text-align: center;
  /* color: #FFE7BA; */
  margin-top: 20rpx;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.formatImg2{
  width: 140rpx;
  height: 142rpx;
  position: absolute;
  top: 60rpx;
  left: 56rpx;
}
.formatImg1{
  width: 210rpx;
  height: 213rpx;
  margin: 0 auto 10rpx;
  display: block;
}
.formatName2{
  position: absolute;
  width: 141rpx;
  height: 43rpx;
  line-height: 43rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  bottom: -6rpx;
  left: 54rpx;
  font-size: 24rpx;
  text-align: center;
  color: #FFE7BA;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}


