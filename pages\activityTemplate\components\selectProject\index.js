Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showParamModal:{
      type: Boolean,
      value: false
    },
    bannerType:{
      type: Boolean,
      value: false
    },
    VSwiper:{
      type:[Array,Object],
      value:[]
    },  
    itemObject:{//选中的项目
      type:Object,
      value:{}
    },
    commdityConfig:{
      type:Object,
      value:{}
    },
    commdityDialog:{
      type:Object,
      value:{}
    },
    projectArray: {
      type: Array,
      value: []
    },
    limitTextConfig:{
      type:Object,
      value:{}

    }
 
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentIndex:1
 
  },
  lifetimes: {
  
  },
  observers: {
    

  },
  /**
   * 组件的方法列表
   */
  methods: {
    selectProject(e){
      console.log(this.data.commdityDialog);
      let itemObject = e.currentTarget.dataset.item
      
      this.setData({
        itemObject,
        currentIndex:1
      })
      this.triggerEvent('selectProject',itemObject)
    },
    sure(){
      console.log(this.data.itemObject);
      this.triggerEvent('sure',this.data.itemObject)
    },
    closePopup(){
      this.setData({
        showParamModal: false,
      });
    }

  }
})
