// pages/subpages/dataBoard/index.js
const app = getApp();
const CryptoJS = require('../../../utils/CryptoJs.js');
const utils = require('../../../utils/util')
import http from '../../../api/request'
Page({

  /**
   * 页面的初始数据
   */
  data: {

    topicList:[
      {
        "name": "全部",
      }
    ],
    tabBarList:[{name:'日指标',id:0},{name:'周指标',id:1},{name:'月指标',id:2}],
    showTopic:false,
    showCascader:false,
    showCompany:false,
    tabBarId:0,
    selectCompanyId: '', //选中的节点id,选择的机构id
    companyName:'',
    activityTopic:'',
    selectWeek:'',
    currentYear:new Date().getFullYear(),
    currentMonth:new Date().getMonth()+1,
    currentDay:new Date().getDate(),

    maxDate:new Date().getTime(),
    currentDate:new Date().getTime(),
    minDate:new Date(2019, 12, 1).getTime(),
    fieldNames: {
      text: 'name',
      value: 'name',
      children: 'child',
    },
    cascaderValue:'',
    showSelect:true,//多选
    selectedCode:[],
    selectedArry:'',
    // options: [{enumName:'全部',enumValue:'-1'},{enumName:'选项A',enumValue:'a'},{enumName:'选项B',enumValue:'b'},{enumName:'选项C',enumValue:'c'},{enumName:'选项B',enumValue:'d'},{enumName:'选项C',enumValue:'e'}],
    rows :[],
    headers: ['创建机构', '活动数', '报名人数', '核销人数'],
    keys: ['companyName', 'activityNum', 'applyNum', 'writeOffNum',],
    tableConfig: {
      columnWidths: ['200rpx','100rpx','120rpx','120rpx'],
      border: true,
      scroll: false,
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.currentRole()
    this.getListBySales()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.setData({ navBarData: app.globalData.navBarData })

  },
  async getDetailInfo(){
    let startTime = ""
    let endTime = ""
    if(this.data.tabBarId === 0){
      startTime = this.data.currentYear+'-'+(this.data.currentMonth<10?'0'+this.data.currentMonth:this.data.currentMonth)+'-'+ (this.data.currentDay<10?'0'+this.data.currentDay:this.data.currentDay)
      console.log(startTime);
      endTime = this.data.currentYear+'-'+(this.data.currentMonth<10?'0'+this.data.currentMonth:this.data.currentMonth)+'-'+ (this.data.currentDay<10?'0'+this.data.currentDay:this.data.currentDay)
    }else if(this.data.tabBarId === 1){
      if(this.data.selectWeek){

      }
      let week = this.data.selectWeek.split("~")
      let start = week[0].split('-')
      let end = week[1].split('-')
      startTime = this.data.currentYear+"-"+(start[0]<10?'0'+ start[0]:start[0])+"-"+(start[1]<10?'0'+ start[1]:start[1])
      endTime = this.data.currentYear+"-"+(end[0]<10?'0'+ end[0]:end[0])+"-"+(end[1]<10?'0'+ end[1]:end[1])
    }else{
      startTime = this.data.currentYear+'-'+(this.data.currentMonth<10?'0'+this.data.currentMonth:this.data.currentMonth)
      endTime = this.data.currentYear+'-'+(this.data.currentMonth<10?'0'+this.data.currentMonth:this.data.currentMonth)

    }
    let data = {
      queryCate:this.data.tabBarId.toString(),
      endTime,
      startTime,
      activityClass:this.data.activityClass?this.data.activityClass:'',
      activityTopic:this.data.activityTopic?this.data.cascaderValue:'',
      topicType:this.data.topicType?this.data.topicType:'',
      companyId:this.data.selectCompanyId

    }
    wx.showLoading({ title: '加载中' })
    let res = await http.dataShow(data)
    let table = await http.dataShowTable(data)
    console.log(res,table);
    if(res.data.code ===200){
      wx.hideLoading()
      let {data} = res.data
      this.setData({
        activityNum:data.activityNum,
        applyNum:data.applyNum,
        writeOffNum:data.writeOffNum,
      })
    }
    if(table.data.code === 200){
      this.setData({
        rows:table.data.data
       
      })

    }

  },


  async removeEmptyListsAsync (data) {
    function removeEmptyListsSync (data) {
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        if (Array.isArray(item.child) && !item.child.length) {
          delete item.child;
        } else if (Array.isArray(item.child)) {
          removeEmptyListsSync(item.child);
          if (item.child.length === 0) {
            delete item.child;
          }
        }
      }
      return data
    }
    return Promise.resolve(removeEmptyListsSync(data));
  },

  async getListBySales () {
    const res = await http.getListBySales()
    let { data, code, message } = res.data
    if (code !== 200) return this.showTips(message)
    if (data && data.length) {
      const list = await this.removeEmptyListsAsync(data);
      let topicList = [{name:'全部'}].concat(list)
      this.setData({ topicList })
    }
  },

  // async getListByCode(){
  //   let res = await http.getConfigure({code:"activity.type.topic.config"})
  //   console.log(res);
  //   let {data,code,message} = res.data
  //   console.log(JSON.parse(data));
  //   if(code!==200) return  wx.showToast({
  //     title: message,
  //     icon: 'none',
  //     duration: 2000,
  //   })
  //   if(data) {
  //     let list = [{name:'全部'}].concat(JSON.parse(data))
  //     console.log(JSON.parse(data),list);
  //     this.setData({
  //       topicList:list
  //     })
  //   }
  // },
  async currentRole(){
    let that = this
    let res = await http.currentRole()
    if (res.data.encryptData) {
      res = JSON.parse(CryptoJS.Decrypts(res.data.encryptData))

      if (res.code === 200) {
        that.setData({
          selectCompanyId: res.data.companyId, //选中的节点id,选择的机构id
          companyName:res.data.company.name,
          level:res.data.company.level
        })
        this.getDetailInfo()
        this.getCompanyList(res.data.companyId)
      }
    }
  },
    
  //获取分公司数据
  getCompanyList (officeCompanyId) {
    let data = {
      parentId:officeCompanyId
    }
    http.getTreeList(data).then(res => {
      let { code, data: companyList } = res.data
      if (code === 200) {
        if (companyList && companyList.length) {
          this.setData({ dataTree: companyList })
        }
      }
    })
  },

  handleSelect(e) {
    console.log(e.detail.item);
    this.setData({
      selectCompanyId: e.detail.item.id,
      companyName: e.detail.item.name,
    })
    this.getDetailInfo()
    
  },
  // 返回上一个页面
  backPage () {
    wx.navigateBack({ url: -1 })
  },
  selectTabBar(e){
    console.log(e.target.dataset.id);
    this.setData({
      tabBarId:e.target.dataset.id
    })
    this.getDetailInfo()
  },
  yearMonth(){
    this.setData({
      showYearMonth:true
    })
  },
  confirmWeek(value){
    console.log(value.detail);
    this.setData({
      selectWeek:value.detail.currentWeek,
      selectWeekNumber:value.detail.currentNumber,
    })
    this.getDetailInfo()
  },
  firstWeek(value){
    console.log(value.detail);
    this.setData({
      selectWeek:value.detail.currentWeek,
      selectWeekNumber:value.detail.currentNumber,
    })
  },
  confirmDate(value){
    let date = new Date(value.detail)
    let currentYear = date.getFullYear(); 
    let currentMonth = date.getMonth() + 1;
    let currentDay = date.getDate();
    console.log(currentYear,currentMonth,currentDay);
    this.setData({
      currentYear,
      currentMonth,
      currentDay,
      showYearMonth:false,
      selectWeek:''
    });
    if(this.data.tabBarId!=1){
      this.getDetailInfo()
    }
    

  },
  cancel(){
    this.setData({
     showYearMonth:false
    });
  },
  goStatistics(){
    wx.navigateTo({
      url: '/pages/subpages/activityCount/activityCount',
    })

  },

  // 活动主题
  // sureData(e){
  //   console.log(e.detail);
  //   let currentVale = e.detail.arrayValue.length&&e.detail.arrayValue.map(item=>{return item.enumName}).join(',')
  //   console.log(currentVale);
  //   this.setData({
  //     showTopic:false,
  //     selectedCode:e.detail.code,
  //     activityTopic:currentVale,
  //   })
  // },
  selectTopic(){
    this.setData({
      showCascader:true
    })
  },
  selectedTopic(e){
    console.log(e.detail);
    let {fieldValue,selectedOptions,cascaderValue} = e.detail
    if(selectedOptions.length ==1){
      this.setData({
        activityTopic:'',
        activityClass:'',
        topicType:'',
        cascaderValue:cascaderValue,
      })
    }else{
      this.setData({
        activityTopic:fieldValue,
        activityClass:selectedOptions[0].name,
        topicType:selectedOptions.length===2?selectedOptions[0].name:selectedOptions[1].name,
        cascaderValue:cascaderValue,
      })
    }
   
    this.getDetailInfo()
  },
  // 选择机构
  selectCompany(){
    this.setData({ showCompany: true});

  },
  onClickHide() {
    this.setData({ showCompany: false});
  },
  closeTopic(){
    this.setData({
      showTopic:false,
    })
  },


  
})