// pages/component/authorizationPop/index.js
const app = getApp()
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: '参与活动'
    },
    show: {
      type: Boolean,
      value: false,
      observer: function (newVal) {
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleTap () {
      wx.showLoading({ title: '加载中' })
      app.getUserInfo(res => {
        console.log('--✨🍎', res)
        wx.hideLoading()
        this.triggerEvent('authPopTap', res)
      })
    }
  }
})