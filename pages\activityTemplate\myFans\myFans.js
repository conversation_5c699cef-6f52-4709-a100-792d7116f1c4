// pages/subpages/myFans/myFans.js
const API = require('../../../api/request.js');
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    lordFansList: [],// 粉丝列表
    lordFaPage: 1,  //粉丝页数
    lordFaSize: 10,  // 粉丝条数

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getLordFansList(1)

  },

  // 社区详情粉丝列表(主管版...)
  getLordFansList(page) {
    let data = {
      current: page || this.data.lordFaPage,
      pageSize: this.data.lordFaSize
    }
    wx.showLoading({
      title: '加载中...',
    })
    API.fansList(data)
      .then(res => {
        if (res.data.code == 200) {
          wx.hideLoading()
          // console.log("社区主管详情粉丝列表",res.data.data)
          let records = res.data.data.records
          if (records.length > 0) {
            if (page === 1) {
              this.setData({ lordFansList: records,total:res.data.data.total })
            } else {
              let lordFansList = this.data.lordFansList
              lordFansList.push(...records)
              this.setData({ lordFansList, total:res.data.data.total })
            }
          }
        } else {
          
          app.showToast(res.data.message)
        }
      })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.lordFansList.length >= this.data.total) return 
    this.setData({
      lordFaPage: this.data.lordFaPage + 1
    })
    this.getLordFansList()
    
  },

})