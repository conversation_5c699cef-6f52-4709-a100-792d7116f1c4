/* pages/editemaninfo/editemaninfo.wxss */
/* 实现textarea高度自适应且提示折行 */
textarea{
  text-align: right;
    /* height: 400rpx; */
    /* min-height:30rpx; */
    /* overflow: none; */
}
page{
  background:#F2F3F5;
}
.content{
  width: 622rpx;
  /* padding-bottom: 200rpx; */
  /* padding-top: 20rpx; */
  /* border-radius: 16rpx; */
  background-size: cover;
  background-repeat: no-repeat;
  
}
.topTitle{
  font-size: 32rpx;
  font-family: PingFang SC-粗体, PingFang SC;
  font-weight: normal;
  color: #17204D;
  line-height: 32rpx;
  font-weight: bold;
  /* background: #fff; */
  padding: 50rpx 0 28rpx 32rpx;
}
.topTitleFilp{
  padding: 260rpx 36rpx 0;
  font-size: 12px;
  color: #7E849F;
}
.placeHolderText{
  color: #999;
}
.content .van-cell{
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  align-items: center;
}
.content  .van-cell__value{
  color: #000;
}
.weui-cell,.radioGroup{
  display: flex;
  justify-content:flex-end;
}
.radioGroup .weui-cell:first-child .weui-cell__bd{
  margin-right: 25rpx;
}
.mask {
  position:absolute;
  z-index:50;
  width:750rpx;
  height:100vh;
  /* height: calc( 100% - 80rpx ); */
  /* background:rgb(233, 220, 220); */
  background-color:rgba(131, 120, 120, 0.4);
  /* background:transparent; */
  border:none;
  border-radius:0;
  margin: 0;
}
.spinner {
  /* margin: 100px auto 0; */
  margin: 50vh auto;
  /* width: 750px; */
  text-align: center;
  /* background-color: #fff; */
}

.spinner > view {
  width: 40rpx;
  height: 40rpx;
  background-color: #dd2b2b;
  border-radius: 100%;
  display: inline-block;
  animation: bouncedelay 1.4s infinite ease-in-out;
  animation-fill-mode: both;
}

.spinner .bounce1 {
  /* -webkit-animation-delay: -0.32s; */
  animation-delay: -0.32s;
}
 
.spinner .bounce2 {
  /* -webkit-animation-delay: -0.16s; */
  animation-delay: -0.16s;
}

@keyframes bouncedelay {
  0%, 80%, 100% {
    transform: scale(0.0);
    /* -webkit-transform: scale(0.0); */
  } 40% {
    transform: scale(1.0);
    /* -webkit-transform: scale(1.0); */
  }
}
.cardItembz{
    display: flex;
    padding:0 32rpx;
    justify-content: space-between;
  }
.card{padding-top: 16rpx;}

.cardItem{
  display: flex;
  padding:0 40rpx;
  justify-content: space-between;
  align-items: center;
}
.van-radio__label--right{
  margin-right: 10rpx;
}
.title{height: 30rpx;line-height: 30rpx;font-size:30rpx;font-family:PingFang SC;font-weight:500;color: #333333;display: flex;flex-wrap: wrap;margin-top: 30rpx;}
.title1{font-size:28rpx;font-family:PingFang SC;font-weight:500;color: #333333;}
.truename1{margin-top: 26rpx;text-align: right;}
  
.truename1img2{width: 26rpx; height: 26rpx;}

.truename1s{
  height: 30rpx;
  line-height: 30rpx;
  font-size:28rpx;
  font-family:PingFang SC;
  font-weight:500;
  z-index: 100;
  width: 400rpx;
}
.truename1sbz{
    height: 0rpx;
    line-height: 30rpx;
    font-size:28rpx;
    font-family:PingFang SC;
    font-weight:500;
    z-index: 100;
    word-wrap:normal;
}
.xian{width:100%;height:2rpx;background-color:#E5E5E5;margin-top: 30rpx;}
.btn0{
  width: 450rpx;
  box-sizing: border-box;
  height: 96rpx;
  font-size: 32rpx;
  color: #FFFFFF;
  z-index: 333;
  font-weight: bold;
  border: none;
  background: linear-gradient(90deg, #4492FC 0%, #025CEA 100%);
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-origin: 0 0;
  background-size: cover;
  background-repeat: repeat;
}
/* .btn0::after{border: none} */
.van-field__body--ios {
  margin-top: 0rpx;
  text-align: right;
}
.footerBox{
  width: 100%;
  margin: 0 auto;
  /* background: #fff; */
  padding: 40rpx;
  box-sizing: border-box;
}
.switch{
  border-top: 20rpx solid #F5F5F5FF;
  background: #fff;
  border-radius: 10rpx;
}
.address{
  position: relative;
  padding: 30rpx;
  border-radius: 15rpx;
  padding-top: 40rpx;
  background: #fff;
}
.required:before {
  position: absolute;
  content: "*";
  left: var(--padding-xs,15rpx);
  top: var(--padding-xs,23rpx);
  font-size: var(--cell-font-size,28rpx);
  color: var(--cell-required-color,#ee0a24)
}
.addressTitle{
  display: flex;
  justify-content: space-between;
  border-left: 4rpx solid #FF564AFF;
  padding-left: 10px;
  color: #666666FF;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.addressContent{
  position: relative;
  border-top: 1px solid #f5f5f5;
  margin: 30rpx 0 20rpx;
  padding-top: 20rpx;
  color: #666666FF;
  font-size: 26rpx;
}
.toEdit{
  position: absolute;
  right: 2rpx;
  top:18rpx;
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
}
.leftCircle{
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  background:#F6F6F7FF;
  left: -46rpx;
  top: -15rpx;
  border-radius: 50%;
  z-index: 9;
}
.rightCircle{
  position: absolute;
  width: 32rpx;
  height: 33rpx;
  background: #F6F6F7FF;
  right: -46rpx;
  top: -15rpx;
  border-radius: 50%;
  z-index: 9;
}
.consignee{
  font-size: 36rpx;
  color: #333333FF;
  font-weight: bold;
  margin-right: 20rpx;
}
.titleAddr{
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #000;
}
.van-cell-text{
  display: flex;
  justify-content: space-between;
  color: #333333FF;
  font-size: 28rpx;
}
.nameAddr{
  font-size: 32rpx;
  font-weight: bold;
}
.phoneNum{
  margin-right: 60rpx;
}
.imgUpLoad{
  /* border-bottom: 2rpx solid #ebedf0; */
  color: #323233;
  font-size: 28rpx;
  background: #fff;
  padding: 32rpx;
  padding-left: 32rpx;
  padding-right: 18rpx;
  padding-bottom: 18rpx;
}
.mainTop {
  padding: 0rpx 4rpx 12rpx;
}
.topBtn{
  display: flex;
  justify-content: space-between;
  font-size: 32rpx;
  padding: 20rpx 30rpx;
  position: fixed;
  z-index: 999;
  width: 100%;
  box-sizing: border-box;
  background: #fff;
}
.surePop{
  color: #FF564AFF;
}
.bottomText{
  color:#666;
  text-align: center;
  margin: 40rpx 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
}