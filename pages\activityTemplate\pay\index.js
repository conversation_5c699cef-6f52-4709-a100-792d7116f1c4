const http = require('../../../utils/util.js');
const CryptoJS = require('../../../utils/CryptoJs.js');
const API = require('../../../api/request.js');
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    order:1235677890,
    number:3,
    money:"$500",
    canPay:true

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let payData =JSON.parse(decodeURIComponent(options.payDataStr)) 
    console.log(payData);
    this.setData({
      orderNo:payData.orderNo,
      number:payData.num,
      money:payData.amount,
      timeStamp:payData.timeStamp,
      nonceStr:payData.nonceStr,
      packageStr:payData.package,
      sign:payData.signType,
      paySign:payData.paySign,
      backUrl:payData.backUrl,


    })
    this.getHeight()

  },

  // 返回上一个页面
  backPage () {
    wx.navigateBack({ url: -1 })
  },
  async surePay(){
    let {sign,orderNo,nonceStr,packageStr,paySign,canPay} = this.data
    if(!canPay) return
    this.setData({
      canPay:false
    })
    let data = {
      orderNo,
    }
    let that = this
    let res = await API.pushRecord(data)
    console.log(res,'状态修改')
    if(res.data.code == 200){
      that.topay(orderNo)
    }else{
      http.neterror(res.data.message, 2000)
      that.setData({
        canPay:true
      })
    }
  // this.topay(orderNo)
  },
  // 去支付
  topay (orderNo) {
    let that = this
    wx.showLoading({ title: '正在发起支付...' })
    wx.requestPayment({
      'timeStamp': that.data.timeStamp, // 时间戳
      'nonceStr': that.data.nonceStr,   //随机串
      'package': that.data.packageStr,     //package
      'signType': that.data.sign,                         //签名方式
      'paySign': that.data.paySign,     //签名
      'success': function (success) {
        wx.hideLoading()
        let data = {
          orderNo: orderNo
        }
        API.orderPaying(data)
          .then(res => {
            console.log('支付', res)
            return false
          })
          that.setData({
            canPay:true
          })
        wx.redirectTo({
          url: `/pages/activityTemplate/pay/webview/index?src=${encodeURIComponent(that.data.backUrl)}`,
        })
        // 返回后端支付成功 tip
      },
      'fail': function (fail) {
        wx.hideLoading()
        that.setData({
          canPay:true
        })
        let data = {
          orderNo:orderNo
        }
        console.error('pay fail', fail)
        API.cancelPay(data).then(res => {
          console.log('res支付失败:' + res.data);
          // http.neterror('支付失败', 1500)
          // setTimeout(() => {
          //   wx.redirectTo({
          //     url: `/pages/payResult/payResult?status=1`,
          //   })
          // }, 1000)
        })
        
      }
    })
     
   
  },
  getHeight () {
    let systemInfo = wx.getSystemInfoSync()
    let menuButton = wx.getMenuButtonBoundingClientRect()
    let menuHeight = menuButton.height
    let menuRight = systemInfo.screenWidth - menuButton.right
    let menuBotton = menuButton.top - systemInfo.statusBarHeight
    let navBarHeight = (menuButton.top - systemInfo.statusBarHeight) * 2 + menuHeight + systemInfo.statusBarHeight
    const navBarData = {
      navBarHeight,
      menuRight,
      menuBotton,
      menuHeight
    }
    this.setData({
      navBarData
    })
  },

})