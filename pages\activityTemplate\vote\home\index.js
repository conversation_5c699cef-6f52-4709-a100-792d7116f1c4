
const wxtimer = require('../../../../utils/time.js');
const http = require('../../../../utils/util.js');
const app = getApp();
const API = require('../../../../api/request.js')
const CryptoJS = require('../../../../utils/CryptoJs')
const { showTab, handleUserStorage } = require('../../../../utils/aboutLogin')

const { setSalesCode, getWhetherAuth } = require('../../../../utils/querySalesCode')
Page({
  data: {
    isShow:true,
    pageUrl:'pages/activityTemplate/vote/home/<USER>',
    linkType:'vote',
    showPoster:false,
    fullscreen: false,
    loadAnimation: true, // 入场动画
    userId: '',
    userType: '',
    isShowCanvassBtn: true, // 是否展示我要拉票按钮
    shareDirectly: false,
    products: 0, // 报名人数
    totalVote: 0, // 累计投票
    current: 1, // 当前页获取作品list,
    currentProductIndex: '', // 当前我的作品被选择的项index
    lapiaotip: '', // 当前我的作品被选择的项拉票宣言
    size: 10, // 一次性获取作品数量
    voteConfig: {}, // 和投票有关配置
    timeObj: {
      day: 1,
      hour: 1,
      points: 1,
      seconds: 1
    }, // 活动倒计时
    activityId: '', // 活动Id
    firstInPageTime: '', // 首次进入该页面时间
    leavePageTime: '', // 出该页面时间
    hasChanceTojoin: true, // 还有机会参与
    filters: '', // 搜索框绑定值
    isClose: true, // 登陆者身份框
    personLogin: false, // 登陆者身份框
    showModalDlg: false, // 遮挡框不能点击（未登录）
    myActivities: [], // 我的所有能拉票的活动
    voteList: [], // 所有投票作品,
    myActivities: [],
    tabActive: 1, // 控制tab栏下颜色
    orderColumn: 'RANK', // tab栏名
    isPageFinished: true, // 能否继续分页获取
    showPickDlg: false, // 拉票活动选取

    isShare: false,
    shareUserType: '',
    showSwitchSale: false,
    changeSalesCode: '',
    currentId: '',
    acceptId: '',
    audit:'', //活动审核判断
    showReject:false,
    rejectReason:'',
  },
  onLoad: function (options) {
    console.log('onload', options)
    var that = this
    let scene = options.scene
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      var id = option[0]
      that.setData({
        firstInPageTime: new Date().getTime(),
        activityId: id,
      })

    } else {
      that.setData({
        firstInPageTime: new Date().getTime(),
        // activityId: options.activityId,
        activityId: options.activityId,
        audit:options.audit || ''
      })
    }

    if (options.isShare) {
      this.setData({
        isShare: true,
        changeSalesCode: options.salesCode,
        changeSaleName: options.saleName,
        shareUserType: options.userType
      })
    }

    that.onBuriedPoint('votePageScan', '投票首页浏览')
    let userId = wx.getStorageSync('userId')
    let userType = wx.getStorageSync('userType')
    that.setData({
      userId,
      userType,
      shareUserId: options.shareUserId
    })
    that.getHeight()
  },
  getHeight () {
    let systemInfo = wx.getSystemInfoSync()
    let menuButton = wx.getMenuButtonBoundingClientRect()
    let menuHeight = menuButton.height
    let menuRight = systemInfo.screenWidth - menuButton.right
    let menuBotton = menuButton.top - systemInfo.statusBarHeight
    let navBarHeight = (menuButton.top - systemInfo.statusBarHeight) * 2 + menuHeight + systemInfo.statusBarHeight
    const navBarData = {
      navBarHeight,
      menuRight,
      menuBotton,
      menuHeight
    }
    this.setData({
      navBarData
    })
  },
  onShow: function () {
    console.log('onshow');
    var that = this
    if (http.userAre() == '') { // 用户未登录，要求先点击加入活动
      // 用户id判断用户是否登录（微信弹窗）
      that.setData({
        showModalDlg: true
      })
      return
    } else {
      that.setData({
        showModalDlg: false
      })
      if (wx.getStorageSync('refreshUserInfo')) {
        that.getUserInfo();
      } else {
        if (wx.getStorageSync('salesCode') || wx.getStorageSync('pcode')) {
          that.setData({
            salesCode: wx.getStorageSync('salesCode'),
            credits: wx.getStorageSync('credits'),
            type: wx.getStorageSync('userType'),
          })
        }
        that.getIsRegister()
      }
    }
    if (this.data.audit != 1) {
      wx.showShareMenu({
        withShareTicket: false,
      })
    } else {
      wx.hideShareMenu({})
    }
  },
  // 活动说明
  toActivityDetail () {
    let that = this
    if(that.data.audit == 1) return
    wx.navigateTo({
      url: `/pages/activityTemplate/vote/rule/index?activityId=${that.data.activityId}`,
    })
  },
  // 活动详情
  async activityDetail () {
    let that = this
    let data = {
      activityId: that.data.activityId
    }
    let res = null
    if(that.data.audit == 1){
      res = await API.auditVoteDetail({id:that.data.activityId})
      this.setData({
        isShow:false
      })
    }else {
      res = await API.voteDetail(data)
    }
    console.log('活动详情', res)
    if (res.data.code == '200') {
      var reg=new RegExp('TXQImgPath/','ig');
      let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
      let activityData  =  res.data.data
      let activityPageData = activityData.activityPageConfig&&activityData.activityPageConfig.replace(reg,dynamicDomainName)
      const activityPageConfig = activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '投票首页') : null
      if (activityPageConfig !== undefined && activityPageConfig !== null) {
        (activityPageConfig.componentData ? activityPageConfig.componentData : []).map((item) => {
          if (item.type === 'button' && (item.btEvent === 'vote' || item.btEvent === 'canvass')) {
            delete item.style.top
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        console.log('页面组件元素', activityPageConfig.componentData)
        const backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
        const voteNumConfig = activityPageConfig.componentData.find((item) => item.type === 'voteNum')
        const voteDescConfig = activityPageConfig.componentData.find((item) => item.type === 'voteDesc')
        const voteNotesConfig = activityPageConfig.componentData.find((item) => item.type === 'voteNotes')
        that.setData({
          backgroundImg: backgroundImg.propValue.url,
          componentData: activityPageConfig.componentData,
          voteNumConfig: voteNumConfig.style,
          activityPageConfig: activityPageData,
          voteDescConfig: voteDescConfig,
          imgOptions:voteNumConfig.imgOptions,
          voteNotesConfig:voteNotesConfig
        })
      }

      console.log(activityPageConfig)
      const activityPosterConfig = activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '专属海报') : null
      if(activityPosterConfig !== undefined && activityPosterConfig !== null){
        (activityPosterConfig.componentData ? activityPosterConfig.componentData : []).map((item) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        const posterBackgroundImg = activityPosterConfig.componentData.find((item) => item.type === 'posterPic')
        const posterText = activityPosterConfig.componentData.find((item) => item.type === 'text')
        that.setData({
          posterBackgroundImg,
          activityPosterConfig,
          posterText
        })
      }
     
      // 计算倒计时
      let nowtime = new Date().getTime() //目前时间
      let from_time1 = -1
      let from_time2 = -1
      if (res.data.data.voteConfig.mobileLimit === 1) {
        var joinbeginTime = res.data.data.applyStartTime // 报名开始时间
        var joinendTime = res.data.data.applyEndTime // 报名结束时间
        let beginTime1 = new Date(joinbeginTime.replace(/-/g, '/')).getTime()  // 报名开始时间时间戳
        let endTime1 = new Date(joinendTime.replace(/-/g, '/')).getTime() // 报名结束时间时间戳毫秒
        from_time1 = parseInt((nowtime - beginTime1) / 1000) //正数： 活动开始  负数：活动开始倒计时
        from_time2 = parseInt((endTime1 - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束
        console.log('报名倒计时', from_time2)
        console.log(from_time1)
      }
      var applyStartTime = res.data.data.voteConfig.voteBegin // 投票开始时间
      var applyEndTime = res.data.data.voteConfig.voteEnd // 投票结束时间
      let createTime = new Date(applyStartTime.replace(/-/g, '/')).getTime()  // 活动开始时间时间戳
      let endTime = new Date(applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒
      let sub_time1 = parseInt((nowtime - createTime) / 1000) //正数： 活动开始  负数：活动开始倒计时
      let sub_time2 = parseInt((endTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束
      // console.log(sub_time1, sub_time2)
      that.setData({
        sub_time1,
        sub_time2,
        from_time1,
        from_time2,
        voteConfig: res.data.data.voteConfig,
        contactCfg: res.data.data.contactCfg,
        canApply: res.data.data.canApply,
        totalVote: res.data.data.totalVote,
        products: res.data.data.products,
        flagConnect: res.data.data.flagConnect,
        activityInfo: res.data.data,
        financeStatus: res.data.data.financeStatus||'',
        auditStatus: res.data.data.auditStatus||''
      })
      var subtime = ""
      if (sub_time1 < 0) {
        subtime = parseInt((createTime - nowtime) / 1000)  //投票倒计时
      }
      if (from_time2 < 0 && this.data.audit != 1 && res.data.data.voteConfig.mobileLimit === 1) {
        that.setData({
          isFinish: true,
        })
        wx.showToast({
          title: "报名已结束！",
          icon: "none",
          duration: 1500,
        })
      }
      if (sub_time2 < 0 && this.data.audit != 1) {
        that.setData({
          isFinish: true,
        })
        wx.showToast({
          title: "投票已结束！",
          icon: "none",
          duration: 1500,
        })
      }
      if (sub_time1 > 0 && sub_time2 > 0) {
        subtime = parseInt((endTime - nowtime) / 1000) //进行中  投票结束倒计时
      }
      let timer = new wxtimer({
        complete: function () {
          if (sub_time1 < 0) { // 投票开始倒计时
            clearInterval(timer)
          } else if (sub_time1 > 0 && sub_time2 > 0) {
            that.setData({
              isFinish: true,
            })
            wx.showModal({
              title: '提示',
              content: '投票已结束',
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  wx.navigateBack({
                    delta: 1
                  })
                }
              }
            })
          }
        }
      });
      //  计时器
      timer.start(that, subtime, false);
      // 计算倒计时
    } else {
      wx.showToast({
        title: res.data.message,
        icon: 'none'
      })
    }
    that.setData({
      loadAnimation: false
    })

    if (this.data.isShare) {
      let salesCode = wx.getStorageSync('salesCode')
      let saleName = wx.getStorageSync('saleName')
      if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
        this.setData({ showSwitchSale: true, currentId: saleName + salesCode, acceptId: this.data.changeSaleName + this.data.changeSalesCode })
      }
    }
  },
  closeChangeTieModal () {
    this.setData({ showSwitchSale: false })
  },
  // 关闭专属海报弹框
  onClickHide(){
    this.setData({
      showPoster: false
    })
  },
  // 下载海报
  downPoster(){
    this.setData({
      showPoster: true
    })
  },
  handlerStyle (style) {
    // console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  onHide () {
    let that = this
    that.setData({
      leavePageTime: new Date().getTime(),
      currentProductIndex: '',
      current: 1,
      myActivities: [],
      showPickDlg: false,
      isPageFinished: true
    })
    let scanDuration = that.data.leavePageTime - that.data.firstInPageTime
    let day = Math.floor((scanDuration) / (86400000)); //天
    let hours = Math.floor((scanDuration % 86400000) / 3600000); //时
    let minutes = Math.floor((scanDuration % 3600000) / 60000); //分
    let seconds = Math.floor((scanDuration % 60000) / 1000); //秒
    let scanDuration1 = ''
    if (day > 0) {
      scanDuration1 = `${day}天${hours}时${minutes}分${seconds}秒`
    } else if (day == 0 && hours > 0) {
      scanDuration1 = `${hours}时${minutes}分${seconds}秒`
    } else if (hours == 0 && minutes > 0) {
      scanDuration1 = `${minutes}分${seconds}秒`
    } else if (minutes == 0 && seconds > 0) {
      scanDuration1 = `${seconds}秒`
    }
    // 埋点
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    app.sensors.track('votePageStayTime', {
      name: '投票首页停留时长',
      personScan: nickName,
      scanTime: scanDuration1,
      userId: userId
    });
    console.log('投票首页停留时长', scanDuration1, day, hours, minutes, seconds);
  },
  // 获取所有作品列表
  toGetVoteWorkList () {
    let that = this
    let data = {
      activityId: that.data.activityId,
      current: that.data.current,
      filters: that.data.filters,
      orderColumn: that.data.orderColumn,
      size: that.data.size,
    }
    API.voteProducts(data).then(res => {
      console.log('获取所有投票作品', res.data)
      if (res.data.code == '200') {
        var list = []
        res.data.data.records.map(item => {
          let mediaSrc = item.mediaType == "IMG" ? item.mediaSrc.split(',')[0] : item.mediaSrc
          console.log('mediaSrc', item.cfgInfo);
          let infoConfog = {}
          item.cfgInfo ? Array.isArray(JSON.parse(item.cfgInfo)) ? JSON.parse(item.cfgInfo).forEach((item) => {
            infoConfog = {
              ...infoConfog,
              ...item
            }
          }) : infoConfog = {
            ...JSON.parse(item.cfgInfo)
          } : ''
          let obj = {
            id: item.id,
            infoConfog,
            mediaSrc: mediaSrc,
            mediaType: item.mediaType,
            votedNum: item.votedNum,
            votedNumToday: item.votedNumToday,
            votes: item.votes,
            orders: item.orders
          }
          list.push(obj)
        })
        console.log('修改后的list', list);
        if (res.data.data.records.length > 0) {
          if (that.data.current > 1) {
            console.log('旧数据', that.data.voteList)
            console.log('薪数据', list)
            that.setData({
              voteList: that.data.voteList.concat(list),
            })
          } else {
            that.setData({
              voteList: list,
            })
          }
        } else {
          that.setData({
            voteList: list,
          })
        }
        console.log('voteList', that.data.voteList);
        that.setData({
          pages: res.data.data.pages, // 总共有多少页
          isPageFinished: Number(res.data.data.pages) > Number(that.data.current) ? false : true
        })
      }
    })
  },
  // 去投票详情页面
  toVote (e) {
    console.log('eeee--✨🍎', e)

    if(this.data.audit === 1) return
    console.log('去投票详情页面', e.target.dataset)
    let that = this
    that.onBuriedPoint('clickVote', '去投票详情页面')
    let productId = e.target.dataset.id
    const title = e.target.dataset.title
    wx.navigateTo({
      url: `/pages/activityTemplate/vote/detail/index?productId=${productId}&flagConnect=${that.data.flagConnect}&title=${title}`,
    })
  },
  // 埋点事件
  onBuriedPoint (name, title) {
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let time = new Date().toLocaleString()
    app.sensors.track(name, {
      name: title,
      personScan: nickName,
      scanTime: time,
      userId: userId
    });
  },
  // 人气排名
  popuRank () {
    console.log('人气排名')
    let that = this
    that.setData({
      tabActive: 1,
      orderColumn: 'RANK',
      current: 1,
      isPageFinished: true
    })
    that.toGetVoteWorkList()
  },
  // 最新参赛
  newJoin () {
    console.log('最新参赛')
    let that = this
    that.setData({
      tabActive: 2,
      orderColumn: 'TIME',
      current: 1,
      isPageFinished: true
    })
    that.toGetVoteWorkList()
  },
  // 我要报名
  async toJoin () {
    let that = this
    console.log(that.data.from_time1)
    const flag = await getWhetherAuth(this.data.activityId)
    if (!flag) return
    if (that.data.from_time1 < 0) {
      wx.showToast({
        title: '报名还未开始',
        icon: 'none'
      })
      return false
    } else if (that.data.from_time2 < 0) {
      wx.showToast({
        title: '报名已结束',
        icon: 'none'
      })
      return false
    } else if (that.data.from_time1 > 0 && that.data.from_time2 > 0) {
      that.onBuriedPoint('IwillJoin', '我要报名')
      let activityId = that.data.activityId
      wx.navigateTo({
        url: `/pages/activityTemplate/vote/registration/index?activityId=${activityId}`,
        success: function (res) {
          // 通过eventChannel向被打开页面传送数据
          console.log(res)
          res.eventChannel.emit('acceptDataFromOpenerPage', { data: that.data.contactCfg, voteConfig: that.data.voteConfig, activityPageConfig: that.data.activityPageConfig })
        }
      })
    }
  },
  // 选择作品去拉票
  toShare (e) {
    console.log('选择需要拉票的作品', e.target.dataset)
    let that = this
    that.setData({
      currentProductIndex: e.target.dataset.id,
      lapiaotip: e.target.dataset.lapiaotip
    })
  },
  // 关闭我的作品选择框
  toCloseActivityPick () {
    this.setData({
      showPickDlg: false,
      currentProductIndex: '',
      // lapiaotip: ''
    })
  },
  // 我的作品
  myProducts () {
    let that = this
    let data = {
      activityId: that.data.activityId
    }
    API.voteMyPublishetail(data).then(res => {
      console.log('我的作品', res)
      if (res.data.code == 200) {
        if (res.data.data.length > 0) {
          let list = res.data.data || []
          let myActivities = that.data.myActivities
          list.map(item => {
            let obj = {
              id: item.id,
              infoConfog: JSON.parse(item.cfgInfo)
            }
            myActivities.push(obj)
          })
          console.log('myActivities', myActivities)
          var myActivityLength = myActivities.length
          that.setData({
            myActivities: myActivities,
            isShowCanvassBtn: true,
            shareDirectly: myActivityLength == 1 ? true : false
          })
        } else {
          that.setData({
            isShowCanvassBtn: false,
            shareDirectly: false
          })
        }
      }
    })
  },
  // 我要拉票
  toCanvass (e) {
    console.log('我要拉票', e);
    let that = this
    that.setData({
      showPickDlg: true
    })
    that.onBuriedPoint('clickToCanvass', '点击我要拉票')
  },
  // 搜索框数据改变
  changeInput (e) {
    console.log('搜索框数据改变', e)
    let that = this
    that.setData({
      filters: e.detail.value,
      current: 1,
      isPageFinished: true
    })
    that.toGetVoteWorkList()
  },
  // 瀑布流触底
  scrollTolower () {
    console.log('瀑布流触底')
    let that = this
    if (that.data.isPageFinished) { // 没有更多数据了
      console.log('没有更多数据了');
      return
    } else {
      that.setData({
        current: that.data.current + 1
      })
      that.toGetVoteWorkList()
    }
  },
  // 身份选择框关闭
  close (e) {
    console.log(e)
    var that = this
    that.setData({
      personLogin: false,
    })
    wx.showToast({
      title: '请选择身份登录',
      duration: 2000,
      icon: "none",
    })
  },
  //用户微信登录授权(点击进入)
  userInfoHandler () {
    console.log('执行了用户微信登录')
    var that = this;
    wx.removeStorageSync('defaultPerson');
    wx.removeStorageSync('refreshUserInfo')
    app.getUserInfo(function (userInfo) {
      console.log(userInfo)
      wx.setStorageSync('userId', userInfo.id);
      wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
      wx.setStorageSync('nickName', userInfo.nikename);
      wx.setStorageSync('openId', userInfo.openId);
      wx.showToast({
        title: '授权成功',
        icon: "none",
        duration: 1500,
      })
      that.setData({
        userId: userInfo.id,
        isAuthorize: true
      })
    })
    var timer3 = setInterval(() => {
      if (http.userAre() != '') {
        that.getUserInfo();
        clearInterval(timer3);
      }
    }, 100)
  },
  // 查询是否注册
  getIsRegister (userId) {
    let that = this
    if (http.unRegister()) {
      // 没有注册
      // 查询绑定的业务员
      that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode,that.data.changeSaleName)
      that.setData({
        personLogin: true,
        showModalDlg: false
      })
    } else {
      showTab()
      that.setData({
        showModalDlg: false
      })
    }
    that.toGetVoteWorkList()
    that.myProducts()
    that.activityDetail()
  },
  //   获取用户信息
  getUserInfo () {
    let that = this;
    var userId = wx.getStorageSync("userId")
    var token = wx.getStorageSync("token")
    that.setData({
      headImgUrl: (wx.getStorageSync('headImgUrl') == '') ? '../../image/user.png' : wx.getStorageSync('headImgUrl'),
      nickName: (wx.getStorageSync('nickName') == '') ? '请点击登录' : wx.getStorageSync('nickName'),
    })
    // 获取用户所属门店信息
    if (token) {
      API.getInfoById().then(res => {
        console.log('获取用户所属门店信息', res)

        if (res.data.code == 200) {
          console.log(res.data)
          that.setData({
            credits: res.data.data.credits,
            type: res.data.data.type,
            salesCode: res.data.data.salesCode,
          })
          console.log(that.data.salesCode);

          handleUserStorage(res.data.data)
          if (wx.getStorageSync('userId') !== '') {
            console.log('判断是否注册过')
            // 判断是否注册过    
            that.getIsRegister(wx.getStorageSync('userId'))
          }
        }
      })
    }
  },


  /**
   * 用户点击右上角分享
   */
  onShareAppMessage (res) {
    if(this.data.audit) return
    console.log('用户点击分享按钮', res)
    let userId = wx.getStorageSync('userId')
    var that = this
    let path = ``
    let activityId = that.data.activityId
    let productId = ''
    let title = ''
    let imageUrl = ''
    let salesCode = wx.getStorageSync('salesCode')
    let userType = wx.getStorageSync('userType')
    let saleName = wx.getStorageSync('saleName')
    if (that.data.myActivities.length == 1) {
      productId = that.data.myActivities[0].id
      title = that.data.myActivities[0].infoConfog['拉票宣言'] ? that.data.myActivities[0].infoConfog['拉票宣言'] : '快来投我一票吧~'
    } else {
      console.log('拉票宣言', that.data.lapiaotip);
      productId = that.data.currentProductIndex
      title = that.data.lapiaotip ? that.data.lapiaotip : '快来投我一票吧~'
    }
    console.log('productId', that.data.myActivities, productId)
    if (res.from == 'menu') {
      const { shareText } = this.data.activityInfo
      console.log('shareText--✨🍎', shareText)
      if (shareText) {
        title = shareText.replace('#微信昵称#', wx.getStorageSync('nickName')).replace('#活动名称#', this.data.activityInfo.title)
      }

      // .replace('#作品名称#',wx.getStorageSync('nickName'))
      var reg=new RegExp('TXQImgPath/','ig');
      let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
      imageUrl = this.data.activityInfo.shareUrl.replace(reg,dynamicDomainName)
      // imageUrl = this.data.activityInfo.shareUrl
      console.log(imageUrl,this.data.activityInfo.shareUrl);
      path = `/pages/activityTemplate/vote/home/<USER>'userId')}&clientShareUserId=${userId}`
    } else if (res.from == 'button') {
      path = `/pages/activityTemplate/vote/detail/index?activityId=${activityId}&productId=${productId}&shareUserId=${wx.getStorageSync('userId')}&clientShareUserId=${userId}`
    }
    that.onBuriedPoint('voteShare', '投票分享')
    return {
      title: title,
      imageUrl: imageUrl,
      path: `${path}&salesCode=${salesCode}&saleName=${saleName}&isShare=1&userType=${userType}`,
    }
  },
  // 返回上一个页面
  backPage () {
    wx.navigateBack({ url: -1 })
  },
  reject() {
    console.log('触发了')
    this.setData({
      showReject:true,
      rejectReason:'',

    })
  }
})