const { countDetail } = require("../../../api/request");
const { formatDate } = require("../../../utils/util");

// pages/subpages/videoList/videoList.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list:[
      {
        zname:'浙江分公司',
        name:'与爱相伴 美好人生 司庆活动',
        url:'https://tb.yptech.tv/live/login/login?video_id=149249&share_code=8df54b4ad9608a2931ad433f8069ec15',
        date:'2022-05-13',
        monthDay:"05月13日",
        time:'18：00'
      },
    ]

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let that = this
    let list = that.data.list
    let nowDate = formatDate(new Date().getTime())
    list.map((item,key)=>{
      if(that.compareDate(item.date,nowDate) == true){
        item.isShow = true
      }else{
        item.isShow = false
      }
    })
    that.setData({
      list,
      nowDate
    })

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },
  compareDate(a,b){
    let a1 = a.split('-');
    let b1 = b.split('-');
    let c1 = new Date(a1[0],a1[1],a1[2])
    let c2 = new Date(b1[0],b1[1],b1[2])
    if(Date.parse(c1)-Date.parse(c2) == 0){
      return true
    }else{
      return false
    }
  },
  jumpVideo(e){
    console.log(e.currentTarget.dataset.url)
    let url = e.currentTarget.dataset.url
    wx.navigateTo({
      url: `/pages/activityTemplate/videoPage/videoPage?url=${encodeURIComponent(url)}`,
    })
  },

})