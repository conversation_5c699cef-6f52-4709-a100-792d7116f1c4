
<view class="content" wx:if="{{isShowBtn}}">
  <view class="downLoadBtn" bindtap="onClickShow" style="{{btnText.styleStr}};border-width: {{btnText.style.borderWidth ? btnText.style.borderWidth*2 : 0}}rpx;">
    <view>{{btnText.propValue}}</view>
  </view>
  
</view>
<canvas class='canvas-poster' canvas-id='canvasposter' style="height:{{height+5}}px"></canvas>

<van-overlay show="{{ show }}" z-index="99999999">
  <view class="contentPop">
    <view class="wrapper">
      <view class="wrapper-content" style="height:{{height*2}}rpx">
        <view class="popBox"></view>
        <image class="expert" src="{{banner}}" ></image>
        <!-- 徽章 -->
        <view class="saleContent" >
          <image src="https://txqmp.cpic.com.cn/uploads/img/postBg.png" class="bg"></image>
          <view class="containter">
            <view  class="saleInfo">
              <image src='{{baseInfo.photo||headUrl}}'  class="headImg"></image>
              <view>
                <view class="saleaName">{{baseInfo.empName}}</view>
                <view>{{baseInfo.phone}}</view>
              </view>
            </view>
            <image src='{{codeUrl}}' data-src="{{codeUrl}}"  class="ecode"></image>
          </view>
        </view>
        <view class="bottomContent">
          <view class="detail" wx:if="{{occupationalInfo&&occupationalInfo.occupationalShowFlag==='1'}}">
            <view class="event">
              <image src="/image/template/box.png" class="boxBg"></image>
              <view class="numText">{{occupationalInfo.workYears}}</view>
              <view>从业经验/年</view>
            </view>
            <view class="event">
              <image src="/image/template/box.png" class="boxBg"></image>
              <view class="numText">{{occupationalInfo.serviceCustomeNum}}</view>
              <view>服务客户/人</view>
            </view>
            <view class="event">
              <image src="/image/template/box.png" class="boxBg"></image>
              <view class="numText">{{occupationalInfo.insuredAmount}}</view>
              <view>客户保障/{{occupationalInfo.insuredUnit}}</view>
            </view>
            <view class="event">
              <image src="/image/template/box.png" class="boxBg"></image>
              <view class="numText">{{occupationalInfo.policyNum}}</view>
              <view>保单件数/{{occupationalInfo.policyUnit}}</view>
            </view>
          </view>
          <image src="/image/template/title.png" class="badgeTitle" wx:if="{{honorList.length!=0}}"></image>
          <view class="badge">
            <view class="event" wx:for="{{honorList}}" wx:key="index">
              <image src="/image/template/box.png" class="boxBg"></image>
              <image src="{{item.medalImageUrl}}" class="badgeImg"></image>
            </view>
          </view>
        </view>
       
      </view>
      <image src="../../../../image/closeImg.png" class="close" bindtap="onClickHide"></image>
     
    </view>
  </view>
</van-overlay>
<van-dialog id="myDialog" />
