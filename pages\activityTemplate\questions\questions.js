const app = getApp()
const API = require('../../../api/request')
import { unRegister } from '../../../utils/util';
const { setSalesCode } = require('../../../utils/querySalesCode');
const { showTab, handleUserStorage } = require('../../../utils/aboutLogin')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isShow: true,
    navBarData: app.globalData.navBarData,
    pageUrl: 'pages/activityTemplate/questions/questions',
    showReject: false,
    rejectReason: '',
    disagree: false,
    isClose: true,
    personLogin: false,
    showModalDlg: false,
    form: {},//提交参数
    list: [],
    activityId: 0,
    drawTrigger: 0,
    topDescs: '',
    isEdit: false,
    contentStyle: '',
  },

  async onLoad (options) {
    console.log(options);
    this.setData({
      activityId: options.id * 1,
      audit: options.audit,
      isEdit: options.isEdit === '1' ? true : false,
      orderConsumerId: options.orderConsumerId * 1 || '',
      batchNo: options.batchNo || ''
    })
    if (options.isShare) {
      this.setData({ clientShareUserId: options.clientShareUserId, activityId: options.id })
    }


  },
  async onShow () {
    const userId = wx.getStorageSync('userId')
    if (!userId) {
      this.setData({ showModalDlg: true })
    } else {
      const ret = await this.getUserInfo()
      if (!ret) return
      const flag = await this.getIsRegister()
      if (flag) {
        this.getQuestions()
      }
    }
  },
  // 多选框
  onChange (e) {
    console.log(e)
    let list = this.data.list
    let form = this.data.form
    let key = e.target.dataset.key //id
    let index = e.target.dataset.index //父级dex
    let value = e.detail
    list[index].defaultValue = value //修改默认值
    list[index].options.map((item) => {
      item.select = false
      value.map((value) => {
        if (value == item.id) {
          item.select = true
        }
      })
    })
    form[key] = value
    this.setData({
      form,
      list
    });
  },
  // 单选框
  onChanges (e) {
    console.log(e)
    let list = this.data.list
    let form = this.data.form
    let key = e.target.dataset.key //问题id
    let index = e.target.dataset.index //
    let value = e.detail //选项id
    list[index].defaultValue = value //修改默认值
    let options = list[index].options.filter((item) => {
      return value == item.id
    })
    list[index].options.map((item) => {
      item.select = false
      if (value == item.id) {
        item.select = true
      }
    })
    let option = {
      [value]: options[0].content
    }
    form[key] = option
    this.setData({
      form,
      list
    });
  },
  //输入框
  inputBlur (e) {
    console.log(e);
    let value = e.detail.value
    let key = e.target.dataset.key //key
    let index = e.target.dataset.index //
    let list = this.data.list
    let form = this.data.form
    list[index].defaultValue = value //修改默认值
    form[key] = value
    this.setData({
      list,
      form
    })
  },
  async getQuestions () {
    let that = this;
    let data = {
      activityId: that.data.activityId
    }
    let datas = {
      id: that.data.activityId * 1,
    }
    wx.showLoading({ title: '加载中' })
    let res = null
    if (this.data.audit) {
      res = await API.auditQuesDetail(datas)
      this.setData({
        isShow: false
      })
    } else if (this.data.isEdit) {
      data.orderConsumerId = this.data.orderConsumerId
      data.batchNo = this.data.batchNo
      res = await API.queryAnswerById(data)
    } else {
      res = await API.getInquireInfo(data)
    }
    wx.hideLoading()

    var reg = new RegExp('TXQImgPath/', 'ig');
    let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
    let activity = res.data.data.activity
    let activityData = activity.activityPageConfig && activity.activityPageConfig.replace(reg, dynamicDomainName)
    let activityPageConfig = activityData ? JSON.parse(activityData) : null
    // let activityPageConfig = JSON.parse(res.data.data.activity.activityPageConfig)

    if (res.data.code == 200) {
      that.setData({
        financeStatus: res.data.data.activity.financeStatus || '',
        auditStatus: res.data.data.activity.auditStatus || '',
      })
      let questionnairePage = activityPageConfig.find((item) => item.title === '问卷详情')
      let result = activityPageConfig.find((item) => item.title === '问卷提交')
      wx.setStorageSync('result', JSON.stringify(result))

      const backgroundImg = questionnairePage.componentData.find(item => item.type === 'backgroundImg')
      const componentData = questionnairePage.componentData.filter(item => item.type !== 'backgroundImg')

      let questions = JSON.parse(JSON.stringify(res.data.data.questions).replace(reg, dynamicDomainName))
      questions.forEach((item, index) => {
        if (item.type == 'MUTISELECT' || item.type == 'RADIO') {
          let imageUrl = item.options.findIndex((option) => { return option.url != '' })
          if (imageUrl != -1) {
            // 选项只要有一个有图片，就显示为图片类型的选项
            item['imageUrl'] = true
          } else {
            item['imageUrl'] = false
          }
          if (item.type == 'MUTISELECT' && that.data.isEdit) {
            item.defaultValue = item.defaultValue && item.defaultValue.split(",")
          }

        }

      })
      that.setData({
        contentStyle: 'background-image: url(' + backgroundImg.propValue.url + ')',
        activity: res.data.data,
        componentData: componentData.map(item => {
          let styleStr = `${that.handlerStyle(item.style)}position: absolute;`
          if (item.component === 'VButton') {
            const lineHeight = `line-height:${item.style.height * 2}rpx;`

            const bgStyle = item.bgStatus === 1 ? `background-image: url(${item.imgOptions.url});background-color: ''` : ''
            styleStr = item.type === 'poster'? `${styleStr}${bgStyle}` : `${styleStr}${lineHeight}${bgStyle}`
          }

          return {
            ...item,
            styleStr
          }
        }),
        drawTrigger: res.data.data.drawTrigger,
        list: questions,
        topDescs: res.data.data.topDescs,
        drawId: res.data.data.drawId,

      })

    } else {
      that.showTips(res.data.message)
    }

  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  handlerStyle (style) {
    console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }],
      ['padding', {
        name: 'padding',
        type: 1
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  inquireSubmit () {
    let that = this
    console.log(that.data.form);
    let isRequired = that.data.list.filter((list) => {
      return !list.defaultValue && list.needed == '1'
    })
    console.log(isRequired)
    if (isRequired && isRequired.length != 0) {
      wx.showToast({
        title: `${isRequired[0].title}为必填项！`,
        duration: 2000,
        icon: "none",
      })
      return
    }
    let data = {
      activityId: that.data.activityId,
      answers: that.data.form
    }
    wx.showLoading({ title: '加载中' })
    console.log(data);
    API.inquireSubmit(data).then((res) => {
      wx.hideLoading()
      if (res.data.code == 200) {
        console.log(res.data);
        wx.redirectTo({
          url: `/pages/activityTemplate/submitSuccess/submitSuccess?drawId=${that.data.drawId}&type=${that.data.drawTrigger}&qtnId=${that.data.activityId}`,
        })

      } else {
        that.showTips(res.data.message)
      }
    })

  },
  reject () {
    this.setData({
      showReject: true,
      rejectReason: ''
    })
  },
  // 获取用户信息
  async getUserInfo (token) {
    const res = await API.getInfoById()
    console.log('--获取用户信息✨✨', res)
    const { data, code, message } = res.data
    if (code == 200) {
      handleUserStorage(data)
    } else {
      this.showTips(message)
    }

    return new Promise((resolve, reject) => {
      if (code === 200) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  },

  // 判断是否注册过
  async getIsRegister () {
    let that = this
    return new Promise((resolve, reject) => {
      const res = !unRegister()
      if (!res) {
        resolve(false)
        that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode, that.data.changeSaleName)
        this.setData({ personLogin: true })
      } else {
        this.data.isAuthorize && showTab()
        resolve(true)
      }
    })
  },

  //用户微信登录授权
  async userInfoHandler () {
    app.getUserInfo(async (userInfo) => {
      wx.setStorageSync('userId', userInfo.id);
      wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
      wx.setStorageSync('nickName', userInfo.nikename);
      wx.setStorageSync('openId', userInfo.openId)
      this.setData({ userId: userInfo.id, isAuthorize: true })
      if (userInfo.id) {
        this.setData({ showModalDlg: false })
        const ret = await this.getUserInfo()
        if (!ret) return
        const res = await this.getIsRegister() // 判断是否注册过 
        if (res) {
          this.getQuestions()

        }
      }
    })
  },

  showTips (title, duration = 2000) {
    wx.showToast({
      title,
      duration,
      icon: "none",
      mask: true
    })
  },

  close () {
    this.showTips('请选择身份登录')
  },

})