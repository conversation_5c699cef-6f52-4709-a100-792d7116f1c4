// pages/activityTemplate/signIn/home/<USER>
const app = getApp();
const http = app.require('/utils/util.js');
const API = app.require('/api/request')
const CryptoJS = app.require('/utils/CryptoJs.js');
const { unRegister } = app.require('/utils/util');
const { showTab, handleUserStorage } = app.require('/utils/aboutLogin')
const { setSalesCode } = app.require('/utils/querySalesCode')
const { identity,identityEnum } = app.require('/utils/phone-idcard')
const { formatDate } = app.require("/utils/util");


let totalTime = 5
let clock = null

Page({

  /**
   * 页面的初始数据
   */
  data: {
    isShow:true,
    pageUrl:'pages/activityTemplate/signIn/home/<USER>',
    navBarData: app.globalData.navBarData,
    isShowTitle: true,
    // activity: {
    //   flagPoster: 1,
    //   qrcode: "https://txqmp.cpic.com.cn/uploads/img/bargain_active_picture6.png",
    //   title: "33344fff"
    // },
    activity: null,
    ids: "",
    category: 14,
    showPoster: false,
    activeTimeStart: '',
    activeTimeEnd: '',
    initDisplayDate: '',
    nowDate: '',
    fixTimes: 0,
    signTimes: 0,
    isShowAwardBtn: false,
    awarPrizeInfo: {},
    signinPrizeInfo: {},

    siginInDialogShow: false,
    isHasSigninPrize: true,
    repairDialogShow: false,
    awardingShow: false,

    showReject:false,
    audit:'',
    identityType: identityEnum,

    myLoadAnimation: false,
    reflashToast: false,
    reflashBtnContent: "",
    userInfoSuccess: false,
    detailSucess: false,

    tableHeaderTitle: ["奖品名称", "领取时间"],
    tableData: [],
    calendarSet: {},

    backgroundImg: "",
    posterBackgroundImg: {},
    signInPrizeStyle: "",
    activityRuleCom: [],
    dateText: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let scene = options.scene
    let that = this
    let userId = wx.getStorageSync('userId')
    let userType = wx.getStorageSync('userType')
    let saleName = options.saleName || ''
    let salesCode = options.salesCode || ''
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      let id = option[0]
      that.setData({
        ids: id,
        userType: userType,
        userId,
        scene: scene
      })

    } else {
      let id = options.id
      that.setData({
        ids: id,
        isShare: options.isShare || '',
        shareUserId: options.shareUserId || '',
        audit: options.audit || '',
        userType: userType,
        userId,
        changeSalesCode: salesCode,
        changeSaleName: saleName,
        shareUserType:options.userType || ''
      })
    }
  },

  onPageScroll({ scrollTop }){
    if(scrollTop <= 30 && !this.data.isShowTitle){
      this.setData({isShowTitle: true})
    }
    if(scrollTop > 30 && this.data.isShowTitle){
      this.setData({isShowTitle: false})
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (wx.getStorageSync('token')) {
      wx.showShareMenu({
        withShareTicket: false,
      })
    } else {
      wx.hideShareMenu({})
    }
    let that = this;
    let userId = wx.getStorageSync('userId')
    if (userId !== '') {
      if (wx.getStorageSync('refreshUserInfo')) {
        that.getUserInfo();
      } else {
        if (wx.getStorageSync('salesCode') || wx.getStorageSync('pcode')) {
          const cardType = wx.getStorageSync('cardType')
          const identityValue = that.data.identityType.filter((item) => item.value === cardType )
          console.log(identityValue)
          that.setData({
            type: wx.getStorageSync('userType'),
            salesCode: wx.getStorageSync('salesCode'),
            cardType:wx.getStorageSync('cardType'),
            identity:identityValue[0]?.name || '',
            idCard: wx.getStorageSync('idCard') ? wx.getStorageSync('idCard') : null,
            userType: wx.getStorageSync('userType'),
            credits: wx.getStorageSync('credits'),
            userInfoSuccess: true
          })
        }
        that.getIsRegister()
      }
    } else {
      that.setData({
        showModalDlg: true,
        InfoShow: true
      })
    }

    const nowDate = this.getActiveTime(new Date())
    this.setData({nowDate})
    
  },
  async getSignRecords(){
    const res = await API.findSignRecords({id: this.data.ids})

    this.setSignInData(res)
  },
  setSignInData(res){
    let dateText = []

    // 签到状态 0:未签到且不设置补签-缺 1：已签到-签  2:已补签-签 3:日期未到-空 4：待补签-日期已到，且设置补签,且在补签周期内-补 5：连续奖励-盒
    res.data.data.records.forEach(item => {
      if(item.signState === 0){
        // 缺卡
        dateText.push({
          value: item.signDate,
          text: "缺卡",
          textColor: "#7E849F",
          type: "tag",
          bgImg: "https://txqmp.cpic.com.cn/uploads/img/sign-calendar-softbg.png",
          tagBg: "#F2F2F5",
          tagColor: "#7E849F",
        })
      }
      if(item.signState === 1 || item.signState === 2){
        if(this.data.nowDate === item.signDate){
          // 设置今日标识
          this.isSetTodayTag = true
          dateText.push({
            value: item.signDate,
            isToday: true,
            text: "签到",
            numberBg: "#A95C0B",
            textColor: "#fff",
            bgImg: "https://txqmp.cpic.com.cn/uploads/img/sign-calendar-todaybg.png",
            type: "tag",
            tagBg: "linear-gradient(85deg, #FF9634 0%, #FD1505 100%)",
            signIntype: "hasSignin"
          })
        }else{
          dateText.push({
            value: item.signDate,
            text: "签到",
            type: "tag",
            bgImg: "https://txqmp.cpic.com.cn/uploads/img/sign-calendar-bg.png",
            tagBg: "linear-gradient(85deg, #FF9634 0%, #FD1505 100%)",
            signIntype: "hasSignin"
          })
        }
        
      }
      if(item.signState === 3){
        if(this.data.nowDate === item.signDate){
          // 设置今日标识
          this.isSetTodayTag = true
          dateText.push({
            value: this.data.nowDate,
            isToday: true,
            numberBg: "#A95C0B",
            textColor: "#fff",
            bgImg: "https://txqmp.cpic.com.cn/uploads/img/sign-calendar-todaybg.png",
          })
        }else{
          dateText.push({
            value: item.signDate
          })
        }
      }
      if(item.signState === 4){
        dateText.push({
          value: item.signDate,
          text: "补卡",
          type: "tag",
          bgImg: "https://txqmp.cpic.com.cn/uploads/img/sign-calendar-bg.png",
          tagBg: "linear-gradient(90deg, #4492FC 0%, #025CEA 100%)",
        })
      }
      if(item.signState === 5){
        if(this.data.nowDate === item.signDate){
          this.isSetTodayTag = true
        }
        dateText.push({
          value: item.signDate,
          text: "奖励",
          isPrize: true,
          bgImg: "https://txqmp.cpic.com.cn/uploads/img/sign-calendar-award.png",
        })
      }
    })
    // 设置今日标识
    // console.log(330, this.isSetTodayTag)
    if(!this.isSetTodayTag){
      dateText.push({
        value: this.data.nowDate,
        isToday: true,
        numberBg: "#A95C0B",
        textColor: "#fff",
        bgImg: "https://txqmp.cpic.com.cn/uploads/img/sign-calendar-todaybg.png",
      })
    }

    this.setData({
      dateText,
      fixTimes: res.data.data.fixTimes || 0,
      signTimes: res.data.data.signTimes || 0
    })
  },

  back(){
    if (this.data.isShare == 1 || this.data.scene) {
      wx.switchTab({
        url: '/pages/home/<USER>',
      })
    } else {
      wx.navigateBack({
        delta: 1
      })
    }
  },
  // 关闭专属海报弹框
  onClickHide(){
    this.setData({
      showPoster: false
    })
  },
  // 下载海报
  async downPoster(){
    this.setData({
      showPoster: true
    })
    const res = await API.signInShare({id: this.data.ids})
    console.log(res)
    // 分享会增加补卡次数 补卡次数增加影响日历展示
    this.getSignRecords()
  },
  // 点击日期
  async dateTipClick(e){
    // const src = encodeURIComponent("https://ft.kjgxplus.cpic.com.cn/actweb/capquestionnaire?voteid=A356952FD6C587E3AD664D5883DF4769&type=V1001&phone=18638539582&openid=ob3dI44_rETIk_j7xRLSK7OM_9cw&source=TXQ&empNo=AAC507D0E6C27AC47AE7E17CBA9B85DF&callbackUrl=https://txqmp-sit.cpic.com.cn/admin/%23%2Fresults%3FactivityType%3DSIGN_DRAW%26activityId%3D55060")
    // wx.navigateTo({ url: `/pages/activityTemplate/signIn/webview/index?id=${this.data.ids}&src=${src}`})
    // return
    const tip = e.detail.tip
    // console.log(375, tip)
    if(tip && tip.text === "补卡"){
      this.setData({repairDialogShow: true})
      this.repairDate = tip.value
    }
  },
  // 点击连续签到奖励图标
  async awardingClick(){
    this.setData({
      awarPrizeInfo: this.tempConPrizes[0],
      awardingShow: true
    })
  },
  findTodayIsReceive(){
    let index = this.data.tableData.findIndex(item =>{
      return isToday(new Date(item.receiveTime))
    })
    return index >= 0
  },
  // 连续签到奖励领取
  // 连续签到奖励 如用户有多个连续签到奖励未领取，则在第一个奖品领取后，弹第二轮的中奖奖品弹窗，以此类推，直至领取完所有奖励；
  async awardReceiveClick(){
    const res = await API.receiveSignInPrize({
      recordId: this.tempConPrizes[0].id,
      id: this.data.ids,
      prizeType: 2 // 连续签到奖励
    })
    console.log(res)
    if(res.data.code === 200){
      
      // console.log(411, this.getUrlRes)
      if(this.data.isFirst){
        const src = encodeURIComponent(this.getUrlRes.returnUrl)
        wx.navigateTo({ url: `/pages/activityTemplate/signIn/webview/index?id=${this.data.ids}&src=${src}`})
      }else{
        // 跳转第三方则不提示用户领取成功
        wx.showToast({title: "领取成功",icon: "none",duration: 2500})
      }
      
      this.setData({isFirst: false})
      if(res.data.data.length>0){
        this.tempConPrizes = res.data.data
        this.setData({
          awarPrizeInfo: res.data.data[0],
          awardingShow: true
        })
      }else{
        this.setData({isShowAwardBtn: false, awardingShow: false})
      }

      // 更新我的领取记录
      const receiveRes = await API.signInReceiveList({id: this.data.ids})
      if(receiveRes.data.code === 200){
        this.setData({tableData: receiveRes.data.data})
      }
    }else{
      wx.showToast({
        title: res.data.message,
        icon: "none",
        duration: 2500,
      })
    }
  },
  signIndialogClose(){
    this.setData({siginInDialogShow: false})
  },
  // 签到成功奖励领取 
  async signInReceiveClick(){
    const res = await API.receiveSignInPrize({
      recordId: this.signinPrizeId,
      id: this.data.ids,
      prizeType: 1 // 日常签到奖励
    })
    // console.log(450, res)
    if(res.data.code === 200){

      if(this.data.isFirst){
        const src = encodeURIComponent(this.getUrlRes.returnUrl)
        wx.navigateTo({ url: `/pages/activityTemplate/signIn/webview/index?id=${this.data.ids}&src=${src}`})
      }else{
        // 跳转第三方则不提示用户领取成功
        wx.showToast({title: "领取成功",icon: "none",duration: 2500})
      }

      this.setData({siginInDialogShow: false, isFirst: false})

      // 更新我的领取记录
      const receiveRes = await API.signInReceiveList({id: this.data.ids})
      if(receiveRes.data.code === 200){
        this.setData({tableData: receiveRes.data.data})
      }

    }else{
      wx.showToast({
        title: res.data.message,
        icon: "none",
        duration: 2500,
      })
    }
  },
  // 取消补卡
  repairDialogclose(){
    this.setData({repairDialogShow: false})
  },
  //  确认补卡
  repairClick(){
    const signDate = formatDate(new Date(this.repairDate.replace(/-/g, '/')).getTime())
    this.activitySignInHandle(signDate, "补卡") 
  },
  // 立即签到
  async signInClick(){
    if(this.data.audit == 1) return
    if (wx.getStorageSync('token') && this.data.userType == 0) {
      if (wx.getStorageSync('userId') !== '') {
        // 判断是否注册过
        this.getIsRegister(wx.getStorageSync('userId'))
      }
      return false;
    }
    
    if (this.data.sub_time1 < 0) {
      wx.showToast({
        title: "活动尚未开始，请耐心等待！",
        icon: "none",
        duration: 2500,
      })
      return false;
    }

    if (this.data.sub_time2 < 0) {
      wx.showToast({
        title: "活动已结束,请下次参与！",
        icon: "none",
        duration: 2500,
      })
    }
    let nowDate = formatDate(new Date().getTime())
    this.activitySignInHandle(nowDate, "签到")    
  },
  async activitySignInHandle(date, type){
    const res = await API.activitySignIn({signDate: date, id: this.data.ids})
    if(res.data.code == 200){
      if(type === "签到"){
        this.signinPrizeId = res.data.data.recordId
        this.setData({
          isHasSigninPrize: this.data.activity.activitySign.signOn==1,
          siginInDialogShow: true,
          signinPrizeInfo: res.data.data.prize
        })
      }
      // 补卡成功无奖励
      if(type === "补卡"){
        this.setData({repairDialogShow: false})
        wx.showToast({
          title: "补卡成功",
          icon: "none",
          duration: 2500,
        })
      }


      // 设置日历签到图标等
      // this.setSignInData(res)
      
      // 签到或者补卡都会 影响连续签到奖励 连续签到奖励奖品从活动详情获取
      this.getactivity(this.data.ids, {isDiasbleThird: true})
      
    }else{
      wx.showToast({
        title: res.data.message,
        icon: "none",
        duration: 2500,
      })
      this.getactivity(this.data.ids, {isDiasbleThird: true})
    }
    
  },
  goRule(){
    if(this.data.audit == 1) return
    wx.navigateTo({
      url: `/pages/activityTemplate/signIn/rule/index?activityId=${this.data.ids}`,
    })
  },
  siginInDialogShowClose(){
    this.setData({siginInDialogShow: false})
  },
  repairDialogShowClose(){
    this.setData({repairDialogShow: false})
  },
  awardingShowClose(){
    this.setData({awardingShow: false})
  },
  //活动详情
  async getactivity (id, options) {
    let that = this
    that.setData({
      myLoadAnimation: true,
    })
    let res = null
    if(that.data.audit == 1){
      res = await API.auditSigninDetail({id})
      this.setData({
        isShow:false
        
      })
    }else {
      res = await API.getActivityDetail({id})
    }
    console.log('详情数据：', res)
    if (res.header['Content-Type'] === 'text/html' && !that.data.reflashToast) {
      that.setData({
        myLoadAnimation: false,
        detailSucess: false
      })
      this.handleReflashToast()
    } else {
      if (res.data.code == 200) {
        // console.log(that);
        this.setData({
          detailSucess: true,
          myLoadAnimation: false,
        })
        if (this.data.detailSucess && this.data.userInfoSuccess && wx.getStorageSync('key')) {
          this.setData({
            reflashToast: false
          })
        }
        
        const create_time = res.data.data.startTime // 活动开始时间
        let applyStartTime = res.data.data.applyStartTime // 活动开始时间
        const end_time = res.data.data.endTime
        let applyEndTime = res.data.data.applyEndTime
        this.tempConPrizes = res.data.data.conPrizes
        var reg=new RegExp('TXQImgPath/','ig');
        let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
        let  activity =  res.data.data
        activity.activityPageConfig = activity.activityPageConfig&&activity.activityPageConfig.replace(reg,dynamicDomainName)
 
        const that = this
        that.setData({
          enterTime: new Date().getTime(),
          activity:activity,
          applyEndTime: res.data.data.applyEndTime,
          applyStartTime: res.data.data.applyStartTime,
          create_time: create_time,
          end_time: end_time,
          shiwutype: res.data.data.type,
          isProductionTalk: res.data.data.isProductionTalk, // 是否是产说会
          batchFlag: res.data.data.batchFlag,
          loadAnimation: false,
          chatLst: res.data.data.chatLst,  //活动可以参与的业务员渠道
          myLoadAnimation: false,
          isReflash: false,
          
          // 是否有未领取的连续签到奖励
          isShowAwardBtn: Array.isArray(res.data.data.conPrizes) && res.data.data.conPrizes.length>0,
          tableData: res.data.data.prizes
        })
       
        // 处理页面元素
        this.handlePageStyle(activity)
        let nowtime = new Date().getTime() //目前时间
        let createTime = new Date(applyStartTime.replace(/-/g, '/')).getTime()  // 活动开始时间时间戳
        let endTime = new Date(applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒

        let sub_time1 = parseInt((nowtime - createTime) / 1000) //正数： 活动开始  负数：活动开始倒计时
        let sub_time2 = parseInt((endTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束

        const activeTimeStart = this.getActiveTime(new Date(applyStartTime.replace(/-/g, '/')))
        const activeTimeEnd = this.getActiveTime(new Date(applyEndTime.replace(/-/g, '/')))

        that.setData({
          sub_time1,
          sub_time2,
          activeTimeStart,
          activeTimeEnd,
          initDisplayDate: (sub_time2 < 0 || sub_time1 < 0) ? activeTimeStart : this.data.nowDate
        })
        
        if (sub_time2 < 0 && !that.data.audit) {
          that.setData({
            isFinish: true,
          })
          wx.showToast({
            title: "活动已结束,请下次参与！",
            icon: "none",
            duration: 2500,
          })
        }
        if(this.data.audit != 1){
          // 获取签到记录 日历展示标识
          this.getSignRecords()
        }
        
        // 签到活动如有配置【奖励领取条件】，则当所有用户每日第一次获取奖励时（每日签到奖励或连续签到奖励），跳转进入第三方对应ID的文章/问卷页面；同时自动做奖励领取操作
        // ("DRAW","抽奖活动"), ("SIGN_DRAW","签到抽奖 "), ("VOTE","投票"),
        // receiveType 奖励领取条件1问卷2文章/视频
     
        if(options && options.isDiasbleThird) return;
        // const {receiveType} = this.data.activity.activitySign
        // if(receiveType){
        //   const backUrl = encodeURIComponent(`#/results?activityType=SIGN_DRAW&activityId=${this.data.ids}`)
        //   const getUrlRes = await API.signInGetReceiveUrl({activityId: this.data.ids, backUrl, receiveType, type: "SIGN_DRAW"})
        //   if(getUrlRes.data.code == 200){
        //     this.getUrlRes = getUrlRes.data.data
        //     // . 如正常返回ID，并未领奖
        //     this.setData({isFirst: this.getUrlRes.isNot === 'n'})
        //   }else{
        //     wx.showToast({
        //       title: getUrlRes.data.message,
        //       duration: 2000,
        //       icon: "none",
        //     })
        //   }
        // }
      } else {
        that.setData({
          myLoadAnimation: false,
        })
        if (res.data.code === 500 && res.data.message === '活动火爆，小主请稍后再试' && !that.data.reflashToast) {
          that.setData({
            detailSucess: false
          })
          that.handleReflashToast()
        } else {
          wx.showToast({
            title: "网络繁忙，请稍后重试",
            icon: "none",
            duration: 2000,
          })
        }
      }
    }
  },
  getActiveTime(date){
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    return year + '-' + month + '-' + day
  },
  handlerStyle (style) {
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  // 处理页面元素
  handlePageStyle(res){
    const that = this
    console.log(413, JSON.parse(res.activityPageConfig))
    const activityPageConfig = res.activityPageConfig ? JSON.parse(res.activityPageConfig).find((item) => item.code === 'homePage') : null
    if(activityPageConfig !== undefined && activityPageConfig !== null){
      // console.log('页面组件元素',activityPageConfig.componentData)
      activityPageConfig.componentData.forEach(item =>{
        if (item.component === "VButton") {
          let backgroundImage = ''
          let backgroundColor = item.style.backgroundColor
          if (item.bgStatus == 1) {
            backgroundImage = `url(${item.imgOptions.url})`
            backgroundColor = ''
          }
          item.style = {
            ...item.style,
            backgroundImage,
            backgroundColor
          }
        }
        item['styleStr'] = that.handlerStyle(item.style)
      })
      
      let backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'pageBackgroundImg')
      const signInPrizeConfig = activityPageConfig.componentData.find((item) => item.type === 'signInPrize')
      const calendarSet = activityPageConfig.componentData.find((item) => item.type === "calendar")

      that.setData({
        backgroundImg: backgroundImg?.propValue.url || '',
        signInPrizeStyle: signInPrizeConfig.style,
        componentData: activityPageConfig.componentData,
        calendarSet
      })
    }

    const activityPosterConfig = res.activityPageConfig ? JSON.parse(res.activityPageConfig).find((item) => item.title === '专属海报') : null
    if(activityPosterConfig !== undefined && activityPosterConfig !== null){
      const posterBackgroundImg = activityPosterConfig.componentData.find((item) => item.type === 'posterPic')
      const posterText = activityPosterConfig.componentData.find((item) => item.type === 'text')
      console.log(675, posterText)
      that.setData({
        posterBackgroundImg,
        activityPosterConfig,
        posterText,
      })
    }

    const signIndialogConfig = res.activityPageConfig ? JSON.parse(res.activityPageConfig).find((item) => item.title === '签到成功') : null
    // console.log(551, signIndialogConfig.componentData)
    // 要注释
    signIndialogConfig.componentData.forEach(item =>{
      if (item.component === "VButton") {
        let backgroundImage = ''
        let backgroundColor = item.style.backgroundColor
        if (item.bgStatus == 1) {
          backgroundImage = `url(${item.imgOptions.url})`
          backgroundColor = ''
        }
        item.style = {
          ...item.style,
          backgroundImage,
          backgroundColor
        }
      }
      item['styleStr'] = that.handlerStyle(item.style)
    })
    if(signIndialogConfig !== undefined && signIndialogConfig !== null){
      const signIndialogBgImg = signIndialogConfig.componentData.find((item) => item.type === 'pageBackgroundImg')
      that.setData({
        signIndialogBgImg,
        signIndialogConfig
      })
    }

    const awardDialogConfig = res.activityPageConfig ? JSON.parse(res.activityPageConfig).find((item) => item.title === '领奖弹窗') : null
    // console.log(551, awardDialogConfig.componentData)
    // 要注释
    awardDialogConfig.componentData.forEach(item =>{
      if (item.component === "VButton") {
        let backgroundImage = ''
        let backgroundColor = item.style.backgroundColor
        if (item.bgStatus == 1) {
          backgroundImage = `url(${item.imgOptions.url})`
          backgroundColor = ''
        }
        item.style = {
          ...item.style,
          backgroundImage,
          backgroundColor
        }
      }
      item['styleStr'] = that.handlerStyle(item.style)
    })
    if(awardDialogConfig !== undefined && awardDialogConfig !== null){
      const awardDialogBgImg = awardDialogConfig.componentData.find((item) => item.type === 'pageBackgroundImg')
      that.setData({
        awardDialogBgImg,
        awardDialogConfig
      })
    }

    const repairDialogConfig = res.activityPageConfig ? JSON.parse(res.activityPageConfig).find((item) => item.title === '补卡弹窗') : null
    // 要注释
    awardDialogConfig.componentData.forEach(item =>{
      if (item.component === "VButton") {
        let backgroundImage = ''
        let backgroundColor = item.style.backgroundColor
        if (item.bgStatus == 1) {
          backgroundImage = `url(${item.imgOptions.url})`
          backgroundColor = ''
        }
        item.style = {
          ...item.style,
          backgroundImage,
          backgroundColor
        }
      }
      item['styleStr'] = that.handlerStyle(item.style)
    })
    if(repairDialogConfig !== undefined && repairDialogConfig !== null){
      const repairDialogBgImg = repairDialogConfig.componentData.find((item) => item.type === 'pageBackgroundImg')
      that.setData({
        repairDialogBgImg,
        repairDialogConfig
      })
    }

    const activityRuleConfig = res.activityPageConfig ? JSON.parse(res.activityPageConfig).find((item) => item.title === '专属海报') : null
    if(activityRuleConfig !== undefined && activityRuleConfig !== null){
     this.data.activityRuleCom = activityRuleConfig.componentData
    }
  }, 

  //用户登录验证
  userInfoHandler: function (e) {
    var that = this;
    if (e.detail.errMsg == "getUserInfo:fail auth deny") { } else {
      wx.removeStorageSync('defaultPerson');
      wx.removeStorageSync('refreshUserInfo')
      app.getUserInfo(function (userInfo) {
        console.log(userInfo)
        wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
        wx.setStorageSync('nickName', userInfo.nikename);
        wx.setStorageSync('openId', userInfo.openId);
        wx.setStorageSync('userId', userInfo.id);
        // that.getisplay(that.data.ids)
        that.setData({
          showModalDlg: false,
          InfoShow: false,
          isAuthorize: true
        })
        wx.showToast({
          title: '授权成功',
          icon: "none",
          duration: 1500,
        })
        if (userInfo) {
          that.getactivity(that.data.ids)
          var timer3 = setInterval(() => {
            if (http.userAre() != '') {
              that.getUserInfo();
              clearInterval(timer3);
            }
          }, 100)
        }
      })
    }
  },
  // 查询是否注册
  getIsRegister() {
    let that = this
    if (http.unRegister()) {
      // 已经授权没有注册
      // 查询绑定的业务员
      if (that.data.isShare) {
        that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode,that.data.changeSaleName)
      }
      that.setData({
        personLogin: true,
        showModalDlg: false,
        InfoShow: false
      })
      if (!that.data.activity) {
        that.getactivity(that.data.ids)
      }
    } else {
      showTab()
      that.setData({
        showModalDlg: false,
        InfoShow: false
      })
      if(that.data.isShare == 1 ||that.data.isShare == 2){
        that.openChangeScole()
      }
      if (!that.data.activity) {
        that.getactivity(that.data.ids)
      }
    }
  },
  // 获取用户信息
  getUserInfo () {
    const that = this
    const token = wx.getStorageSync("token")
    // 获取用户所属门店信息
    if (token) {
      this.setData({
        myLoadAnimation: true,
      })
      API.getInfoById()
        .then(res => {
          if (res.header['Content-Type'] === 'text/html' && !that.data.reflashToast) {
            that.setData({
              myLoadAnimation: false,
            })
            this.handleReflashToast()
          } else {
            if (res.data.code == 200) {
              console.log('获取用户信息', res);
              const {cardType} = res.data.data
              const identityValue = that.data.identityType.filter((item) => item.value === cardType )
              console.log(identityValue)
              that.setData({
                type: res.data.data.type,
                salesCode: res.data.data.salesCode,
                idCard: CryptoJS.Decrypt(res.data.data.idcard),
                userType: res.data.data.type,
                credits: res.data.data.credits,
                userInfoSuccess: true,
                myLoadAnimation: false,
                cardType:cardType,
                identity:identityValue[0]?.name || '',
              })

              if (that.data.detailSucess && that.data.userInfoSuccess && wx.getStorageSync('key')) {
                that.setData({
                  reflashToast: false
                })
              }
              console.log(CryptoJS.Decrypt(res.data.data.salesCode));
              handleUserStorage(res.data.data)
              console.log('用户信息业务员账号', wx.getStorageSync('salesCode'));
              if (wx.getStorageSync('userId') !== '') {
                // 判断是否注册过
                that.getIsRegister(wx.getStorageSync('userId'))
              }
            } else {
              that.setData({
                myLoadAnimation: false,
              })
              if (res.data.code === 500 && res.data.message === '活动火爆，小主请稍后再试' && !that.data.reflashToast) {
                this.handleReflashToast()
              } else {
                wx.showToast({
                  title: res.data.message,
                  icon: 'none',
                  duration: 2000
                })
              }
            }
          }
        })
    }
    if (wx.getStorageSync("token")) {
      that.setData({
        phonebtn: true,
      })
    } else {
      that.setData({
        phonebtn: false,
        tobind: false
      })
    }
  },
  handleReflashToast () {
    let that = this
    that.setData({
      reflashToast: true,
      reflashBtnContent: `(${totalTime}s)`,
      isReflash: false
    })
    clearInterval(clock);
    clock = setInterval(() => {
      totalTime--;
      that.setData({
        reflashBtnContent: `(${totalTime}s)`
      })
      if (totalTime < 1) {
        clearInterval(clock);
        totalTime = 5
        that.setData({
          reflashBtnContent: '',
          isReflash: true
        })
      }
    }, 1000);
  },
  // 刷新获取活动数据
  reGetActivity () {
    let that = this
    this.handleReflashToast()
    if (!that.data.userInfoSuccess) {
      this.getUserInfo()
    }
    if (!wx.getStorageSync('key')) {
      CryptoJS.getKey()
    }
    if (that.data.userInfoSuccess && !that.data.detailSucess && !that.data.activity) {
      console.log('加载活动');
      this.getactivity(this.data.ids)
    }
  },
  openChangeScole() {
    let salesCode = wx.getStorageSync('salesCode')//加密
    let saleName = wx.getStorageSync('saleName')
    console.log('--✨🍎',)
    if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
      this.setData({
        showSwitchSale: true,
        currentId: saleName + salesCode,
        acceptId: this.data.changeSaleName + this.data.changeSalesCode
      })
    }
  },
  // 更改证件类型
  identityChange (e) {
    const that = this
    const identityType = that.data.identityType
    console.log('picker发送选择改变，携带值为', e.detail.value);//index为数组点击确定后选择的item索引
    this.setData({
      identityIndex: e.detail.value,
      identity: identityType[e.detail.value].name,
      cardType:identityType[e.detail.value].value
    })
  },

  reject(){
    this.setData({
      showReject:true,
      rejectReason:''
    })
  },
  triggerEvent(e){
    console.log(e);
    wx.navigateBack({
      delta:1
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  async onShareAppMessage () {
    let that = this
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let salesCode = wx.getStorageSync('salesCode')
    let userType = wx.getStorageSync('userType')
    let saleName = wx.getStorageSync('saleName')

    let res = await API.signInShare({id: this.data.ids})
    var result = this.data.activity.shareText.replace('#微信昵称#', nickName);
    var title = result.replace('#活动名称#', this.data.activity.title);
    console.log('result:', res);
    // 分享会增加补卡次数 补卡次数增加影响日历展示
    this.getSignRecords()

    return {
      title: title,
      imageUrl: this.data.activity.shareUrl,
      path: `/pages/activityTemplate/signIn/home/<USER>
    }


  },
})