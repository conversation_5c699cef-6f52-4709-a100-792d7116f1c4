<view class="content" style="{{contentStyle}}">
  <view class="content_header" style="height: {{navBarData.navBarHeight}}px">
    <view class="content_header_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px">
      <van-icon name="arrow-left" size="1.3em" color="#fff" bind:click="goBack" class="content_header_menu_icon" style="left:{{navBarData.menuRight}}px;" />
      <view class="content_header_menu_title">问卷活动</view>
    </view>
  </view>
  <view class="content_main">
    
    <view wx:for="{{componentData}}" wx:key="index">
      <view wx:if="{{!item.isHide}}">
        <view class="content_main_item {{ item.component === 'VButton' ? 'content_main_item_btn' : '' }}" style="{{item.styleStr}}">
          <image wx:if="{{item.component === 'Picture'}}" class="content_main_item_img" src="{{item.propValue.url}}"></image>
          <view wx:if="{{item.component === 'VButton'}}" data-item="{{item}}" bind:tap="tapItem" class="content_main_item_btn_info">
            {{item.propValue}}
          </view>
          <view wx:if="{{item.component === 'rule'}}" class="content_main_item_rule">
            <rich-text nodes="{{item.propValue}}"></rich-text>
          </view>
          <view wx:if="{{ item.component === 'plainText' }}">{{item.propValue}}</view>
          <view wx:if="{{ item.component === 'ruleText' }}">{{item.propValue}}</view>
        </view>
      </view>
    </view>

    <view class="content_info" wx:if="{{!isHomePage}}">
      <view class="content_info_main">
        <view wx:for="{{questionsData}}" wx:key="index" class="content_info_item">
          <view class="content_info_item_header">
            <view class="content_info_item_header_index">
              <view wx:if="{{item.needed == 1}}" class="content_info_item_header_index_required">*</view>
              Q{{index+1}}</view>
            <view class="content_info_item_header_title">{{item.title}}</view>
          </view>
          <view class="content_info_item_main">
            <textarea wx:if="{{item.type == 'INPUT'}}" class="content_info_item_main_textarea" type="text" bindinput="inputBlur" placeholder="请输入" value="{{item.defaultValue}}" data-key="{{item.id}}" placeholder-style="font-size:28rpx;color:#C0C0CA" data-index='{{index}}' disabled="{{isEdit}}" />
            <view wx:if="{{item.type == 'MUTISELECT'}}" class="content_info_item_main_mutiselect">
              <van-checkbox-group bind:change="onChange" value="{{item.defaultValue}}" data-key="{{item.id}}" data-index='{{index}}'>
                <block wx:for="{{item.options}}" wx:for-item="option" wx:key="id">
                  <!-- 有图片的选项结构 -->
                  <view class="imageUrl {{option.select?'selectType':''}}" wx:if="{{item.imageUrl}}">
                    <image src="{{option.url}}" class="optionImg" />
                    <van-checkbox name="{{option.id}}" icon-size="18px" checked-color="linear-gradient(to right,#025CEA,#4492FC)" label-class="{{option.select?'colorType':''}}" disabled="{{isEdit}}">
                      {{option.content}}
                    </van-checkbox>
                  </view>
                  <!-- 没有图片的选项 -->
                  <van-checkbox wx:else name="{{option.id}}" icon-size="18px" checked-color="linear-gradient(to right,#025CEA,#4492FC)" label-class="{{option.select?'colorType':''}}" disabled="{{isEdit}}">
                    {{option.content}}
                  </van-checkbox>
                </block>
              </van-checkbox-group>
            </view>
            <view wx:if="{{item.type == 'RADIO'}}" class="content_info_item_main_radio">
              <van-radio-group bind:change="onChanges" value="{{item.defaultValue}}" data-key="{{item.id}}" data-index='{{index}}'>
                <block wx:for="{{item.options}}" wx:for-item="option" wx:key="id">
                  <!-- 有图片的选项结构 -->
                  <view class="imageUrl {{option.select?'selectType':''}}" wx:if="{{item.imageUrl}}">
                    <image src="{{option.url}}" class="optionImg" />
                    <van-radio name="{{option.id}}" icon-size="18px" checked-color="linear-gradient(to right,#025CEA,#4492FC)" label-class="{{option.select?'colorType':''}}" disabled="{{isEdit}}">
                      {{option.content}}
                    </van-radio>
                  </view>
                  <!-- 没有图片的选项 -->
                  <van-radio wx:else name="{{option.id}}" data-content="{{option.content}}" icon-size="18px" checked-color="linear-gradient(to right,#025CEA,#4492FC)" label-class="{{option.select?'colorType':''}}" disabled="{{isEdit}}">
                    {{option.content}}
                  </van-radio>
                </block>
              </van-radio-group>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close" allShow='{{true}}'></login-box>
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler">点击进入</button>
  </view>
</view>
<!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" showCancel="{{showCancel}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" salesCode="{{changeSalesCode}}" bind:close="closeChangeTieModal"></switch-sale>
<!-- 分享页面是否切换业务员 -->
<!-- 活动审核显示底部操作按钮 -->
<audit wx:if="{{audit}}" bind:reject="reject" activityId="{{activityId}}" linkType="inquire"></audit>
<!-- 活动审核显示底部操作按钮 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" audit="{{auditStatus}}" financeStatus="{{financeStatus}}" rejectReason="{{rejectReason}}" rejectButton="triggerEvent"></reject-reason>

<post-popup show="{{showPoster}}" wx:if="{{showPoster}}" id="poster" codeUrl="{{activity.qrcode}}" activityName="{{activity.title}}" isShowBtn="{{false}}" banner="{{postersBanner}}" title="{{posterText}}" textStyle="{{textStyle}}" bind:onClickHide="onClickHide" audit="{{audit}}"></post-popup>
