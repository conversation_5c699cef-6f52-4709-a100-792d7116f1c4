

@import '../../../../utils/quill.snow.wxss';
@import '../../../../utils/quill.bubble.wxss';
@import '../../../../utils/quill.core.wxss';
page{
  box-sizing: border-box;
  position: relative;
}
.navTitle{
  background-color: #fff;
}
.contain{
  background-size: 100% auto;
  background-repeat: no-repeat;
}
.content{
  position: relative;
  background-size: 100% 100%;
}
.mask {
  position:absolute;
  z-index:50;
  width:750rpx;
  height:100vh;
  background-color:rgba(131, 120, 120, 0.3);
  border:none;
  border-radius:0;
  margin: 0;
}
.spinner {
  margin: 50vh auto;
  text-align: center;
}

.spinner > view {
  width: 40rpx;
  height: 40rpx;
  background-color: #dd2b2b;
  border-radius: 100%;
  display: inline-block;
  animation: bouncedelay 1.4s infinite ease-in-out;
  animation-fill-mode: both;
}
.spinner .bounce1 {
  animation-delay: -0.32s;
}
.spinner .bounce2 {
  animation-delay: -0.16s;
}
@keyframes bouncedelay {
  0%, 80%, 100% {
    transform: scale(0.0);
  } 40% {
    transform: scale(1.0);
  }
}
.activityNeeds {
  position: absolute;
  left: 0;
  top: 48rpx;
  width: 128rpx;
  height: 44rpx;
  background: rgba(0,0,0,0.4);
  border-radius: 200rpx 0rpx 0rpx 200rpx;
  text-align: center;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #EFEFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}
.voteBanner {
  width: 100%;
  height: 100%;
}
.voteCouter{
  position: relative;
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  top:0;
  left: 0;
}
.voteCouterTitle {
  width: 100%;
  font-size: 60rpx;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #FF564A;
  box-sizing: border-box;
  margin-bottom: 32rpx;
  letter-spacing: 8rpx;
  /* text-shadow: 0rpx 4rpx 4rpx #F63432; */
  letter-spacing: 8rpx;
  /* text-shadow: 0rpx 4rpx 4rpx #F63432; */
}
.voteCouterTime {
  text-align: left;
  box-sizing: border-box;
  font-size: 28rpx;
  font-family: PingFang SC;
}
.num {
  display: inline-block;
  width: 52rpx;
  height: 52rpx;
  line-height: 52rpx;
  background-color: linear-gradient(180deg, #FFFFFE 49%, #FFDADA 100%);
  box-shadow: 0rpx 2rpx 0rpx 0rpx #FC3E3E;
  border-radius: 8rpx;
  box-sizing: border-box;
  color: #FF5030;
  text-align: center;
}
.numUnit {
  display: inline-block;
  text-align: center;
  width: 28rpx;
  height: 52rpx;
  font-weight: 500;
  color: #fff;
  line-height: 52rpx;
}
.activityDetailBox{
  width: 100%;
  position: relative;
  background: linear-gradient(180deg, #F5F6FF 0%, #FFFFFF 25%);
  border-radius: 10rpx;
  margin-bottom: 48rpx;
  box-sizing: border-box;
  padding: 24rpx 32rpx;
  box-shadow: 0rpx 4rpx 10rpx rgba(23,32,77,.07);
  margin-top: 252rpx;
}
.detailTopBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 108rpx;
  margin-bottom: 24rpx;
}
.numInfo {
  width: 268rpx;
  height: 108rpx;
  /* display: flex;
  flex-direction: column;
  align-items: center; */
  background: #FFF2F2;
  border-radius: 8rpx;
  padding-left: 32rpx;
  position: relative;
}
.realNum{
  font-size: 32rpx;
  font-family: PingFang SC-粗体, PingFang SC;
  font-weight: normal;
  color: #17204D;
  margin-top:20rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
  line-height: 32rpx;
}
.numTitle{
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #7E849F;
  line-height: 24rpx;
}
.img_desc{
  position: absolute;
  right: 8rpx;
  bottom: 0;
  width: 80rpx;
  height: 68rpx;
}
.lineCss {
  width: 100%;
  height: 100%;
}
.voteNeedsBox {
  box-sizing: border-box;
  background: #FDF6F6;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #555C80;
}
.voteNeedsBox span{
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #17204D;
}
.closeX {
  width: 100%;
  text-align: right;
  font-size: 30rpx;
  font-weight: 700;
  box-sizing: border-box;
  padding-right: 20rpx;
  padding-top: 20rpx;
}
/* .van-cell {
  border: 2rpx solid #FF564A;
  border-radius: 10rpx;
} */
.voteInfo {
  padding: 32rpx;
  position: relative;
}
/* 搜索框 */
.searchOutter {
  position: relative;
  height: 72rpx;
  margin: 48rpx 0 40rpx;
  box-sizing: border-box;
}
.van-cell{
  background-color: #F2F3F5 !important;
}
.searchBox {
  box-sizing: border-box;
  background-color: #F2F3F5;
  overflow: hidden;
  height: 100%;
  width: 100%;
  border-radius: 36rpx;
  line-height: 72rpx;
  padding-left: 70rpx;
  font-size: 28rpx;
  color: #17204D;
}
.searchBox input {
  height: 100%;
  width: 100%;
  line-height: 72rpx;
  border-radius: 36rpx;
  color: #17204D;
}
.searchIconBtn {
  position: absolute;
  left: 18rpx;
  top: 14rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.iconBtnSearch{
  width: 44rpx;
  height: 44rpx;
}
/* 排名按钮 */
.classBtns {
  display: flex;
  font-size: 28rpx;
  font-family: PingFang SC;
  color: #7E849F;
  justify-content: space-around;
  align-items: baseline;
  box-sizing: border-box;
}

.tabCss {
  height: 40rpx;
  line-height: 40rpx;
  padding-bottom: 24rpx;
}
.tabActive{
  border-bottom: 6rpx solid #025CEA; 
  font-size: 28rpx; 
  font-weight: bold;
  color: #17204D;
}
/* 瀑布流 */
.voteListBox {
  width: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 32rpx;
}
.left{
  width: 336rpx;
  box-sizing: border-box;
  margin-right: 14rpx;
}
.right{
  width: 336rpx;
  box-sizing: border-box;
}
.activityItemImg {
  width: 304rpx;
  height: 280rpx;
}
.voteWorkItem {
  position: relative;
  margin-bottom: 24rpx;
  text-align: center;
  padding:16rpx 16rpx 24rpx 16rpx;
  display: inline-block;
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0rpx 4rpx 10rpx rgba(23,32,77,.07);
}
.classIco {
  position: absolute;
  bottom: 16rpx;
  left: 16rpx;
  width: 120rpx;
  height: 40rpx;
  background: rgba(0,0,0,0.4);
  border-radius: 4rpx;
  z-index: 3;
}
.classIcoItem{
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 24rpx;
  line-height: 40rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #FFFFFF;
}
.classIcoItem span{
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  border-radius: 4rpx;
  background: #FFF7F4;
}
.classIcoItem text:last-child{
  display: inline-block;
  width: 44rpx;
  height: 32rpx;
  background: linear-gradient(270deg, #FC3E1B 0%, #FC6B37 100%);
  border-radius: 4rpx;
  line-height: 32rpx;
  font-size: 24rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #FFFFFF;
}
.classIcoRight{
  width: 94rpx;
  height: 40rpx;
  background: rgba(0,0,0,0.4);
  border-radius: 4rpx 0rpx 0rpx 4rpx;
  position: absolute;
  top: 32rpx;
  right: 0;
  font-size: 20rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #FFFFFF;
  text-align: center;
  line-height: 40rpx;
  display: none;
}
.classIng{
  width: 100%;
  height: 100%;
}
.voteWorkItem_img {
  width: 304rpx;
  height: 280rpx;
  position: relative;
  /* height: 240rpx; */
}
.voteWorkItem_title {
  word-wrap: break-word;
  word-break: break-all;
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #17204D;
  text-align: left;
  line-height: 40rpx;
  margin:18rpx 0 16rpx 0;
}
.voteWorkItem_info {
  font-size: 26rpx;
  color: #FF564A;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}
.voteWorkItem_num {
  font-size: 36rpx;
  font-family: PingFang SC-粗体, PingFang SC;
  font-weight: normal;
  color: #FF5030;
  line-height: 32rpx;
  text-align: left;
}
.voteWorkItem_num span{
  font-size: 20rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #7E849F;
  margin-left: 8rpx;
}
.voteWorkItem_vote {
  width: 128rpx;
  height: 56rpx;
  background: linear-gradient(90deg, #4492FC 0%, #025CEA 100%);
  border-radius: 48rpx;
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #FFFFFF;
  line-height: 56rpx;
  text-align: center;
}
.voteWorkItem_vote_success{
  border-radius: 48rpx;
  border: 2rpx solid #025CEA;
  background:none;
  font-size: 28rpx;
  font-family: PingFang SC-中等, PingFang SC;
  font-weight: normal;
  color: #025CEA;
}
.footInfoWarn {
  text-align: center;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
/* 模态框 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 105;
  opacity: 0.7;
}
.modalDlg{
  width: 80%;
  height: 300rpx;
  position: fixed;
  top: 60%;
  left: -1%;
  z-index: 1000;
  margin: -370rpx 85rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: space-between; */
  overflow: hidden; 
}
.pickTitle{
  text-align: center;
  font-size: 36rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #12172E;
  margin-bottom: 20rpx;
}
.btnsBox {
  width: 100%;
  overflow: auto;
  height: 400rpx;
}
.btnCss {
  width: 100%;
  background: #fff;
  margin: 10rpx 0;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #666666;
}
.xCss {
  width: 44rpx;
  height: 44rpx;
}
.modalDlg1 {
  width: 80%;
  position: fixed;
  top: 50%;
  left: -1%;
  z-index: 1000;
  margin: -370rpx 85rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden; 
}
.modalDlg1>text{
  font-size:30rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height:180rpx;
  width:100%;
  font-weight: bold;
}
/* .modalDlg1>view>button{
  width:500rpx;
  height:80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #FF564AFF;
  background-color:#FF564AFF;
  color: #fff;
} */
.modalDlg>text{
  font-size:30rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height:180rpx;
  width:100%;
  font-weight: bold;
}
.modalDlg>view>button{
  width:500rpx;
  height:80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #FF564AFF;
  background-color:#FF564AFF;
  color: #fff;
}
/* 底部按钮 */
.bargain {
  position: fixed;
  width: 100%;
  font-size: 34rpx;
  color: #025CEA;
  background: #FFf;
  box-sizing: border-box;
  border: 2rpx solid #025CEA;
  z-index: 1;
  bottom: 68rpx !important;
  background-size: 100% 100%;
}
.bargain .buttonName{
  text-align: center;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bargain .buttonWidth {
  width: 45%;
}
.buttonBgc {
  background: linear-gradient(90deg, #4492FC 0%, #025CEA 100%);
  color: #fff;
}