page {
  background-color: #fff;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.jinz<PERSON>{
  width: 100vh;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
}
/* 模态框 */
.mask{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 105;
  opacity: 0.7;
}
.modalDlg{
  width: 80%;
  height: 300rpx;
  position: fixed;
  top: 62%;
  left: -1%;
  z-index: 1000;
  margin: -370rpx 85rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: space-between; */
  overflow: hidden; 
}
.van-radio{
  margin-bottom: 20rpx !important;

}
.modalDlg>text{
  font-size:30rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height:180rpx;
  width:100%;
  font-weight: bold;
}
.modalDlg>view>button{
  width:500rpx;
  height:80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #FF564AFF;
  background-color:#FF564AFF;
  color: #fff;
}
.content{
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
  padding-bottom: calc(constant(safe-area-inset-bottom) - 12px);
  padding-bottom: calc(env(safe-area-inset-bottom) - 12px);
}

.content_header {
  position: relative;
  width: 100%;
  flex-shrink: 0;
}

.content_header_menu {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 34rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #FFFFFF;
}

.content_header_menu_icon {
  position: absolute;
  left: 0;
}

.content_main {
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
}

.content_main_item {
  position: absolute;
  box-sizing: border-box;
}

.content_main_item_btn {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.content_main_item_btn_info {

}

.content_main_item_rule {
  padding: 0 30rpx;
  max-height: 500rpx;
  overflow: hidden;
  overflow-y: auto;
}

.content_main_item_img {
  width: 100%;
  height: 100%;
  display: block;
}

.backgroundImg,title-img{
  position: absolute;
}
.backgroundImg{
  z-index: -1;
}
.questions{
  width: 100%;
  padding: 216rpx 48rpx 0;
  box-sizing: border-box;
  height: calc(100% - 150rpx);
}
.title{
  margin: 0rpx auto 50rpx;
  font-size: 32rpx;
  color: #17204D;
  text-align: center;
  font-weight: bold;
  font-family: PingFang SC;
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.clickBtn{
  position: fixed;
  width: 100%;
  height: 96rpx;
  background: #fff;
  background-size: contain;
  z-index: 1;
  bottom:20rpx;
  left: 32rpx;
}
/* .subBtn{
  width: 100%;
  text-align: center;
  background: linear-gradient(to right,#025CEA,#4492FC);
  border-radius: 48rpx;
  font-size: 32rpx;
  padding: 28rpx 0;
  color: #fff;
} */
.question_title{
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #2A3039;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.question_title_required {
  color: #F35B31;
}
.questions_main {
  background-color: #fff;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 20rpx;
  overflow: hidden;
  overflow-y: auto;
  height: calc(100% - 60rpx);
}
.topIntroduct{
  padding: 20rpx;
  font-size: 26rpx;
  color: #999;
  margin:60rpx 38rpx 0;
  border: 1px solid #e1e1e1;
}
.questionType{
  margin-top: 40rpx;
  overflow: hidden;
}
.property{
  display: flex;
  /* height: 40rpx;
  line-height: 40rpx; */
  margin-bottom: 30rpx;
  font-size: 30rpx;
  color: #17204D;

  /* padding-left: 10rpx; */
  /* border-left:4rpx solid #F82420; */
}
.question_key{
  width: 100%;
  overflow: hidden;

 }
.questionType textarea{
  width: 620rpx;
  height: 120rpx;
  font-size: 28rpx;
  background: #F7F8FA;
  border-radius: 16rpx;
  padding: 24rpx;
  box-sizing: border-box;
}

.bargain {
  position: relative;
  width: 670rpx;
  font-size: 36rpx;
  color: #fff;
  height: 107rpx;
  line-height: 107rpx;
  padding: 0;
  margin-top: 60rpx;
  bottom: 0;
  left: 50%;
  margin-left: -339rpx;
}

.van-radio__label{
  color: #555C80 !important;
  overflow-x: scroll;
}
.questionType .colorType{
  color:#17204D !important;
  font-weight: bold;
}
.bargain image{
  width: 670rpx;
  height: 107rpx;
}
.bargain text{
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  text-align: center;
  height: 107rpx;
  line-height: 88rpx;
}
.imageUrl{
  width: 45%;
  float: left;
  width: 292rpx;
  height: 236rpx;
  border: 1px solid #F2F3F5;
  border-radius: 16rpx;
  padding: 16rpx 16rpx 24rpx;
  text-align: center;
  box-sizing: border-box;
  margin-bottom: 32rpx;
}
.imageUrl:nth-child(2n+1){
  margin-right: 40rpx;
}
.selectType{
  border: 1px solid #025CEA;
}
.imageUrl .van-checkbox,.imageUrl .van-radio{
  justify-content: center;
}
.van-checkbox__label{
  overflow-x: scroll;
}
.imageUrl .van-radio__label,.imageUrl .van-checkbox__label{
  font-size: 28rpx;
  overflow-x: scroll;
}
.optionImg image{
  width: 260rpx;
  height: 140rpx;
  margin-bottom: 20rpx;
}