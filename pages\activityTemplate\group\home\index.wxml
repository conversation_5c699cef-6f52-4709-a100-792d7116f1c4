
<!-- 获取微信授权弹框 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{InfoShow}}"></view>
<view class="modalDlg" wx:if="{{InfoShow}}">
    <text>参与活动</text>
    <view>
    <button bindtap="userInfoHandler">点击进入</button>
    </view>
</view>
<!-- 获取微信授权弹框 -->
<!-- 加载动画 -->
<loading-animation  wx:if="{{myLoadAnimation}}"></loading-animation>
<!-- 加载动画 -->
<!-- 分享页面是否切换业务员 -->
<!--   -->
<switch-sale show="{{showSwitchSale}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" bind:sureSwitch="confirmChangeTieModal" isShowCancel="{{isShowCancel}}" isShowClose="{{isShowClose}}" bind:close="closeChangeTieModal"></switch-sale>
<!-- 分享页面是否切换业务员 -->

<!-- 没有同意协议显示弹窗 -->
<privacy-popop  showPrivacy="{{disagree}}" bind:agree="agreePrivacy" jumpType="{{jumpType}}"></privacy-popop>
<!-- 没有同意协议显示弹窗 -->

<!-- 刷新弹窗 -->
<view  wx:if="{{reflashToast}}">
  <view class="mymask">
    <view class="mask_content">
      <image class="reflash_img" src="https://txqmp.cpic.com.cn/uploads/img/refresh.png" lazy-load="false"/>
      <view class="reflash_text_wrap">
        <view>啊哦~</view>
        <view>大伙的热情过于高涨，</view>
        <view>请稍等片刻~</view>
      </view>
      <view class="reflash_btn_wrap">
        <button class="reflash_btn" disabled="{{!isReflash}}" bindtap="reGetActivity">刷新{{reflashBtnContent}}</button>
      </view>
    </view>
  </view>
  <view class="mask2" catchtouchmove="preventTouchMove"></view>
</view>
<!-- 刷新弹窗 -->

<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close"></login-box>
<!-- 登陆者身份选择 -->
<scroll-view class="page_main" scroll-y="true" style="background-image:url({{backgroundImg.propValue.url}});height: {{(audit == 1) ? 'calc(100vh - 196rpx)':showPoster?'100vh':'96vh'}}">
  <!-- 活动详情banner -->
  <block wx:for="{{activityPageConfig.componentData}}" wx:key="index">
    <block wx:if="{{item.type === 'VSwiper'}}">
      <custom-carousel  bannerConfig="{{item}}" heightTop="{{heightTop}}" showHomeMenu="{{isShare||scene||groupNo || refer=='kege'}}" showIcon="true"></custom-carousel>
    </block>
  <!-- 活动详情banner -->
  <!-- 基本信息 -->

  <view class="activity_detail_bg" wx:if="{{item.type === 'describe'}}" style="{{item.styleStr}};background-image:url({{item.propValue.url}}">
    <view class="rights">
      <view class="sale_place">{{activity.groupPersons}}人成团</view>
    </view>
    <!-- <view class="activity_detail_time">
      <view class="left">
        <view class='red_price' hidden="{{activity.price == 0 || activity.price == null?true:false}}">
          <text class="wordTip">现价：</text>
          <text style="font-size: 24rpx;">¥</text><text>{{activity.price}}</text>
          
        </view>
        <view class='red_price' hidden="{{activity.price == 0 || activity.price == null?false:true}}">{{activity.freeLimit}}</view>
      </view>
      <view class='origin_price' hidden="{{activity.price == 0 || activity.price == null?true:false}}"><text style="font-size: 24rpx;">¥</text>{{activity.originalPrice}}</view>
    </view>
    <view class="tokenMax" decode="{{true}}" wx:if="{{activity.tokenMax > 0}}">积分可抵扣{{activity.tokenMax}}元</view>-->
    <view class="innerContent">
      <view class="companyName" wx:if="{{activity.companyName}}">{{activity.companyName}}</view>
    </view>
  </view>
  <block wx:if="{{item.type === 'limitText' || item.type === 'registrationPrice'}}">
    <view class="left" style="{{item.styleStr}}; position: absolute">
      <block wx:if="{{item.type === 'registrationPrice'}}">
        <view class='red_price' wx:if="{{activity.price != 0 && activity.price !== null }}">
          <text class="wordTip">{{item.propValue}}</text>
          <text style="font-size:24rpx;color:{{item.style.priceColor}}">¥</text>
          <text style="color:{{item.style.priceColor}}">{{activity.price}}</text>
          <view class='bla_price' style="color:{{item.style.originalPriceColor}}">
            ￥{{activity.originalPrice}}
          </view>
        </view>
      </block>
      <block wx:else>
        <view class='red_price' wx:if="{{activity.price == 0 || activity.price == null}}" style="{{item.styleStr}}">{{item.propValue}}
        </view>
      </block>
    </view>
  </block>
  <block wx:if="{{item.type === 'credit'}}">
    <view class="activity_detail_credit" style="{{item.styleStr}}" decode="{{true}}" wx:if="{{activity.tokenMax > 0}}">
      积分可抵扣{{activity.tokenMax}}元
    </view>
  </block>
  <view class="activity_detail_title" wx:if="{{item.type === 'titleText'}}" style="{{item.styleStr}}">{{activity.title}}</view>
 
  <view class="activity_time" wx:if="{{item.type === 'time'}}" style="{{item.styleStr}}">
    <view class="time_main" style="color: {{item.style.textColor}}">
      <text wx:if="{{sub_time1 < 0 ?true:false}}">{{item.propValue.before.startText}}:</text>
      <text wx:else >{{item.propValue.after.startText}}:</text>
      <span style="color: {{item.style.numberColor}}">{{timeObj.day}}</span>天
      <span style="color: {{item.style.numberColor}}">{{timeObj.hour}}</span>时
      <span style="color: {{item.style.numberColor}}">{{timeObj.points}}</span>分
      <span style="color: {{item.style.numberColor}}">{{timeObj.seconds}}</span>秒
      <text wx:if="{{sub_time1 < 0 ?true:false}}">{{item.propValue.before.endText}}</text>
      <text wx:else>{{item.propValue.after.endText}}</text>
    </view>
  </view>
  <block wx:if="{{item.type === 'poster' && activity.flagPoster == 1}}">
    <!-- 专属海报弹框 -->
    <post-popup show="{{showPoster}}" codeUrl="{{activity.qrcode}}" activityName="{{activity.title}}" btnText="{{item}}" banner="{{posterBackgroundImg.propValue.url}}" title="{{posterText.propValue}}" textStyle="{{posterText}}" bind:onClickHide="onClickHide" bind:clickDownPoster="downPoster" audit="{{audit||checkDetail}}"></post-popup>
    <!-- 专属海报弹框 -->
  </block>
 <block wx:if="{{item.type === 'applyTime'}}">
    <view class="activity_detail_time_title" style="{{item.styleStr}};color:{{item.optionsStyle.timeColor}};font-size:{{item.optionsStyle.timeFontSize}};font-weight:{{item.optionsStyle.timeFontWeight}};">
      <view style="color:{{item.optionsStyle.color}};font-size:{{item.optionsStyle.fontSize}};font-weight:{{item.optionsStyle.fontWeight}};border-color:{{item.optionsStyle.color}};">报名时间</view>{{applyStartTime}} ~ {{applyEndTime}}
    </view>
 </block>
  
  <view class="totalNum" wx:if="{{item.type === 'stock'}}" style="{{item.styleStr}} {{item.optionsStyleStr}}" >
    <view class="sale_free">已售：<text class="mount" style="color:{{item.optionsStyle.timeColor}};font-size:{{item.optionsStyle.timeFontSize}};font-weight:{{item.optionsStyle.timeFontWeight}};">{{activity.totalPlace-activity.place}}</text></view>
    <view class="sale_free">剩余：<text class="mount" style="color:{{item.optionsStyle.timeColor}};font-size:{{item.optionsStyle.timeFontSize}};font-weight:{{item.optionsStyle.timeFontWeight}};">{{activity.place}}</text></view>
  </view>
  <!-- 基本信息 -->
  <!-- 活动进度-->
  <view class="activity_detail" wx:if="{{item.type === 'text' && item.options.isShowProgress==1}}" style="{{item.styleStr}};background-image:url({{item.propValue.url}}">
    <view class="activity_detail_desc">
      <view class="detailtip">
        <view class="progress">
          <view><image src="../../../../image/activity/activity_bargain_detail_left.png"></image></view>
          <view>{{item.options.title}}</view>
          <view><image src="../../../../image/activity/activity_bargain_detail_right.png"></image></view>
        </view>
        <block wx:if="{{groupList.length !== 0}}">
          <scroll-view scroll-y="true" style="height: {{item.options.count*120 - 30}}rpx;">
          <view wx:for="{{groupList}}" wx:key="index" wx:for-item="items">
            <view class="header_main" >
              <view class="header">
                <image class="portraitimg" width="100%" height="100%" src='{{items.avatarUrl}}'></image>
                <view class="userName">
                  <view style="font-size: 28rpx;" class="margin_bottom16">{{items.nikename}}{{item.options.name}}</view>
                  <view  class="smallInfo">{{items.createTimes}}</view>
                </view>
              </view>
              <view class="middle" style="font-size: 24rpx;">
                <view class="margin_bottom16" style="color: #FF5030;">{{item.options.progress.before}}{{items.canJoinPersons}}{{item.options.progress.after}}</view>

                <!-- <view  class="smallInfo">还剩{{items.timeObj}}</view> -->
                <view  class="smallInfo">还剩{{items._text}}</view>
              </view>
              <view class="buttonBox" bindtap="tobuy" data-type="wantJoin" style="color:{{item.options.textColor}};background-color:{{item.options.btnColor}}" data-item="{{items}}">{{item.options.btnName}}</view>
            </view>
          </view>
            
          </scroll-view>
        </block>
        <block wx:else>
          <view class='noinfo' hidden='{{hidd}}'>赶快邀请好友参与活动吧~</view>
        </block>
      </view>
    </view>
  </view>
<!-- 活动进度 -->
<!-- 活动详情内容 -->
  <view class="detailBg" wx:if="{{item.type == 'detailBackgroundImg'}}" style="{{item.styleStr}};     background-image:url({{item.propValue.url}}">
  </view>
  <view wx:if="{{item.type=='detail-area'}}" style="{{item.styleStr}}" class="detailContent">
    <view class="jj">
      <view><image src="../../../../image/activity/activity_bargain_detail_left.png"></image></view>
      <view>商品详情</view>
      <view><image src="../../../../image/activity/activity_bargain_detail_right.png"></image></view>
    </view>
    <block wx:for="{{item.options}}" wx:key="index" wx:for-item="items">
      <view class="activity_detail_editor" wx:if="{{items.type == 'detail-text'}}">
        <rich-text nodes="{{items.propValue}}"  class="ql-editor"></rich-text> 
      </view>
      <image wx:if="{{items.type == 'detail-picture'}}" class="goods_list" src='{{items.url}}' mode="widthFix"></image>
      <video 
        id="myVideo"
        autoplay
        wx:if="{{items.type == 'detail-video'&&items.url!=''}}"
        object-fit='fill'
        src="{{items.url}}" 
        show-center-play-btn='true' 
        show-play-btn="true" 
        controls
      ></video>
    </block>
  </view>
  <!-- 活动详情内容 -->
  </block>
</scroll-view>
 <!-- 底部按钮 -->
<view  wx:if="{{!checkDetail&&!audit}}" class="footer {{showPoster? 'footerHide' : '' }}">
      <view class="buybtn" wx:if="{{isShare!=2&&sub_time1 < 0 ?true:false}}" style="color: {{warnInfo === '提醒我' ? '#000' : ''}}" bindtap="handleWarnMe">{{warnInfo}}</view>
      <view class="buybtn"  wx:elif="{{isShare!=2&&activity.activityConfig.groupLimit-activity.groupNum == 0}}" bindtap="{{isAttentionAgainFlag? '' : 'wantToJoin'}}" style="{{isAttentionAgainFlag? 'background: #999;':''}}">我想参加</view>
      <block wx:else>
        <block wx:for="{{activityPageConfig.componentData}}" wx:key="index">
          <!-- 参团按钮 -->
          <block wx:if="{{isShare==2}}">
            <block wx:if="{{item.type === 'button' && item.btEvent === 'open'}}">
              <view class="myOrder" style="{{item.styleStr}}background-image:url({{item.bgStatus==1?'item.imgOptions.url':''}});{{isAttentionAgainFlag? 'background-color: #999;':'rgba(252, 62, 27, 1)'}}"  wx:if="{{activity.activityConfig.groupLimit-activity.groupNum == 0}}" bindtap="{{isAttentionAgainFlag? '' : 'wantToJoin'}}" >我想参加</view>
              <view class="myOrder" wx:else style="{{item.styleStr}}background-image:url({{item.bgStatus==1?item.imgOptions.url:''}})" bindtap="tobuy" data-type="open">{{item.propValue}}</view>
            </block>
            <block wx:if="{{item.type === 'button' && item.btEvent === 'join'}}">
              <view class="myOrder  {{sub_time2 < 0 || sub_time < 0||needPeople==0?'disabled_btn':''}}" data-type="join" style="{{item.styleStr}}background-image:url({{item.bgStatus==1?item.imgOptions.url:''}})" bindtap="tobuy">{{item.propValue}}</view>
              <view class="groupTip">
                <span class="headImg"><image src="{{shareHeadImg?shareHeadImg:'https://txqmp.cpic.com.cn/uploads/img/qukeTab.png'}}"/></span>
                <span class="groupTipText" wx:if="{{needPeople!=0}}" :style="{ color: element.options.textColor }"> {{item.options.progress.before}}{{needPeople}}{{item.options.progress.after}}</span>
                <span class="groupTipText" wx:else :style="{ color: element.options.textColor }">团已满员</span>
              </view>
            </block>
          </block>
          <block wx:else>
            <block wx:if="{{item.type === 'button' && item.btEvent === 'myOpen'}}">
              <view class="myOrder" style="{{item.styleStr}}background-image:url({{item.bgStatus==1?item.imgOptions.url:''}});" bindtap="tobuy"  data-type="open">{{item.propValue}}</view>
            </block>
          </block>
         
        </block>
      </block>
    </view>
<!-- 选择区域的弹窗 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{chooseArea}}"></view>
<view class='{{chooseArea ? "chooseArea" : ""}} ' wx:if="{{chooseArea}}">
  <view class="modal_icon" catchtap="closeChooseAreaModal"><van-icon size="44rpx" color="#D0D2DB" name="cross" /></view>
  <view class="title">温馨提示</view>
  <view class="desc">请完善您的身份证号信息</view>
  <view class="choose">
    <view><span>*</span>姓名</view>
    <view class="chooseClick">
      <input type="text" placeholder="请输入" disabled="disabled" value="{{trueName}}" />
    </view>
  </view>
  <view class="choose">
    <view><span>*</span>证件类型</view>
    <view class="chooseClick">
      <picker class="truename1s"  bindchange="identityChange" range="{{identityType}}" value="{{identityIndex}}" range-key="name">
        <view class="weui-input">{{identity}}</view>
      </picker>
    </view>
  </view>
  <view class="choose">
    <view><span>*</span>身份证号</view>
    <view class="chooseClick">
      <input type="text" placeholder="请输入18位身份证号码" value="{{idCard}}" bindinput="idCardValue" />
    </view>
  </view>
  <view>
    <button catchtap="confrimChooseArea">保存</button>
  </view>
</view>
<!-- 选择区域的弹窗 -->

<!-- 拼团弹框 -->
<!-- -->
<block  wx:if="{{!showSwitchSale}}">
  <view class="mask" catchtouchmove="preventTouchMove" wx:if="{{shareGroup}}"></view>
  <view wx:if="{{shareGroup}}">
    <image class="groupBg" src="{{tipBackgroundImg.propValue.url}}"  style="{{tipBackgroundImg.styleStr}} "/>
    <view class='bargainSuccess'  style="{{tipBackgroundImg.styleStr}}">
      <image class="close" src="../../../../image/error.png"  bindtap="closeGroup"/>
      <view class="productInfo">
        <image src="{{activity.coverImg}}" />
        <view>
          <view class="activityTitle">{{activity.title}}</view>
          <view class="priceFont" wx:if="{{activity.price}}">
            <view style="color: #FF5030 ;margin-right: 16rpx;">
            ¥<text style="font-size: 36rpx;">{{activity.price}}</text>
            </view>
            <view class='bla_price'>原价：<text >¥</text>{{activity.originalPrice}}</view>
          </view>
        </view>
      </view>
    
    </view>
    <image class="groupBg" src="{{collageTip.propValue.url}}"  style="{{collageTip.styleStr}} "/>
    <view style="{{collageTip.styleStr}};color:{{collageTip.style.textColor}}" class="groupContent">
      <view class="showTitle" wx:if="{{showTitle}}">
        <image src="{{shareHeadImg}};" class="headerBox"/>
        <view>{{collageTip.options.startTips}}{{nickName}}{{collageTip.options.endTips}}</view>
      </view>
      <view class="peopleNumber" wx:if="{{showNum&&needPeople!=0}}">{{collageTip.options.progressTips.before}}{{needPeople}}{{collageTip.options.progressTips.after}} </view>
      <view class="peopleNumber" wx:if="{{needPeople==0}}">团已满员</view>
      <view class="time_box"  wx:if="{{showTime&&needPeople!=0}}">{{collageTip.options.timeTips}} <span>{{groupTime.day}}</span>天<span>{{groupTime.hour}}</span>时<span>{{groupTime.points}}</span>分<span>{{groupTime.seconds}}</span>秒</view>
    </view>
    <button class="confirm_btn" style="{{buttonTips.styleStr}}background-image:url({{buttonTips.bgStatus==1?buttonTips.imgOptions.url:''}});background-color:{{buttonTips.bgStatus!=1?buttonTips.style.backgroundColor:'transparent'}}" bindtap="closeGroup" data-type="close">{{buttonTips.propValue}}</button>
    <button class="confirm_btn {{sub_time2 < 0 || sub_time < 0||needPeople==0?'disabled_btn':''}}" style="{{buttonTip.styleStr}}background-image:url({{buttonTip.bgStatus==1?buttonTip.imgOptions.url:''}}" bindtap="tobuy" data-type="join">{{buttonTip.propValue}}</button>
  </view>
</block>
<!-- 拼团弹框 -->

<audit wx:if="{{audit}}" bind:reject="reject" activityId="{{ids}}" financeStatus="{{activity.financeRecheckStatus}}" activityType="{{category}}"></audit>
<reject-reason showDialog="{{showReject}}" activityId="{{ids}}" rejectReason="{{rejectReason}}" audit="{{activity.auditStatus}}" financeStatus="{{activity.financeStatus}}" rejectButton="triggerEvent"></reject-reason>
<contact module="活动参与" pageName="拼团活动详情" pageUrl="{{pageUrl}}" businessData="{{activity}}" isShow="{{isShow}}"></contact>
<overTips content="{{ message }}" showClose="{{false}}" isShowOverlay="{{isShowOverlay}}" bind:closeOverTip="closeOverTip"></overTips>

