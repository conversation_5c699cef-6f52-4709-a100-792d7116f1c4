/* pages/activityTemplate/golfArea/index/index.wxss */
.content{
  padding:24rpx 32rpx;
}
page{
  background: #F2F3F5;
}
.header{
  /* width: 100%; */
  height: 96rpx;
  line-height: 96rpx;
  font-size: 28rpx;
  color: #17204D;
  background: #fff;
  font-family: 'PingFang SC-Bold';
  border-radius: 16rpx;
  padding: 0 52rpx 0 32rpx;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  position: relative;
}
.calendar{
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}
.arrow{
  position: absolute;
  right: 24rpx;
  top: 34rpx;
}
.societyName{
  width: 480rpx !important;
  text-align: right !important;
}
.time{
  padding: 48rpx 32rpx 12rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 170rpx;
}
.title{
  font-size: 32rpx;
  color: #17204D;
  font-family: 'PingFang SC-Bold';
}
.period{
  display: flex;
  margin-top: 32rpx;
  flex-wrap: wrap;
}
.showText{
  margin-top: 32rpx;
  font-size:28rpx;
  text-align: center;
  color: #999999;
  margin-bottom: 20rpx;
}
.periods{
  display: flex;
  flex-wrap: wrap;
  position: relative;
}
.full{
  position: absolute;
  right: 20rpx;
  top: 0rpx;
  font-size: 20rpx;
  color: #7E849F;
  background: #E9E9ED;
  padding: 0 4rpx;
  border-radius: 8rpx;
  height: 40rpx;
  line-height: 40rpx;
  box-sizing: border-box;

}
.timeItem{
  width: 310rpx;
  margin-bottom: 32rpx;
  position: relative;
}
.van-radio__label{
  color: #17204D !important;
  font-size: 28rpx;
  font-family: 'PingFang SC-Medium';
}
.footer{
  position: fixed;
  width: 100%;
  height: 164rpx;
  padding: 16rpx 32rpx;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  background: #fff;
  box-shadow: -8rpx 0rpx 20rpx #ccc;
}
.place{
  width: 300rpx;
  height: 96rpx;
  line-height: 96rpx;
  font-size: 28rpx;
  color: #555C80;
  font-family: 'PingFang SC-Medium';
 
}
.number{
  color: #17204D;
  font-size: 36rpx;
  font-family: 'PingFang SC-Bold';
}
.btn{
  width:360rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  font-family: 'PingFang SC-Bold';
  /* background: linear-gradient(to right,#FC3E1B ,#FC6B37); */
  font-size: 32rpx;
  color: #fff;
  border-radius: 48rpx;
  background-size: 100% 100%;
}