/*
 * 使用逻辑判断来控制类名
 */

// 标点的逻辑计算
function spot (item, selectDay, spotMap, nowDay) {
  // 只有当前月才显示标点 //item.month === selectDay.month当前日期以后得日期都可约
  if (item.time >= nowDay.time) {
    // 通过年月日拼接的key来判断是否有标点
    var keys = item.year + '-' + (item.month < 10 ? '0' + item.month : item.month) + '-' + (item.day < 10 ? '0' + item.day : item.day)
    // if (typeof spotMap === 'object' && spotMap.hasOwnProperty(keys)) {
    //   return 'unAppointment-' + spotMap[keys].type;
    // }
    if (spotMap.length) {
      var list = []
      for (var i = 0; i < spotMap.length; i++) {
        if (keys === spotMap[i].date) {
          list.push(spotMap[i])
        }
      }
      if (list.length) {
        var type = []
        for (var i = 0; i < list.length; i++) {
          type.push(list[i].type)
        }
        return 'unAppointment-' + type.join('-')
      }
    }
  }
  if (item.time >= nowDay.time) {
    return 'spot';
  }
}
// 当前日期的逻辑计算(显示今天的日期)
function hasNow (item, nowDay) {
  if (
    item.year === nowDay.year &&
    item.month === nowDay.month &&
    item.day === nowDay.day
  ) {
    return 'now';
  }
  return '';
}
// 当前月的逻辑计算(其他月的日期变灰)
function hasNowMonth (item, selectDay) {
  if (item.year === selectDay.year && item.month === selectDay.month) {
    return '';
  }
  return 'other-month';
}
// 选中日期的逻辑计算(选中的日期变色)
function hasSelect (item, selectDay, oldCurrent, listIndex) {
  if (
    item.year === selectDay.year &&
    item.month === selectDay.month &&
    item.day === selectDay.day &&
    oldCurrent === listIndex
  ) {
    return 'select';
  }
  return '';
}
// 禁用日期的逻辑计算(禁用的日期变灰)
function hasDisable (item, disabledDateList) {
  // var key = 'disabled' + item.year + 'M' + item.month + 'D' + item.day;
  // if (disabledDateList[key]) {
  //   return 'other-month';
  // }
  // return 'other-month';
}

module.exports = {
  spot: spot,
  hasNow: hasNow,
  hasNowMonth: hasNowMonth,
  hasSelect: hasSelect,
  hasDisable: hasDisable,
}
