.tip {
  width: 100%;
  padding: 0 48rpx;
  box-sizing: border-box;
  opacity: 1;
}
.tip .tip_info {
  background-color: #fff;
  border-radius: 16rpx;
  text-align: center;
}
.tip .tip_info .tip_info_header {
  padding: 40rpx 0;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tip .tip_info .tip_info_header .tip_info_header_text {
  color: #333333;
  padding: 0 18rpx;
  font-weight: 700;
}
.tip .tip_info .tip_info_main {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 40rpx;
}
.tip .tip_info .tip_info_main .tip_info_main_item {
  width: 168rpx;
  background-color: #F5F6FA;
  color: #707E9E;
  font-size: 28rpx;
  border-radius: 8rpx;
  line-height: 64rpx;
  text-align: center;
  margin: 0 0 28rpx 36rpx;
}
.tip .tip_info .tip_info_main .currentI {
  background-color: #3B91FF;
  color: #fff;
}
