// pages/activityTemplate/golfArea/index/index.js
const API = require('../../../../api/request.js')
const http = require('../../../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    pageUrl:"pages/activityTemplate/general/golfArea/index",
    venueId:'1',
    society:"",//
    place:0,
    periodList:[],//当前日期
    currentperiodList:[],//当前时段列表
    spotMap:{},
    appointTime:[],//可约日期
    timeIndex:null,//时间段序号
    selTimes:''//选择的时段

  },
  onLoad(options) {
    console.log(options);
    let userId = wx.getStorageSync('userId')
    let userType = wx.getStorageSync('userType')
    let activityPeriodConfig = JSON.parse(wx.getStorageSync('activityPeriodConfig'))
   const gaokeNumber = activityPeriodConfig.componentData.find((item) => item.type === 'text')
   const button = activityPeriodConfig.componentData.find((item) => item.type === 'button')
   const timeText = activityPeriodConfig.componentData.find((item) => item.type === 'timeText')
   const titleText = activityPeriodConfig.componentData.find((item) => item.type === 'titleText')
   this.setData({
    activityPeriodConfig,
    gaokeNumber,
    button,
    timeText,
    titleText,
    applyStartTime: options.applyStartTime,
    isQtn: options.isQtn,
    userType,
    userId,
    lockDate: options.lockDate,
    activityId:options.id,
    society:options.society,
    venueId:options.venueId,
    activeMode: options.activeMode,
    customerPrice: options.customerPrice,
    salesPrice: options.salesPrice,
    salesPay: options.salesPay,
    secKill: options.secKill === 'false' ? false : true,
    title: options.title,
    coverImg: options.coverImg,
    creditsSource:options.creditsSource,
    usableCredits:options.usableCredits
    

  })

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if(this.data.venueId){
      this.getInfo()
    }
  },
  getSpotMap (appointTime) {
    console.log('--✨🍎', appointTime)
    const spotMap = {}
    appointTime.forEach(item => {
      spotMap[item] = 'spot'
    })
    return spotMap
  },
  getFullMap (appointTime) {
    const fullMap = {}
    appointTime.forEach(item => {
      fullMap[item] = 'full'
    })
    return fullMap
  },
  // 选择时间段
  changeTime(event){
    this.setData({
      radio: event.detail,
    });

  },
//选择日期
  selectDay(e){
    console.log(e.detail);
    let year = e.detail.year
    let month = e.detail.month
    let day = e.detail.day
    let months = month<10?'0'+month:month
    let days = day<10?'0'+day:day
    let selectDay = (year+'-'+months +'-'+ days).toString()
    let periodList = this.data.periodList&&this.data.periodList.find(ele => {
      return ele.date === selectDay
    })
    console.log(periodList);
    if(this.data.currentTime != selectDay){
      this.setData({
        selTimes:"",
        timeIndex:null
      })
    }
    this.setData({
      currentTime:selectDay,
      currentperiodList:periodList?periodList.periodList:[],
    })
  },
  //选择场馆
  selectArea(){
     wx.navigateBack({
      delta:1
    })
    // wx.navigateTo({
    //   url: `/pages/activityTemplate/general/selectArea/index?id=${this.data.venueId}`,
    // })
  },
  seletPeriod(e) {
    console.log(e);
    let place = this.data.currentperiodList[e.detail].place
    let golfPeriod = this.data.currentperiodList[e.detail].golfPeriod
    if ( place == 0) {
      wx.showModal({
        title: '提示',
        content: '预约失败—当场场次已被抢先预订，请选择其他场次',
        success(res) {
          if (res.confirm) {
            // console.log('用户点击确定')
          } else if (res.cancel) {
          }
        }
      })
    } else {
      let selPeriod = this.data.currentTime + " " + golfPeriod
      console.log(selPeriod);
      this.setData({
        timeIndex: e.detail,
        place,
        selTimes:selPeriod
      });
    }
  },

  async tobuy() {
    let {selTimes,society} = this.data
    if(society ==''){
      return wx.showToast({
        title: '请先选择场馆!',
        icon: 'none'
      })
    }
    if(selTimes ==''){
      return wx.showToast({
        title: '请先选择时段!',
        icon: 'none'
      })
    }
    let that = this
      if (that.data.userType == 3) {
        that.checkYwyLabel()
        return false
      } else {
        //校验是否同意隐私项
        if (http.userAre() && wx.getStorageSync('agree') == '0') {//已登录没有同意隐私项
          that.setData({
            disagree: true
          })

        } else {
          that.toActivityReady()
        }
       
      }
  },
  // 跳转报名页的判断
  toActivityReady() {
    let that = this
    if (that.data.userType != 3 && that.data.activeMode === 'PLUS' && that.data.salesPay == 1) {
      wx.showToast({
        title: '请联系您的客户经理进行活动报名',
        icon: 'none',
        duration: 1500,
      });
      return false
    }
    if (that.data.selTimes) {
      wx.setStorageSync('currentIndex', that.data.currentIndex)
      wx.setStorageSync('selTimes', that.data.selTimes)
      wx.setStorageSync('currentperiodList', that.data.currentperiodList)
      console.log('秒杀', that.data.secKill);
      let url = `/pages/activityready/activityready?selTimes=${that.data.selTimes}&golfId=${that.data.venueId}&id=${that.data.activityId}&maxNum=${that.data.maxNum}&minNum=${that.data.minNum}&salesMaxNum=${that.data.salesMaxNum}&salesMinNum=${that.data.salesMinNum}&activeMode=${that.data.activeMode}&customerPrice=${that.data.customerPrice}&salesPrice=${that.data.salesPrice}&salesPay=${that.data.salesPay}&cate=5&lockDate=${that.data.lockDate}&secKill=${that.data.secKill}&isQtn=${that.data.isQtn}&creditsSource=${that.data.creditsSource}&usableCredits=${that.data.usableCredits}`
      if (that.data.secKill) {
        url += `&title=${that.activity.title}&coverImg=${that.activity.coverImg}`
      }
      wx.navigateTo({
        url,
        success: function (res) { }
      })
    } else {
      wx.showToast({
        title: '请选择预约时间',
        icon: 'none',
        duration: 1500
      });
    }
  },
  getInfo() {
    let that = this
    that.setData({
      loadAnimation: true
    })
    let data = {
      golfId: that.data.venueId
    }
    API.getGolfAreaDetail(data)
      .then(res => {
        console.log(res.data);
        if (res.data.code == 200) {
          if (res.data.data.length > 0) {
            let appointTime = []
            let unAppointTime = []
            res.data.data.forEach(ele => {
              if(ele.canAppoint){
                let isFull = ele.periodList.every(item => item.place == 0 && item.canAppoint)
                if(isFull){
                  unAppointTime.push(ele.date)
                }else{
                  let isFull = ele.periodList.every(item => !item.canAppoint)
                  if(!isFull){
                    appointTime.push(ele.date)
                  }
                  
                }
              }
            })
           
            const spotMap = this.getSpotMap(appointTime)
            const fullMap = this.getFullMap(unAppointTime)
           
            that.setData({
              spotMap,
              fullMap,
              periodList: res.data.data,
              appointTime,
              venueId:that.data.venueId,
              currentperiodList: res.data.data[0].periodList,//默认时间段
              defaultTime: res.data.data[0].date.replace(/-/g, '/').toString(),
              currentTime: res.data.data[0].date,//当前日期
              
            })
            // let ele = res.data.data
            // let canAppoint = Object.keys(spotMap)
            // let isDefault = ele.find(item => item.date == canAppoint[0])
            // if(isDefault){
            //   that.setData({
            //     spotMap,
            //     fullMap,
            //     periodList: res.data.data,
            //     appointTime,
            //     venueId:that.data.venueId,
            //     currentperiodList: isDefault.periodList,//默认时间段
            //     defaultTime: isDefault.date.replace(/-/g, '/').toString(),
            //     currentTime: isDefault.date,//当前日期
            //   })
            // }
            //else{
            //   that.setData({
            //     spotMap,
            //     fullMap,
            //     periodList: res.data.data,
            //     appointTime,
            //     venueId:that.data.venueId,
            //     currentperiodList: [],//默认时间段
            //     defaultTime: '',
            //     currentTime: '',//当前日期
            //   })
            // }
            
          } else {
            that.setData({
              defaultTime: '',
              // currentTime: res.data.data[0].date,//当前日期
              venueId:that.data.venueId,
              periodList:[],//当前日期
              currentperiodList:[],//当前时段列表
              spotMap:{},
              place:0,
              appointTime:[],//可约日期
              timeIndex:null,//时间段序号
            })

          }

        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none',
            duration: 1500,
          });

        }
        that.setData({
          loadAnimation: false
        })
      })
  },
  // 业务员标签校验
  checkYwyLabel() {
    let that = this
    let data = {
      activityId: that.data.activityId
    }
    API.checkYwyLabel(data)
      .then(res => {
        if (res.data.code == 200) {
          if (!res.data.data) {
            wx.showToast({
              title: '当前为限定业余员标签活动，您无法参与！',
              icon: 'none',
              duration: 2000,
            });
          } else {
            that.toActivityReady()
          }
        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none',
            duration: 1500,
          });

        }
      })
  },

})