page {
  background-color: #fff;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.modalDlg {
  width: 80%;
  height: 300rpx;
  position: fixed;
  top: 62%;
  left: -1%;
  z-index: 100;
  margin: -370rpx 85rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
}

.modalDlg text {
  font-size: 30rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 180rpx;
  width: 100%;
  font-weight: bold;
}

.modalDlg button {
  width: 500rpx;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #FF564AFF;
  background-color: #FF564AFF;
  color: #fff;
}

.mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 10;
  opacity: 0.7;
}

.content_header {
  position: relative;
  width: 100%;
  flex-shrink: 0;
}

.content_header_menu {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 34rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #FFFFFF;
}

.content_header_menu_icon {
  position: absolute;
  left: 0;
}

.content {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  /* padding: 90rpx 0 120rpx; */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: calc(constant(safe-area-inset-bottom) - 12px);
  padding-bottom: calc(env(safe-area-inset-bottom) - 12px);
  position: relative;
}

.content_main {
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
}

.content_main_item {
  position: absolute;
  box-sizing: border-box;
}

.content_main_item_btn {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.content_main_item_btn_info {

}

.content_main_item_rule {
  padding: 0 30rpx;
  max-height: 500rpx;
  overflow: hidden;
  overflow-y: auto;
}

.content_main_item_img {
  width: 100%;
  height: 100%;
  display: block;
}

.backgroundImg,
title-img,
.clickBtn,
.title {
  position: absolute;
}

.clickBtn {
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: contain;
}

.backgroundImg {
  z-index: -1;
}

.title-img image {
  width: 100%;
  height: 100%;
}

.ruleTime {
  margin-top: 24rpx;
  color: #555C80;
  font-size: 28rpx;
}

.optionImg {
  width: 100%;
  height: 140rpx;
  margin-bottom: 20rpx;
}

.imageUrl {
  display: inline-block;
  width: 40%;
  border: 1px solid transparent;
  margin-bottom: 20rpx;
  padding: 15rpx;
  border-radius: 16rpx;
}

.van-radio {
  padding-bottom: 20rpx;
}

.van-checkbox {
  padding-bottom: 20rpx;
}

.imageUrl .van-radio {
  padding-bottom: 5rpx;
}

.imageUrl .van-checkbox__label {
  font-size: 28rpx;
  overflow-x: scroll;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.imageUrl .van-checkbox {
  padding-bottom: 5rpx;
}


.imageUrl:nth-child(2n+1) {
  margin-right: 40rpx;
}

.selectType {
  border: 1px solid #025CEA;
}

.content_info {
  width: 100%;
  padding: 216rpx 48rpx 0;
  box-sizing: border-box;
  height: calc(100% - 150rpx);
}

.content_info_main {
  background-color: #fff;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 20rpx;
  overflow: hidden;
  overflow-y: auto;
  height: calc(100% - 60rpx);
}

.content_info_item {
  padding-bottom: 40rpx;
}

.content_info_item_header {
  padding-bottom: 30rpx;
  display: flex;
  font-weight: 600;
}

.content_info_item_header_index {
  /* width: 40rpx; */
  /* height: 40rpx; */
  /* line-height: 40rpx; */
  /* text-align: center; */
  /* background: linear-gradient(to right, #025CEA, #4492FC); */
  /* border-radius: 12rpx 0 12rpx 0; */
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #2A3039;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.content_info_item_header_index_required {
  color: #F35B31;
}

.content_info_item_header_title {
  word-break: break-all;
  white-space: normal;
}

.content_info_item_main_textarea {
  width: 100%;
  height: 120rpx;
  font-size: 28rpx;
  background: #F7F8FA;
  border-radius: 16rpx;
  padding: 24rpx;
  box-sizing: border-box;
}

.content_info_item_main_mutiselect,.content_info_item_main_radio {
  padding-left: 20rpx;
}