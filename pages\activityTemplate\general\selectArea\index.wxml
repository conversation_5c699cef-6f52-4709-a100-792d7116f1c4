<view class="content">

  <view wx:for="{{golfList}}" class="venues {{id==item.id?'active':''}}" wx:key="index">
    <view class="address">
      <view class="venuesInfo">{{item.field}}</view>
      <view>{{item.address}}</view>
    </view>
    <van-icon name="checked" wx:if="{{item.id == id}}" bindtap="select" data-item="{{item}}" color="#FC3E1B"  />
    <van-icon name="circle" wx:else color="#7E849F" bindtap="select" data-item="{{item}}" />
  </view>
  
<view class="footer">
  <view class="btn" bindtap="sure">确定</view>
</view>
  
</view>
<!-- <contact module="活动参与" pageName="高客场地场次选择页面" pageUrl="{{pageUrl}}" businessData="{{activity}}"></contact> -->