<!-- 弹窗 -->
<view class="cloak" wx:if="{{showDialog}}">
  <view  class="rejectBox">
    <view class="rejectTitle">驳回理由</view>
    <view class="rejectInput__box">
      <textarea maxlength='100' class="rejectInput" value="{{rejectReason}}" bindinput="bindRejectReason" placeholder="说出驳回理由" placeholder-class="textarea-rejectReason"></textarea>
      <view class="reject--reason">
        <text class="textColor">{{rejectReason.length || 0}}</text>
        /100
      </view>
    </view>
    <view class="rejectBtn">
      <view class="rejectButton" bindtap='rejectButton' data-index="0">取消</view>
      <view class="rejectButton-backColor" bindtap='rejectButton' data-index="1">确定</view>
    </view>
  </view>
</view>