// pages/activityTemplate/springOuting/winning/index.js
import API from '../../../../api/request.js'

Page({

  init (options) {
    console.log('--options✨🍎', options)
    const winningPage = wx.getStorageSync("winningPage")
    const successPage = wx.getStorageSync("successPage")
    const data = JSON.parse(decodeURIComponent(options.p))
    const pageData = JSON.parse(successPage)
    if (winningPage) {
      this.setData({
        winningPage: JSON.parse(winningPage),
        winningData: data,
        tipsPage: pageData.successPage,
        attributes: pageData.attributes,
        acceptAwardBtn: pageData.acceptAwardBtn,
        acceptAwardImg: pageData.acceptAwardImg
      }, () => {
        this.initPage()
      })
    }
  },

  initPage () {
    console.log('--✨🍎', this.data.winningData)

  },

  changeLaunch () {
    this.setData({
      isLaunch: !this.data.isLaunch,
      contentStyle: this.data.isLaunch ? '' : 'height: 1078rpx;',
      scrollTop: 0
    })
  },


  onclickBtn (e) {
    console.log('--✨🍎', e)
    const { flagReceiveInfo } = this.data.winningData
    const { item } = e.currentTarget.dataset

    // 再去试一次
    if (item.btEvent === 'again') return wx.navigateBack()
    if (item.btEvent === 'other') return this.handlerOther(item)

    console.log('--✨🍎', flagReceiveInfo)

    // 领奖
    if (item.btEvent === 'receive' && flagReceiveInfo) return this.setData({ isShowInfo: true })
    this.saveRecord()
  },

  handlerOther (item) {
    const { setting } = item
    if (setting.jumpType === 'H5') {
      const src = encodeURIComponent(setting.jumpUrl)
      wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?src=${src}` })
    }
  },

  async saveRecord () {
    wx.showLoading({ title: '加载中' })
    const res = await API.saveRecord({ id: this.data.winningData.recordId })
    const { data, code, message } = res.data
    wx.hideLoading()
    if (code !== 200) return this.showTips(message)
    this.saveSuccess()
  },

  saveSuccess () {
    this.setData({ isShowInfo: false, showTipsPage: true })
  },

  closeTipsPage () {
    wx.navigateBack()
  },

  onClose () {
    this.setData({ isShowInfo: false })
  },

  showTips (title, duration = 2000) {
    wx.showToast({
      title,
      duration,
      icon: "none",
    })
  },

  /**
   * 页面的初始数据
   */
  data: {
    winningPage: [],
    winningData: '',
    tipsPage: [],
    attributes: '',
    acceptAwardImg: '',
    acceptAwardBtn: '',
    scrollTop:'',
    contentStyle: '',
    winningStyle: '',
    isLaunch: false,
    showTipsPage: false,
    isShowInfo: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad (options) {
    this.init(options)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload () {
    wx.removeStorageSync('successPage')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage () {

  }
})