<van-overlay show="{{ showAddress }}" >
  <view class="wrapper">
    <view class="block" >
      <view class="close" bindtap="onClickHide">
        <van-icon name="cross" color="#D0D2DB" size="16px"/>
      </view>
      <view class="header">
        <view>收货地址</view>
        <!-- <view class="tip">请选择您的收货方式(二选一)</view> -->
      </view>
      <!-- <van-radio-group value="{{ radio }}" bind:change="onChange" class="selectRadio">
        <van-radio name="1" checked-color="#4492FC">送货至机构</van-radio>
        <van-radio name="0" checked-color="#4492FC">送货上门</van-radio>
      </van-radio-group> -->
      <view class="dSteps">
        <scroll-view class="swiper-list" type="custom" scroll-y="true" >
          <view wx:if="{{radio=='2'&&homeAddress}}">
            <view class="choose">
              <view>
                <span>*</span>
                收货人
              </view>
              <view class="chooseClick">{{currentSelectDefault.name}}</view>
            </view>
            <view class="choose">
              <view>
                <span>*</span>
                手机号
              </view>
              <view class="chooseClick">{{currentSelectDefault.phone}}</view>
            </view>
            <view class="choose">
              <view>
                <span>*</span>
                收货地址
              </view>
              <view class="chooseClick">{{currentSelectDefault.province}}/{{currentSelectDefault.city}}/{{currentSelectDefault.area}}</view>
            </view>
            <view class="choose">
              <view></view>
              <view class="chooseClick">{{currentSelectDefault.address}}</view>
            </view>
            <view class="choose">
              <view>
                <span>*</span>
                备注
              </view>
              <!-- <view class="chooseClick" wx:if="{{radio==='1'}}">{{saleName}}_{{consumerName}}</view> -->
              <view class="chooseClick" >{{consumerName}}</view>
            </view>
          </view>
          <view class="noContent" wx:else>您还未添加默认收货地址~</view>
        </scroll-view>
      </view>
      <view class="footer-content">
        <!-- <block  wx:if="{{radio=='1'}}">
          <view class="saveBtn" wx:if="{{!institutionDefaultAddress}}">无地址可用</view>
          <button class="saveBtn" open-type='share' wx:else>分享客户</button>
        </block> -->
        <block wx:if="{{radio=='2'&& homeAddress}}">
          <view  class="repair"  bind:tap="goAdd">去修改</view>
          <button class="saveShare"  open-type='share'>分享客户</button>
        </block>
        <view  class="saveBtn" wx:if="{{radio=='2'&& !homeAddress}}" bind:tap="goAdd">去添加</view>
      </view>
    </view>
  </view>
</van-overlay>
