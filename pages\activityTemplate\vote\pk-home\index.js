// pages/activityTemplate/vote/pk-home/index.js

const wxtimer = require('../../../../utils/time.js');
const http = require('../../../../utils/util.js');
const app = getApp();
const API = require('../../../../api/request.js')
const CryptoJS = require('../../../../utils/CryptoJs')
const { showTab, handleUserStorage } = require('../../../../utils/aboutLogin')

const { setSalesCode, getWhetherAuth } = require('../../../../utils/querySalesCode')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    showPoster:false,
    loadAnimation: true, // 入场动画
    userId: '',
    userType: '',
    voteConfig: {}, // 和投票有关配置
    timeObj: {
      day: 1,
      hour: 1,
      points: 1,
      seconds: 1
    }, // 活动倒计时
    activityId: '', // 活动Id

    backgroundImg: null,
    componentData: [],
    voteProductList: [],
    leftVote: 4,
    rightVote: 40,
    height: '100vh',
    
    firstInPageTime: '', // 首次进入该页面时间
    leavePageTime: '', // 出该页面时间
    isClose: true, // 登陆者身份框
    personLogin: false, // 登陆者身份框
    showModalDlg: false, // 遮挡框不能点击（未登录）

    isShare: false,
    shareUserType: '',
    showSwitchSale: false,
    changeSalesCode: '',
    currentId: '',
    acceptId: '',
    audit:'', //活动审核判断
    showReject:false,
    rejectReason:'',
  },

  onLoad: function (options) {
    console.log('onload', options)
    var that = this
    let scene = options.scene
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      var id = option[0]
      that.setData({
        firstInPageTime: new Date().getTime(),
        activityId: id,
      })

    } else {
      that.setData({
        firstInPageTime: new Date().getTime(),
        // activityId: options.activityId,
        activityId: options.activityId,
        audit:options.audit || ''
      })
    }

    if (options.isShare) {
      this.setData({
        isShare: true,
        changeSalesCode: options.salesCode,
        changeSaleName: options.saleName,
        shareUserType: options.userType
      })
    }

    // that.onBuriedPoint('votePageScan', '投票首页浏览')
    let userId = wx.getStorageSync('userId')
    let userType = wx.getStorageSync('userType')
    that.setData({
      userId,
      userType,
      shareUserId: options.shareUserId
    })
    that.getHeight()
  },
  getHeight () {
    let systemInfo = wx.getSystemInfoSync()
    let menuButton = wx.getMenuButtonBoundingClientRect()
    let menuHeight = menuButton.height
    let menuRight = systemInfo.screenWidth - menuButton.right
    let menuBotton = menuButton.top - systemInfo.statusBarHeight
    let navBarHeight = (menuButton.top - systemInfo.statusBarHeight) * 2 + menuHeight + systemInfo.statusBarHeight
    const navBarData = {
      navBarHeight,
      menuRight,
      menuBotton,
      menuHeight
    }
    this.setData({
      navBarData
    })
  },
  onShow: function () {
    console.log('onshow');
    var that = this
    if (http.userAre() == '') { // 用户未登录，要求先点击加入活动
      // 用户id判断用户是否登录（微信弹窗）
      that.setData({
        showModalDlg: true
      })
      return
    } else {
      that.setData({
        showModalDlg: false
      })
      if (wx.getStorageSync('refreshUserInfo')) {
        that.getUserInfo();
      } else {
        if (wx.getStorageSync('salesCode') || wx.getStorageSync('pcode')) {
          that.setData({
            salesCode: wx.getStorageSync('salesCode'),
            credits: wx.getStorageSync('credits'),
            type: wx.getStorageSync('userType'),
          })
        }
        that.getIsRegister()
      }
    }
    if (this.data.audit != 1) {
      wx.showShareMenu({
        withShareTicket: false,
      })
    } else {
      wx.hideShareMenu({})
    }
  },
  btnClick(e){
    console.log(147,e.currentTarget.dataset)
    const type = e.currentTarget.dataset.type
    if(type == 'gift' || type == 'money'){
      this.voteClick(type)
    }
    if(type == 'shareBtn'){
    }
  },
  async voteClick(type){
    console.log(type, this.data.voteProductList)
    let productId = type == 'gift' ? this.data.voteProductList[0].id : this.data.voteProductList[1].id
    wx.showLoading()
    const res = await API.votePkTrigger({productId, activityId: this.data.activityId})
    wx.hideLoading()
    if(res.data.code == 200){
      wx.redirectTo({url: `/pages/activityTemplate/vote/pk-result/index?activityId=${this.data.activityId}&productType=${type}`})
    }else{
      wx.showToast({
        title: res.data.message,
        icon: 'none'
      })
    }
  },
  // 活动详情
  async activityDetail () {
    let that = this
    let data = {
      activityId: that.data.activityId
    }
    let res = null
    if(that.data.audit == 1){
      res = await API.auditVoteDetail({id:that.data.activityId})
    }else {
      res = await API.votePkDetail(data)
    }
    console.log('活动详情', res)
    if (res.data.code == '200') {
      const activityPageConfig = res.data.data.activityPageConfig ? JSON.parse(res.data.data.activityPageConfig).find((item) => item.title === '投票首页') : null
      if (activityPageConfig !== undefined && activityPageConfig !== null) {
        (activityPageConfig.componentData ? activityPageConfig.componentData : []).map((item) => {
          if (item.type === 'button' && (item.btEvent === 'vote' || item.btEvent === 'canvass')) {
            delete item.style.top
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        console.log('页面组件元素', activityPageConfig.componentData)
        const backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
        const voteDescConfig = activityPageConfig.componentData.find((item) => item.type === 'voteDesc')
        that.setData({
          backgroundImg: backgroundImg.propValue.url,
          height: backgroundImg.style.height,
          componentData: activityPageConfig.componentData,
          activityPageConfig: res.data.data.activityPageConfig,
          voteDescConfig: voteDescConfig || {},
        })
      }
      const activityPosterConfig = res.data.data.activityPageConfig ? JSON.parse(res.data.data.activityPageConfig).find((item) => item.title === '专属海报') : null
      if(activityPosterConfig !== undefined && activityPosterConfig !== null){
        (activityPosterConfig.componentData ? activityPosterConfig.componentData : []).map((item) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        const posterBackgroundImg = activityPosterConfig.componentData.find((item) => item.type === 'posterPic')
        const posterText = activityPosterConfig.componentData.find((item) => item.type === 'text')
        that.setData({
          posterBackgroundImg,
          activityPosterConfig,
          posterText
        })
      }
      console.log(activityPageConfig)
      // 计算倒计时
      let nowtime = new Date().getTime() //目前时间
      let from_time1 = -1
      let from_time2 = -1
      if (res.data.data.voteConfig.mobileLimit === 1) {
        var joinbeginTime = res.data.data.applyStartTime // 报名开始时间
        var joinendTime = res.data.data.applyEndTime // 报名结束时间
        let beginTime1 = new Date(joinbeginTime.replace(/-/g, '/')).getTime()  // 报名开始时间时间戳
        let endTime1 = new Date(joinendTime.replace(/-/g, '/')).getTime() // 报名结束时间时间戳毫秒
        from_time1 = parseInt((nowtime - beginTime1) / 1000) //正数： 活动开始  负数：活动开始倒计时
        from_time2 = parseInt((endTime1 - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束
        console.log('报名倒计时', from_time2)
        console.log(from_time1)
      }
      var applyStartTime = res.data.data.voteConfig.voteBegin // 投票开始时间
      var applyEndTime = res.data.data.voteConfig.voteEnd // 投票结束时间
      let createTime = new Date(applyStartTime.replace(/-/g, '/')).getTime()  // 活动开始时间时间戳
      let endTime = new Date(applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒
      let sub_time1 = parseInt((nowtime - createTime) / 1000) //正数： 活动开始  负数：活动开始倒计时
      let sub_time2 = parseInt((endTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束
      // console.log(sub_time1, sub_time2)
      that.setData({
        sub_time1,
        sub_time2,
        from_time1,
        from_time2,
        voteConfig: res.data.data.voteConfig,
        voteProductList: res.data.data.voteProductList,
        flagConnect: res.data.data.flagConnect,
        activityInfo: res.data.data,
        financeStatus: res.data.data.financeStatus||'',
        auditStatus: res.data.data.auditStatus||''
      })

      if (sub_time2 < 0 && this.data.audit != 1) {
        that.setData({
          isFinish: true,
        })
        wx.showToast({
          title: "投票已结束！",
          icon: "none",
          duration: 1500,
        })
      }
    } else {
      wx.showToast({
        title: res.data.message,
        icon: 'none'
      })
    }
    that.setData({
      loadAnimation: false
    })

    if (this.data.isShare) {
      let salesCode = wx.getStorageSync('salesCode')
      let saleName = wx.getStorageSync('saleName')
      if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
        this.setData({ showSwitchSale: true, currentId: saleName + salesCode, acceptId: this.data.changeSaleName + this.data.changeSalesCode })
      }
    }
  },
  closeChangeTieModal () {
    this.setData({ showSwitchSale: false })
  },
  // 关闭专属海报弹框
  onClickHide(){
    this.setData({
      showPoster: false
    })
  },
  // 下载海报
  downPoster(){
    this.setData({
      showPoster: true
    })
  },
  handlerStyle (style) {
    // console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  onHide () {
    let that = this
    that.setData({
      leavePageTime: new Date().getTime(),
    })
    let scanDuration = that.data.leavePageTime - that.data.firstInPageTime
    let day = Math.floor((scanDuration) / (86400000)); //天
    let hours = Math.floor((scanDuration % 86400000) / 3600000); //时
    let minutes = Math.floor((scanDuration % 3600000) / 60000); //分
    let seconds = Math.floor((scanDuration % 60000) / 1000); //秒
    let scanDuration1 = ''
    if (day > 0) {
      scanDuration1 = `${day}天${hours}时${minutes}分${seconds}秒`
    } else if (day == 0 && hours > 0) {
      scanDuration1 = `${hours}时${minutes}分${seconds}秒`
    } else if (hours == 0 && minutes > 0) {
      scanDuration1 = `${minutes}分${seconds}秒`
    } else if (minutes == 0 && seconds > 0) {
      scanDuration1 = `${seconds}秒`
    }
    // 埋点
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    app.sensors.track('votePageStayTime', {
      name: '投票首页停留时长',
      personScan: nickName,
      scanTime: scanDuration1,
      userId: userId
    });
    console.log('投票首页停留时长', scanDuration1, day, hours, minutes, seconds);
  },
  // 身份选择框关闭
  close (e) {
    console.log(e)
    var that = this
    that.setData({
      personLogin: false,
    })
    wx.showToast({
      title: '请选择身份登录',
      duration: 2000,
      icon: "none",
    })
  },
  //用户微信登录授权(点击进入)
  userInfoHandler () {
    console.log('执行了用户微信登录')
    var that = this;
    wx.removeStorageSync('defaultPerson');
    wx.removeStorageSync('refreshUserInfo')
    app.getUserInfo(function (userInfo) {
      console.log(userInfo)
      wx.setStorageSync('userId', userInfo.id);
      wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
      wx.setStorageSync('nickName', userInfo.nikename);
      wx.setStorageSync('openId', userInfo.openId);
      wx.showToast({
        title: '授权成功',
        icon: "none",
        duration: 1500,
      })
      that.setData({
        userId: userInfo.id,
        isAuthorize: true
      })
    })
    var timer3 = setInterval(() => {
      if (http.userAre() != '') {
        that.getUserInfo();
        clearInterval(timer3);
      }
    }, 100)
  },
  // 查询是否注册
  getIsRegister (userId) {
    let that = this
    if (http.unRegister()) {
      // 没有注册
      // 查询绑定的业务员
      that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode,that.data.changeSaleName)
      that.setData({
        personLogin: true,
        showModalDlg: false
      })
    } else {
      showTab()
      that.setData({
        showModalDlg: false
      })
    }
    that.activityDetail()
  },
  //   获取用户信息
  getUserInfo () {
    let that = this;
    var userId = wx.getStorageSync("userId")
    var token = wx.getStorageSync("token")
    that.setData({
      headImgUrl: (wx.getStorageSync('headImgUrl') == '') ? '../../image/user.png' : wx.getStorageSync('headImgUrl'),
      nickName: (wx.getStorageSync('nickName') == '') ? '请点击登录' : wx.getStorageSync('nickName'),
    })
    // 获取用户所属门店信息
    if (token) {
      API.getInfoById().then(res => {
        console.log('获取用户所属门店信息', res)

        if (res.data.code == 200) {
          console.log(res.data)
          that.setData({
            credits: res.data.data.credits,
            type: res.data.data.type,
            salesCode: res.data.data.salesCode,
          })
          console.log(that.data.salesCode);

          handleUserStorage(res.data.data)
          if (wx.getStorageSync('userId') !== '') {
            console.log('判断是否注册过')
            // 判断是否注册过    
            that.getIsRegister(wx.getStorageSync('userId'))
          }
        }
      })
    }
  },
  // 绑定业务员
  getSalesInfo () {
    let that = this
    let data = {
      id: that.data.shareUserId
    }
    API.getSalesInfo(data).then(res => {
      console.log(res)
      if (res.data.code == '200') {
        that.setData({
          saleId_num: res.data.data.salesCode
        })
        that.salenumber(res.data.data.salesCode)
      } else {
        wx.showToast({
          title: res.data.message,
          duration: 2000,
          icon: "none",
        })
      }
    })
  },
  //业务员查询
  salenumber (saleId) {
    var that = this
    if (!saleId) {
      return
    }
    let data = {
      code: CryptoJS.Encrypt(saleId)
    }
    API.getSaleInfoByCode(data).then(res => {
      if (res.data.code == 200) {
        if (res.data.data !== null) {
          let name = CryptoJS.Decrypt(res.data.data.name)
          let code = CryptoJS.Decrypt(res.data.data.code)
          that.setData({
            saleIdnum: true,
            saleId_num: code,
            saleName: name,
          })
          wx.setStorageSync('salesId_num', res.data.data.code)
          wx.setStorageSync('salesName', res.data.data.name)

        } else {
          that.setData({
            saleIdnum: false,
            saleName: '',
          })
          wx.setStorageSync('salesId_num', '')
          wx.setStorageSync('salesName', '')
        }
      } else {
        wx.showToast({
          title: res.data.message,
          duration: 2000,
          icon: "none",
        })
      }
    })

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage (res) {
    if(this.data.audit) return
    console.log('用户点击分享按钮', res)
    let userId = wx.getStorageSync('userId')
    var that = this
    let path = ``
    let activityId = that.data.activityId
    let title = ''
    let imageUrl = ''
    let salesCode = wx.getStorageSync('salesCode')
    let userType = wx.getStorageSync('userType')
    let saleName = wx.getStorageSync('saleName')

    const { shareText } = this.data.activityInfo
    console.log('shareText--✨🍎', shareText)
    if (shareText) {
      title = shareText.replace('#微信昵称#', wx.getStorageSync('nickName')).replace('#活动名称#', this.data.activityInfo.title)
    }

    // .replace('#作品名称#',wx.getStorageSync('nickName'))
    imageUrl = this.data.activityInfo.shareUrl
    path = `/pages/activityTemplate/vote/pk-home/index?activityId=${activityId}&shareUserId=${wx.getStorageSync('userId')}&clientShareUserId=${userId}`
  
    // that.onBuriedPoint('voteShare', '投票分享')
    return {
      title: title,
      imageUrl: imageUrl,
      path: `${path}&salesCode=${salesCode}&saleName=${saleName}&isShare=1&userType=${userType}`,
    }
  },
  // 返回上一个页面
  back () {
    wx.navigateBack({ url: -1 })
  },
  reject() {
    console.log('触发了')
    this.setData({
      showReject:true,
      rejectReason:'',

    })
  },
})