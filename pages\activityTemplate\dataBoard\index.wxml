
<view class="content">
  <image class="content--back" src="https://txqmp.cpic.com.cn/uploads/img/dataBoardBg.png" />
  <!-- 导航标题 -->
  <view class="navTitle" style="height:{{navBarData.navBarHeight}}px;">
    <view class="nav_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px;">
      <van-icon bindtap="backPage" class="nav--arrow" name="arrow-left" color="#fff"/>
      <text>数据看板</text>
    </view>
  </view>
  <!--  -->
  <view class="appointment" style="margin-top:{{navBarData.navBarHeight + 16}}px;">
    <view class="tabBar">
      <view wx:for="{{tabBarList}}" wx:key="index" data-id="{{item.id}}" class="{{tabBarId==item.id?'activeBar':''}}" bind:tap="selectTabBar">{{item.name}}</view>
    </view>
    <view class="dataContent">
      <view class="filterBox">
        <view class="dataTime">
          <view class="yearMonth" bind:tap="yearMonth">
            <text><text>{{currentYear}}</text>年<text>{{ currentMonth<10?'0'+currentMonth:currentMonth}}</text>月<text wx:if="{{tabBarId === 0}}">{{currentDay<10?'0'+currentDay:currentDay}}日</text></text>
            <van-icon name="arrow-down" class="selectYear" color="#7E849F"/>
          </view>
          <!-- 周选择器弹窗 -->
          <select-week  wx:if="{{tabBarId === 1}}" defaultYear="{{currentYear}}" defaultMonth="{{currentMonth}}" currentDay="{{selectWeek}}" bind:onConfirm="confirmWeek" bind:firstWeek="firstWeek"/>
        </view>
        <view class="activeTopic" bind:tap="selectTopic">
          <span>{{activityTopic?activityTopic:'全部'}}</span>
          <van-icon name="arrow-down" class="arrowDown"  color="#7E849F"/>
        </view>
      </view>
      <view class="showData">
        <view class="showDataTop">
          <view>
            <view class="showDataTopTitle">活动数</view>
            <view class="activityNum" bind:tap="goStatistics">
              {{activityNum}}<span>场<van-icon name="play" class="playIcon"/></span>
            </view>
          </view>
          <view class="companyBox" bind:tap="selectCompany">
            <span>{{companyName}}</span>
            <van-icon name="arrow" class="arrowRight" size="24rpx" color="#025CEA" />
          </view>
        </view>
        <view class="centerContent">
          <view class="registration">
            <view>报名人数</view>
            <view class="peopleNum">{{applyNum}}人(去重)</view>
          </view>
          <view class="line"></view>
          <view class="verification">
            <view>核销人数</view>
            <view class="peopleNum">{{writeOffNum}}人(去重)</view>
          
          </view>
        </view>
        <view><text class="attention">注：</text>仅包含订单类活动(报名、商品、高客、拼团、砍价活动)</view>
      </view>
      <view class="showTableData" wx:if="{{level<4}}">
        <view class="tableTitle">数据明细</view>
        <s-table config="{{tableConfig}}" rows="{{rows}}" keys="{{keys}}" headers="{{headers}}"></s-table>
      </view>
    
    </view>
    
  </view>
</view>

<!-- 多选 -->
<!-- <mulselect show="{{showTopic}}" bind:confirm="sureData" bind:closeTopic="closeTopic" selectedCode="{{selectedCode}}"  columns="{{options}}"></mulselect> -->
<!-- 级联选择类型主题 -->
<cascader show="{{showCascader}}" bind:onFinish="selectedTopic" cascaderValue="{{cascaderValue}}" topicList="{{topicList}}"
/>
<!-- 日期选择 -->
<van-popup
  show="{{ showYearMonth }}"
  position="bottom"
  custom-style="height: 40%"
>
  <van-datetime-picker
    wx:if="{{tabBarId === 0}}"
    type="date"
    value="{{ currentDate }}"
    max-date="{{ maxDate }}"
    min-date="{{ minDate }}"
    bind:confirm="confirmDate"
    bind:cancel="cancel"
  />
  <van-datetime-picker
    wx:else
    type="year-month"
    value="{{ currentDate }}"
    max-date="{{ maxDate }}"
    min-date="{{ minDate }}"
    bind:confirm="confirmDate"
    bind:cancel="cancel"
  />
</van-popup>
<!-- 选择机构 -->
<van-overlay show="{{ showCompany }}" bind:click="onClickHide" custom-style="z-index:100">
  <view class="wrapper">
    <view class="block">
      <scroll-view scroll-y="true" class="scrollView" >
        <tree
          dataTree="{{dataTree}}"
          selectKey="{{selectCompanyId}}"
          bind:select="handleSelect"
        ></tree>
      </scroll-view>
      <van-icon name="close" size="26" class="closeOverlay"  bind:tap="onClickHide"/>
    </view>
  </view>
</van-overlay>


