<view class="lottery_rule" style="background-image: url({{backgroundImg}});">
  <view class="rule_main">
    <block wx:for="{{list}}" wx:key="index">
      <view class="rule_time" wx:if="{{item.type == 'ruleTime'}}" style="top:{{item.style.top*2}}rpx;">
        <view class="rule_desc_title" style="margin-bottom: 12rpx;">活动时间:</view> 
        <view>{{start}} - {{end}}</view>
      </view>
      <view class="rule_text" wx:if="{{item.type == 'rule'}}" style="top:{{item.style.top*2}}rpx;">
        <view class="rule_desc_title" style="margin-top: 12rpx;">活动说明</view>
        <view class="jj_content" wx:if="{{content !== ''}}">
          <rich-text nodes="{{content}}"  class="ql-editor"></rich-text>
        </view>
      </view>
    </block>
  </view>
</view>
<!-- 活动审核显示底部操作按钮 -->
<audit activityId="{{activityId}}" activityType="14" wx:if="{{audit}}" bind:reject="reject"></audit>
<!-- 活动审核显示底部操作按钮 -->
<!-- 活动审核驳回弹框 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" activityType="14" rejectReason="{{rejectReason}}" audit="{{auditStatus}}" financeStatus="{{financeStatus}}" linkTimes='2'></reject-reason>
<!-- 活动审核驳回弹框 -->