
const CryptoJS = require('../../../utils/CryptoJs.js');
const app = getApp();
const API = require('../../../api/request')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showGroup:false,
    lockDate:'',
    showReceive:false,

  },
 
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log(options,999);
    if(options.orderId){
      await this.getDetail(options.orderId)
    }else{
      this.setData({
        ids: options.id,
        recommendService:options.recommendService,
        entryRelatedActivityId: options.entryRelatedActivityId,
        distributionChannel:options.distributionChannel,
        title:options.title,
      })
      if(options.distributionChannel=='PSA'||options.distributionChannel=='XKQ'){
        let qw = wx.getStorageSync('saleWxQrcode')
        this.setData({
          saleWxQrcode:qw
        })
      }
      if(options.cate == 6){
        if(options.canJoinPersons*1 === 0) return
        const { cate, groupNo, id,coverImg, activeMode,endTime,money,groupText,title ,firstOpen,recommendService} = options
        this.setData({
          cate,
          ids:id,
          titleName:title,
          groupNo,
          activeMode,
          endTime,
          totalMoney:money,
          coverImg,
          showGroup:true,
          groupText,
          firstOpen:firstOpen =='true'?true:false,
          recommendService
        })
      }
      if (options.lockDate) {
        let topicObj = { 1 : "太保蓝本", 2: "E锦囊", 3: "线上抽奖" }
        this.setData({
          lockDate:options.lockDate,
          recommendService:options.recommendService,
          topic:topicObj[options.recommendService]
        })
      }
      // let registrationPeople = wx.getStorageSync('selectPeople') //报名人
      let userType = wx.getStorageSync('userType') //登录人身份
      // let userPhone = wx.getStorageSync('phonebtn') //登录人手机号
      if(options.recommendService=='3'){
        this.setData({
          showReceive:true
        })
      }
      if(userType === 1){
        if(options.recommendService==='2'){
            this.setData({
              showReceive:true
            })
        }
    
      }
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    let userType = wx.getStorageSync('userType')
    if(userType === 1){
        if(this.data.recommendService === '1'){
            await this.checkReceive()
        }
    }
  },
  onUnload () {
    const source = wx.getStorageSync('source')
    if (source) {
      wx.removeStorageSync('source')
    }
    wx.removeStorageSync('saleWxQrcode')
  },
//   查询客户太保蓝本是否领取
  async checkReceive(){
    let res = await API.checkBlueprint()
    console.log(res.data.data);
    if(res.data.code === 200){
        if(!res.data.data){
            this.setData({
                showReceive:true
            })
        }else{
            this.setData({
                showReceive:false
            })
        }
    }else{
        app.showToast(res.data.message)
    }
  },
  // 根据订单号获取活动详情
  async getDetail(id){
    let res = await API.getByOrderNo({orderNo:id})
    // console.log(res.data.data.activity);
    if(res.data.code == 200){
      let {activity} = res.data.data
      this.setData({
        ids: activity.id,
        recommendService:activity.recommendService,
        entryRelatedActivityId: activity.entryRelatedActivityId,
      })
      if (activity.lockDate) {
        let topicObj = { 1 : "太保蓝本", 2: "E锦囊", 3: "线上抽奖" }
        this.setData({
          lockDate:activity.lockDate,
          recommendService:activity.recommendService,
          topic:topicObj[activity.recommendService]
        })
      }
      if(activity.category == 6){
        const { category, id,coverImg, activeMode,price,title,recommendService,purchase} = activity
        if(res.data.data.canJoinPersons ===0) return
        this.setData({
          category,
          ids:id,
          titleName:title,
          groupNo:res.data.data.groupNo,
          activeMode,
          endTime:purchase.validTime||null,
          totalMoney:price,
          coverImg,
          showGroup:true,
          groupText:purchase.groupText,
          firstOpen:res.data.data.groupFlag,
          recommendService
        })
      }
     
      let userType = wx.getStorageSync('userType') //登录人身份
      if(activity.recommendService=='3'){
        this.setData({
          showReceive:true
        })
      }
      if(userType === 1){
        if(activity.recommendService=='2'){
            this.setData({
              showReceive:true
            })
        }
        if(activity.recommendService == '1'){
            await this.checkReceive()
        }
      }
      if(activity.isQtn === 1){
        this.toSubscribeOrderFeedback(id)
      }
    }else{
      app.showToast(res.data.message)
    }
  },
  // 订阅订单反馈
  toSubscribeOrderFeedback (ids) {
    let message = 'c27OhZ6QGsfsDZTHfawzh26mFWGbIxwOWpSrRbCXEOk'
    let id = ids
    return new Promise((resolve, reject) => {
      wx.getSetting({
        withSubscriptions: true,//是否同时获取用户订阅消息的订阅状态，默认不获取
        success: (res) => {
          console.log('subscribe Success', res)
          if (res.subscriptionsSetting.mainSwitch) { //用户是否打开了接收消息的总开关
            if (res.subscriptionsSetting.itemSettings != null && res.subscriptionsSetting.itemSettings[message]) { // 用户同意总是保持是否推送消息的选择, 这里表示以后不会再拉起推送消息的授权
              const status = res.subscriptionsSetting.itemSettings[message]
              if (status == 'accept') { // accept：接收，reject：拒绝，ban：已被后台禁止
                wx.requestSubscribeMessage({
                  tmplIds: [message],
                  success: (item) => {
                    console.log('wx.requestSubscribeMessage SUCCESS');
                    if (item[message] == 'accept') {
                      console.log('调接口了订阅号accept');
                      API.subscribeOrde({ activityId: id, status: 1 }).then(res => {
                        resolve(res)
                      }).catch(err => {
                        reject(err)
                      })
                    } else {
                      resolve(true)
                    }
                  },
                  fail: (res) => { console.log('2', res) },
                })
              } else {
                wx.openSetting({
                  withSubscriptions: true,
                  success: (rej) => {
                    console.log('openSetting', rej);
                    if (rej.subscriptionsSetting.itemSettings != null && rej.subscriptionsSetting.itemSettings[message] == 'accept') {
                      wx.showToast({
                        title: '权限修改成功',
                        icon: 'none',
                        duration: 3000
                      })
                      resolve(rej)
                    }
                  }
                })
              }
            } else {
              console.log('准备出弹框')
              wx.requestSubscribeMessage({
                tmplIds: [message],
                success: (res) => {
                  if (res[message] == 'accept') {
                    API.subscribeOrde({ activityId: id, status: 1 }).then(res => {
                      resolve(res)
                    }).catch(err => {
                      reject(err)
                    })
                  } else {
                    resolve(true)
                  }
                },
                fail: (res) => { console.log('2', res) },
              })
            }
          } else {
            resolve(true)
          }
        }
      })
    })
  },
  //立即领取
  async receive(){
    // 'E锦囊',id:'2', '太保蓝本锦囊','1', 抽奖活动 id 3
    let res = await API.getAesSalesCode()
    console.log(res);
    if(res.data.code!=200) return  wx.showToast({
        title: res.data.message,
        duration: 2000,
        icon: "none",
    });
   const saleName = wx.getStorageSync('saleName')
   const salesCode = res.data.data //CryptoJS.Encrypt1(wx.getStorageSync('salesCode'))
   const { fgsCode } = wx.getStorageSync('getInfoByIdData')
   const { shareEurl,blueUrl} = app.kyObj
   console.log(app.env);
   let envVersion = app.env==='development'?'trial':'release'
   let blueShareUrl = blueUrl+`&empno=${salesCode}`
   console.log(`/pages/newWeb/index?empno=${salesCode}&empname=${saleName}&fgsCode=${fgsCode}&shareUrl=${encodeURIComponent(blueShareUrl)}`);
  if(this.data.recommendService == '1'){
    wx.navigateToMiniProgram({
      appId: 'wxcbc8918bd579e80d',
      path: `/pages/newWeb/index?empno=${salesCode}&empname=${saleName}&fgsCode=${fgsCode}&shareUrl=${encodeURIComponent(blueShareUrl)}`,
      extraData: {
        gh: 'gh_ae9535458e1a'
      },
      envVersion: envVersion,
      complete: () => { }
    })

  }else if(this.data.recommendService == '2'){
    wx.navigateToMiniProgram({
      appId: 'wxcbc8918bd579e80d',
      path: `/pages/newWeb/index?empno=${salesCode}&empname=${saleName}&fgsCode=${fgsCode}&shareUrl=${encodeURIComponent(shareEurl)}`,
      extraData: {
        gh: 'gh_ae9535458e1a'
      },
      envVersion: envVersion,
      complete: () => { }
    })

  }else if(this.data.recommendService == '3'){
    wx.navigateTo({
      url: `/pages/activityTemplate/lottery/lottery?activityId=${this.data.entryRelatedActivityId}&qtnId=${this.data.ids}&mode=ENTRY`,
    })
  }
   
  },
  successCancel () {
    this.setData({
      showGroup: false,
    })
  },
  goBack(){
    const isHtPay = wx.getStorageSync('isHtPay')
    if(isHtPay && isHtPay == 1) {
      wx.removeStorageSync('isHtPay')
      wx.reLaunch({
        url: `/pages/fuseSpecial/haitong/index`,
      })
      return
    }

    const source = wx.getStorageSync('source')
    if (source) {
      wx.removeStorageSync('source')
      wx.reLaunch({
        url: `/pages/fuseSpecial/home/<USER>
      })
    } else {
      wx.reLaunch({
        url: '/pages/home/<USER>',
      })
    }
    
  },
  goOrder(){
    const source = wx.getStorageSync('source')
    if (source) {
      wx.removeStorageSync('source')
    }
    wx.reLaunch({
      url: '/pages/myactivity/myactivity',
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (res) {
    console.log('分享', res.target.dataset)
    let forward = res.target.dataset.forward
    let that= this
      // 埋点
      let nickName = wx.getStorageSync('nickName')
      let userId = wx.getStorageSync('userId')
      let userType = wx.getStorageSync('userType')
      let avatarUrl = wx.getStorageSync('headImgUrl')
      let activityName = res.target.dataset.activityname
      let salesCode = wx.getStorageSync('salesCode')
      let saleName = wx.getStorageSync('saleName')
      let time = new Date().toLocaleString()
      app.sensors.track('activityShare', {
        name: '分享拼团活动',
        activityId: that.data.ids,
        activityName: activityName,
        personScan: nickName,
        shareTime: time,
        userId: userId
      });
    
    if (res.from === 'button' && forward == 1) {
      // console.log(that.data.groupText)
      let result = that.data.groupText.replace('#微信昵称#', nickName);
      let titles = result.replace('#活动名称#', that.data.titleName);
      // let titles = `好友${nickName}发起的团`
      return {
        title: titles,
        imageUrl: `${that.data.coverImg}`,
        path: `/pages/activityTemplate/group/home/<USER>
      }
    }

  },
})