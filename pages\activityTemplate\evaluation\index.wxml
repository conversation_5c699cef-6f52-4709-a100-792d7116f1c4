<van-overlay show="{{ showModalDlg }}" z-index="99999999">
  <view class="wrapper">
    <view class="wrapper_info">
      <view class="wrapper_info_title">参与活动</view>
      <view class="wrapper_info_btn" bindtap="userInfoHandler">点击参与</view>
    </view>
  </view>
</van-overlay>
<view class="recordContent">
  <scroll-view class="content" scroll-y="true" >
    <view class="evaluateContent">
      <view wx:for="{{showArr1}}" wx:key="this" wx:for-index="index">
        <view wx:if="{{item.type == 'select'}}" class="evaluate__row">
          <view class="row__title">
            <text class="row--require">*</text>
            {{item.title}}
          </view>
          <view class="row__starList">
            <van-rate readonly="{{showArr1Disable}}" color="#025CEA" void-color="#D0D2DB" value="{{ item.value }}" count="5" bind:change="onChangeStar" data-index="{{index}}" data-status="showArr1" />
          </view>
        </view>
        <view wx:if="{{item.type == 'special'}}" class="evaluate__row">
          <view class="row__title">
            <text class="row--require">*</text>
            {{item.title}}
          </view>
          <view class="row__starList">
            <van-rate readonly="{{showArr1Disable}}" color="#025CEA" void-color="#D0D2DB" value="{{ item.value }}" count="5" bind:change="onChangeSpecialStar" data-index="{{index}}" data-status="showArr1" />
            <view class="special" bind:tap="selectComplaint" data-status="showArr1" data-index="{{index}}">
             <image src="{{complaintValue?'/image/fuse/complaint.png':'/image/fuse/unComplaint.png'}}" />
             <view class="complaint"> 投诉</view>
            
            </view>

          </view>
        </view>
        <view wx:if="{{item.type == 'INPUT'||item.type=='input'}}" class="evaluate__row">
          <view class="row__title">
            <text class="row--require">*</text>
            {{item.title}}
          </view>
          <textarea disabled="{{showArr1Disable}}" class="row__evaluate" bindinput="writeValue" data-index="{{index}}" data-status="showArr1" value="{{item.value}}" placeholder="请至少输入10字以上" placeholder-class="textarea-placeholder"></textarea>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
<view class="btnSubmit__issue" wx:if="{{showArr1.length!=0}}">
  <view class="btn" bindtap="submit">提交</view>
</view>

<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close"></login-box>
<van-dialog id="van-dialog" />
<globalPrompt model:show="{{ showGlobalPrompt }}" content="{{globalPromptMessage}}"></globalPrompt>