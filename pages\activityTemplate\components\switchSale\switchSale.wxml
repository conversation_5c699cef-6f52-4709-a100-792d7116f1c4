<van-overlay show="{{ _show }}">
  <view class="wrapper">
    <image src="../../../../image/close.png" class="close" bindtap="closeChangeTieModal" wx:if="{{isShowClose}}"/>
    <view style="font-size: 32rpx;text-align: center;">温馨提示</view>
    <view class="title">
      需要变更为您服务的业务员吗？
    </view>
    <view class="tip"> 您当前绑定的业务员是：</view>
    <view class="changeTieClick">
      <input type="text" placeholder="" value="{{currentId}}" bindinput="idCardValue" disabled="disabled"/>
    </view>
    <view class="tip"> 您接受活动分享的业务员是：</view>
    <view class="changeTieClick">
      <input type="text" placeholder="" value="{{acceptId}}" bindinput="idCardValue" />
    </view>
  
    <view class="footer" wx:if="{{isShowCancel}}">
      <view class="sure" bindtap="confirmChangeTieModal">确认变更</view>
      <view class="noNeed"  bindtap="closeChangeTieModal">暂不需要</view>
    </view>
    <view class="footer onlyChange" wx:else>
      <view class="sure" bindtap="confirmChangeTieModal">确认变更</view>
    </view>
  </view>
</van-overlay>

