const API = require('../../../../api/request')
const { formatPhone, formatIdcard,identity,Sensitive,identityEnum } = require('../../../../utils/phone-idcard');
Component({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
    styleIsolation:'shared'
  },
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  properties: {
    // 弹窗标题
    dialogData: {            // 属性名
      type: Object,     // 类型（必填），目前接受的类型包括：String, Number, Boolean, Object, Array, null（表示任意类型）
      value: {}    // 属性初始值（可选），如果未指定则会根据类型选择一个
    }, 
    acceptDialogData:{
      type: Object,
      value: {}
    },
    activityTemplateType:{
      type: Number,
      value: 14
    },
    prizeObj:{
      type: Object,
      value: {}  
    },
    activityId:{
      type:String,
      value:''
    },
    mode:{
      type:String,
      value:''
    },
    isInfo: {
      type:Boolean,
      value: false
    },
    isShow: {
      type:Boolean,
      value: false
    },
    isSuccess:{
      type:Boolean,
      value: false
    },
    attributes:{
      type:Array,
      value:[]
    },
    receiveType: {
      type:String,
      value:''
    },
    isFirst: {
      type:Boolean,
      value:''
    },
    getUrlRes:{
      type: Object,
      value: {}  
    },
  },

  /**
   * 私有数据,组件的初始数据
   * 可用于模版渲染
   */
  data: {
    // 弹窗显示控制
    // isShow:false,
    height:612,
    name:'',
    phone:'',
    area:'',
    address:'',
    customItem:'',
    region: [],
    attributeData:{},
    identityType:identityEnum,
    identity:'',
    identityIndex:0,
    cardType:'ID_CARD'
  },

  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    changeInput(e){
      console.log(e)
      let index = e.target.dataset.index //属性index
      let propertyCode =  e.target.dataset.name
      let attributes = this.data.attributes
      let attributeData = this.data.attributeData
      attributes[index].defaultValue = e.detail.value //修改默认值
      attributeData[propertyCode] = e.detail.value
      console.log(index,propertyCode)
      this.setData({
        attributes,
        attributeData
      })

    },
    bindRegionChange: function (e) {
      console.log(e)
      console.log('picker发送选择改变，携带值为', e.detail.value)
      let attributes = this.data.attributes
      attributes[e.target.dataset.index].defaultValue = e.detail.value[0] //修改默认值
      this.setData({
        region: e.detail.value,
        attributes
       
      })
      
    },
    //隐藏弹框
    hideDialog(){
      this.setData({
        isShow: !this.data.isShow,

      })
    },
    hideInfoDialog(){
      this.setData({
       
        isInfo: !this.data.isInfo,
      })
    },
    //展示弹框
    showDialog(){
      this.setData({
        isShow: !this.data.isShow
      })
    },
        //展示弹框
        showInfoDialog(){
          this.setData({
            isInfo: !this.data.isInfo
          })
          console.log(this.data.region)
        },
     /*
     * 内部私有方法建议以下划线开头
     * triggerEvent 用于触发事件
     */
    _cancelEvent(){
      //触发取消回调
      this.triggerEvent("cancelEvent")
    },
    _confirmEvent(e){
      //触发成功回调
      if(!this.data.isSuccess && (this.data.activityTemplateType == 14||this.data.activityTemplateType == 7)){
        const item = e.target.dataset.item
        console.log(item);
        this.triggerEvent("confirmEvent", {url: item.setting.jumpUrl}, {});
        return
      }
      if(this.data.isSuccess && (this.data.activityTemplateType == 7)){
        const item = e.target.dataset.item
        console.log(item);
        this.triggerEvent("confirmEvent", {url: item.setting.jumpUrl}, {});
        return
      }
      this.triggerEvent("confirmEvent");
    },
   async  _saveEvent(){
      //触发成功回调
      let arr = this.data.attributes
      console.log(arr)
      let findViod = arr.find((item)=>item.required == 1 && item.defaultValue == null)
      if(findViod){
        wx.showToast({
          title: `${findViod.propertyName}不能为空!`,
          icon: 'none',
          duration: 2000
        })
        return
      }
      const idCardList = arr.filter((item) =>  item.propertyName === '证件号')
      const cardTypeList = arr.filter((item) =>  item.propertyName === '证件类型')
      console.log(idCardList,cardTypeList)
      if(cardTypeList.length !== 0){
        if (!identity(idCardList[0].defaultValue,cardTypeList[0].defaultValue)) {
          wx.showToast({
            title: "证件号输入格式有误",
            icon: 'none',
          })
          return false;
        }
      }
     let obj = {}
     if(this.data.region[0]!=undefined){
      let data = {
        province:this.data.region[0]==undefined?undefined:this.data.region[0],
        city :this.data.region[1]==undefined?undefined:this.data.region[1],
        area :this.data.region[2]==undefined?undefined:this.data.region[2],
      }
      obj = Object.assign({},this.data.attributeData,data)

     }else{
      obj = Object.assign({},this.data.attributeData)
     }

      const json ={
        id:this.data.activityId,
        address:JSON.stringify(obj)
      }
      console.log(json)
      let res = await API.saveRecord(json)
      if(res.data.code == 200){
        this.triggerEvent("saveEvent",res,{})
        this.setData({
          region:[]
        })
        if(this.data.isFirst){
          // 今日未跳转第三方
          this.setData({isFirst: false})
          // console.log(202, this.data.getUrlRes)
          const src = encodeURIComponent(this.data.getUrlRes.returnUrl)
          wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${this.data.activityId}&src=${src}`})
        }
      }else{
        wx.showToast({
          title: `${res.data.message}!`,
          icon: 'none',
          duration: 2000
        })
      }
    },
    // 更改证件类型
  identityChange (e) {
    console.log(e)
    const that = this
    const {index,name} = e.target.dataset
    const identityType = that.data.identityType
    console.log('picker发送选择改变，携带值为', e.detail.value);//index为数组点击确定后选择的item索引
    this.setData({
      identityIndex: e.detail.value,
      cardType:identityType[e.detail.value].value,
      [`attributes[${index}].defaultValue`]:identityType[e.detail.value].value,
      [`attributeData.${name}`]:identityType[e.detail.value].value,
      identity:identityType[e.detail.value].name
    })
    console.log(this.data.attributes,this.data.attributeData)
  }
  }
})