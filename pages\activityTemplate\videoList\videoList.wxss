/* pages/subpages/videoList/videoList.wxss */
page{
  background: #F5F5F5FF;
}
.top{
  display: flex;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  background: #fff;
}
.top view{
  width: 25%;
}
.title{
  font-size: 28rpx;
  color: #999;
  padding: 0rpx 30rpx;
}
.content{
  margin: 0rpx 30rpx;
  background: #fff;
  padding: 0 20rpx;
  font-size: 28rpx;
  border-radius: 14rpx;
}
.list{
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 36rpx 0;
  align-self: center;
}
.list:last-child{
  border-bottom: none;
}
.zname{
  width: 25%;
  align-self: center;
  color: #333333FF;
}
.videoName{
  text-align: left;
  width: 30%;
  align-self: center;
  /* padding: 0 30rpx; */
  box-sizing: border-box;
  padding-right: 20rpx;
  color: #666666FF;
}
.showDate{
  width: 25%;
  text-align: left;
  color: #666666FF;
  align-self: center;
}
.view{
  width: 21%;
  text-align: right;
  color: #FF564AFF;
  align-self: center;
  font-size: 26rpx;
}