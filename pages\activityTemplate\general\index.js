const wxtimer = require("../../../utils/time.js");
const http = require("../../../utils/util.js");
const app = getApp();
const CryptoJS = require("../../../utils/CryptoJs.js");
const API = require("../../../api/request.js");
import HTAPI from "../../../api/haitong";
const { showTab, handleUserStorage } = require("../../../utils/aboutLogin");
const { setSalesCode } = require("../../../utils/querySalesCode");
const { identity, identityEnum } = require("../../../utils/phone-idcard");
import { formatActivityTime, splitText } from "../../../utils/util";

let totalTime = 5;
let clock = null;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activityReservationTypeList: ['ARTERY','CELL'],
    showGlobalPrompt: false,
    globalPromptMessage: "",
    selectIndex: 0,
    isHt: false,
    isShowOverlay: false,
    message: "",
    financialManagerData: "",
    financialManagerShareData: "",
    distributionChannel: "",
    isShow: true,
    pageUrl: "pages/activityTemplate/general/index",
    projectId: 0,
    activityTime: "",
    showParamModal: false,
    project: [
      {
        id: 1,
        url: "../../../image/mineActive.png",
        totalPlace: 10,
        title: "活动计划阿萨德瓦二收到任何",
        price: 9,
        place: 0,
        bannerImg: "",
      },
      {
        id: 2,
        url: "../../../image/mineActive.png",
        totalPlace: 22,
        title: "活动名称",
        price: 10,
        place: 8,
        bannerImg: "",
      },
      {
        id: 3,
        totalPlace: 20,
        url: "../../../image/mineActive.png",
        title: "活动名称",
        price: 20,
        place: 14,
        bannerImg: "",
      },
    ],
    showReject: false,
    audit: "",
    checkDetail: 0,
    isAttentionAgainFlag: false,
    disagree: false,
    loadAnimation: true,
    playing: true,
    img1: "../../../image/sperkers.png",
    img2: "../../../image/speaker.png",
    subtime: "00:00:00",
    timeObj: {},
    ids: "", // 活动id
    xsde: false, //用户绑定框
    code: false,
    phoneCode: "",
    codename: "获取验证码",
    returns: "",
    showModalDlg: false,
    idCard: "",
    userType: "",
    personLogin: false,
    productionTalk: false, // true隐藏立即购买按钮
    logining: false, // true登录按钮不可点击
    category: "", // 活动类别5高尔夫
    isFinish: false, //活动是否结束
    InfoShows: false,
    scene: "",
    batchFlag: "1",
    warnInfo: "", // 提醒我/已预约
    reflashToast: false,
    isReflash: false,
    userInfoSuccess: false,
    detailSucess: false,
    chooseArea: false,
    trueName: "",
    idCard: "",
    VSwiperImgList: [],
    currentIndex: 1,
    showPoster: false,
    btnText: {
      text: "专属海报",
      cssStr:
        "width:20rpx;height:96rpx;background:linear-gradient(90deg, #FF5030 0%, #FC6B37 100%);top:276rx;left:710rpx;color:#fff;font-size:20rpx;font-weight:400;",
    },
    showSwitchSale: false,
    currentId: "",
    acceptId: "",
    shareUserType: "",
    identityType: identityEnum,
    identity: "身份证",
    identityIndex: 0,
    cardType: "ID_CARD",
    golfActivityArea: [],
    onceToken: "",
    taskId: "",
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log("options--✨🍎", options);

    let scene = options.scene || "";
    let that = this;
    let userId = wx.getStorageSync("userId");
    let userType = wx.getStorageSync("userType");
    let groupNo = options.groupNo || "";
    let saleName =
      options.shareType == "kgApp"
        ? CryptoJS.Decrypt(options.saleName)
        : options.saleName || "";
    let salesCode = options.salesCode || "";
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",");
      let id = option[0];
      that.setData({
        heightTop: app.globalData.height,
        ids: id,
        userType: userType,
        userId,
        scene: scene,
      });
    } else {
      let id = options.id;
      that.setData({
        heightTop: app.globalData.height,
        ids: id,
        isShare: options.isShare || "",
        shareUserId: options.shareUserId || "",
        audit: options.audit || "",
        checkDetail: options.checkDetail * 1 || 0,
        userType: userType,
        userId,
        groupNo,
        changeSalesCode: salesCode,
        changeSaleName: saleName,
        shareUserType: options.userType || "",
        financialManagerShareData: {
          financialManagerNo: options.financialManagerNo || "",
          financialManager: options.financialManager || "",
        },
      });
    }

    if (options.onceToken && options.onceToken !== "" && options.onceToken !== "undefined") {
      this.setData({ onceToken: options.onceToken });
      wx.setStorageSync(
        "onceTokenData",
        JSON.stringify({ onceToken: options.onceToken })
      );
      // this.getByOnceToken(options.onceToken)
    } else {
      wx.setStorageSync("onceTokenData", "");
    }

    if (options.taskId) this.setData({ taskId: options.taskId });

    if (options.refer && options.refer == "kege") {
      this.setData({
        refer: options.refer,
        kegeName: options.name,
        kegePhone: options.phone,
        kegeCardType: options.cardType,
        kegeCardNo: options.cardNo,
      });
    }
  },
  async createKegeUser () {
    if (!this.data.kegePhone) return false;
    let data = {
      phone: this.data.kegePhone,
      name: this.data.kegeName,
      idcard: this.data.kegeCardNo,
      fromKjgx: 1,
      defaultFlag: 0,
      cardType: this.data.kegeCardType,
    };
    const res = await API.saveApplyer({
      encryptData: CryptoJS.Encrypt3(JSON.stringify(data)),
    });

    console.log(154, res);

    if (res.data.code == 200) {
      wx.setStorageSync("selectId", [res.data.data.id + ""]); //保存勾选的id
      let name = CryptoJS.Decrypt(res.data.data.name);
      let phone = CryptoJS.Decrypt(res.data.data.phone);
      let idcard = res.data.data.idcard
        ? CryptoJS.Decrypt(res.data.data.idcard)
        : "";
      res.data.data.name = name;
      res.data.data.phone = phone;
      res.data.data.idcard = idcard;
      res.data.data.cardType = res.data.data.cardType;
      res.data.data.otherInfo = {
        name: name,
        cardType: res.data.data.cardType,
        cardNo: idcard,
        phone: phone,
      };
      wx.setStorageSync(
        "selectPeople",
        JSON.parse(JSON.stringify([res.data.data]))
      ); //保存勾选的用户信息
    }
  },
  onShow: function () {
    this.setData({ onShowStartTime: new Date().getTime() });
    if (wx.getStorageSync("token")) {
      wx.showShareMenu({
        withShareTicket: false,
      });
    } else {
      wx.hideShareMenu({});
    }
    let that = this;
    let userId = wx.getStorageSync("userId");

    console.log("userIduserIduserId--✨🍎", userId);

    if (userId !== "") {
      this.changeAuth();

      // if (wx.getStorageSync('refreshUserInfo')) {
      //   that.getUserInfo();
      // } else {
      //   if (wx.getStorageSync('salesCode') || wx.getStorageSync('pcode')) {
      //     const cardType = wx.getStorageSync('cardType')
      //     const identityValue = that.data.identityType.filter((item) => item.value === cardType)
      //     console.log(identityValue)
      //     that.setData({
      //       type: wx.getStorageSync('userType'),
      //       salesCode: wx.getStorageSync('salesCode'),
      //       cardType: wx.getStorageSync('cardType'),
      //       identity: identityValue[0]?.name || '',
      //       idCard: wx.getStorageSync('idCard') ? wx.getStorageSync('idCard') : null,
      //       userType: wx.getStorageSync('userType'),
      //       credits: wx.getStorageSync('credits'),
      //       userInfoSuccess: true
      //     })
      //   }
      // }
    } else {
      that.setData({
        showModalDlg: true,
        InfoShow: true,
      });
    }
  },
  // 查询是否注册
  getIsRegister () {
    let that = this;
    if (http.unRegister()) {
      // 已经授权没有注册
      // 查询绑定的业务员
      if (that.data.isShare) {
        that.data.changeSalesCode &&
          setSalesCode(that.data.changeSalesCode, that.data.changeSaleName);
      }
      that.setData({
        personLogin: true,
        showModalDlg: false,
        InfoShow: false,
      });
    } else {
      showTab();
      that.setData({
        showModalDlg: false,
        InfoShow: false,
      });
      if (that.data.isShare == 1 || that.data.isShare == 2) {
        that.openChangeScole();
      }
    }
    // that.getactivity(that.data.ids)
  },
  async onHide () {
    const {
      title,
      applyStartTime,
      applyEndTime,
      startTime,
      endTime,
      id,
      distributionChannel,
    } = this.data.activity;
    const userData = wx.getStorageSync("getInfoByIdData");
    const onShowEndTime = new Date().getTime();
    const obj = await http.sensorsData(userData, "太享趣");
    const data = {
      ...obj,
      name: "活动详情浏览",
      $title: "活动详情页",
      task_id: this.data.taskId,
      activityId: id,
      activityName: title,
      activity_type: "报名活动",
      apply_start_time: applyStartTime,
      apply_end_time: applyEndTime,
      start_time: startTime,
      end_time: endTime,
      customer_name: userData.name ? CryptoJS.Decrypt(userData.name) : "",
      agent_id: userData.salesCode ? CryptoJS.Decrypt(userData.salesCode) : "",
      agent_name: userData.salesName
        ? CryptoJS.Decrypt(userData.salesName)
        : "",
      stayTime: onShowEndTime - this.data.onShowStartTime,
      channel:
        distributionChannel === "TX"
          ? "团险"
          : ["SPDB", "YB_SCE"].includes(distributionChannel)
            ? "银保"
            : "个险",
    };
    console.log("data--✨🍎", data);
    app.sensors.track("eventPageView", data);
  },

  closeOverTip () {
    this.setData({ isShowOverlay: false }, () => {
      wx.switchTab({ url: "/pages/home/<USER>" });
    });
  },

  // 刷新获取活动数据
  reGetActivity () {
    let that = this;
    this.handleReflashToast();
    if (!that.data.userInfoSuccess) {
      this.getUserInfo();
    }
    if (!wx.getStorageSync("key")) {
      CryptoJS.getKey();
    }
    if (
      that.data.userInfoSuccess &&
      !that.data.detailSucess &&
      !that.data.activity
    ) {
      console.log("加载活动");
      this.getactivity(this.data.ids);
    }
  },
  handleReflashToast () {
    let that = this;
    that.setData({
      reflashToast: true,
      reflashBtnContent: `(${totalTime}s)`,
      isReflash: false,
    });
    clearInterval(clock);
    clock = setInterval(() => {
      totalTime--;
      that.setData({
        reflashBtnContent: `(${totalTime}s)`,
      });
      if (totalTime < 1) {
        clearInterval(clock);
        totalTime = 5;
        that.setData({
          reflashBtnContent: "",
          isReflash: true,
        });
      }
    }, 1000);
  },
  // 我想参加
  wantToJoin () {
    if (this.data.audit || this.data.checkDetail) return;
    // console.log('我想参加')
    let that = this;
    let data = {
      activityId: Number(that.data.ids),
    };
    API.wantAttention(data).then((res) => {
      // console.log('async我想参加', res)
      if (res.data.code == 200) {
        that.setData({
          isAttentionAgainFlag: true,
        });
      } else {
        wx.showToast({
          title: res.data.message,
          icon: "none",
        });
      }
    });
  },

  //活动详情
  async getactivity (id) {
    const that = this;
    that.setData({
      myLoadAnimation: true,
    });
    let data = {
      id,
    };
    if (this.data.checkDetail == 1) {
      data.containsDeleted = 1;
    }
    console.log(this.data.checkDetail, data);

    let res =
      that.data.audit == 1
        ? await API.auditDetail(data)
        : await API.getActivityDetail(data);
    this.setData({
      isShow: false,
    });
    console.log("详情数据：", res);

    return new Promise((resolve, reject) => {
      if (
        res.header["Content-Type"] === "text/html" &&
        !that.data.reflashToast
      ) {
        that.setData({
          myLoadAnimation: false,
          detailSucess: false,
        });
        this.handleReflashToast();
        resolve(false);
      } else {
        if (res.data.code == 200) {
          this.getHyacinthPerm();
          resolve(true);
          const distributionChannel = res.data.data.distributionChannel;
          this.setData({ distributionChannel });

          that.setData({
            detailSucess: true,
            myLoadAnimation: false,
            isAttentionAgainFlag:
              res.data.data?.isAttention == 1 ? true : false,
          });
          if (
            that.data.detailSucess &&
            that.data.userInfoSuccess &&
            wx.getStorageSync("key")
          ) {
            that.setData({
              reflashToast: false,
            });
          }
          wx.setStorageSync(
            "attributes",
            JSON.parse(JSON.stringify(res.data.data.attributes))
          );
          console.log(
            369,
            JSON.parse(JSON.stringify(res.data.data.attributes))
          );
          // 埋点
          const nickName = wx.getStorageSync("nickName");
          const userId = wx.getStorageSync("userId");
          const activityName = res.data.data?.title || "";
          const activityId = res.data.data?.id || "";
          const time = new Date().toLocaleString();
          app.sensors.track("activityDetail", {
            name: "活动详情页",
            personScan: nickName,
            activityName,
            activityId,
            scanTime: time,
            userId,
          });
          const activityTime = formatActivityTime(
            res.data.data.startTime,
            res.data.data.endTime
          );

          const applyStartTime = res.data.data.applyStartTime; // 活动买票开始时间
          const end_time = res.data.data.endTime || "";
          const applyEndTime = res.data.data.applyEndTime;
          if (res.data.data.detailImgs) {
            res.data.data.detailImgs = res.data.data.detailImgs.split(",");
          }
          // 免费活动的时候显示freeLimit字段，默认是限时免费
          if (!res.data.data.freeLimit) {
            res.data.data.freeLimit = "限时免费";
          }
          // totalplace: 活动总票数
          // place: 活动票数剩余库存
          // shiwutype: 活动类型 0:虚拟 1:实物活动(有收货地址)
          if (res.data.data.category == 3) {
            that.setData({
              minNum:
                that.data.userType == 3
                  ? res.data.data.salesMinNum
                  : res.data.data.minNum,
              maxNum:
                that.data.userType == 3
                  ? res.data.data.salesMaxNum
                  : res.data.data.maxNum,
            });
          }
          if (res.data.data.distributionChannel === "PL") {
            wx.hideShareMenu({});
          }
          var reg = new RegExp("TXQImgPath/", "ig");
          let dynamicDomainName = wx.getStorageSync("dynamicDomainName");
          let activityData = res.data.data;
          activityData.activityPageConfig &&
            activityData.activityPageConfig.replace(reg, dynamicDomainName);
          that.setData({
            enterTime: new Date().getTime(),
            lockDate: res.data.data.lockDate,
            activity: activityData,
            place: res.data.data.limitBuy,
            applyEndTime: res.data.data.applyEndTime,
            applyStartTime: res.data.data.applyStartTime,
            activityTime,
            create_time: res.data.data.startTime,
            end_time: end_time,
            prdsen: parseInt(
              (1 - res.data.data.place / res.data.data.totalplace) * 100
            ),
            shiwutype: res.data.data.type,
            isProductionTalk: res.data.data.isProductionTalk, // 是否是产说会
            category: res.data.data.category,
            videoUrl: res.data.data.video,
            batchFlag: res.data.data.batchFlag,
            loadAnimation: false,
            warnInfo: res.data.data.subscribed ? "已预约" : "提醒我",
            chatLst: res.data.data.chatLst, //活动可以参与的业务员渠道
            myLoadAnimation: false,
            isReflash: false,
            project: res.data.data.activityCustomList,
          });

          that.createKegeUser();

          // 判断高尔夫
          if (res.data.data.category == 5) {
            if (res.data.data.batchFlag == "1") {
              //批次预约
              res.data.data.golfList.map((element) => {
                element.selFlag = false;
                element.appointTimes = JSON.parse(element.appointTimes);
              });
            } else if (res.data.data.batchFlag == "2") {
              //机构预约
              res.data.data.golfList.map((element) => {
                element.selFlag = false;
              });
            } else {
              that.setData({
                golfActivityArea: res.data.data.golfList,
              });
            }
            that.setData({
              activeMode: res.data.data.activityMode,
              golfActivityArea: res.data.data.golfList,
            });
          }
          // 判断产说会 isProductionTalk : 1 是产说会,0不是
          if (
            res.data.data.isProductionTalk == 1 &&
            res.data.data.category == 1 &&
            (that.data.userType == 1 || that.data.userType == 2)
          ) {
            // 普通产说会
            that.setData({
              productionTalk: true, //隐藏立即购买按钮
            });
          } else if (
            res.data.data.isProductionTalk == 1 &&
            (res.data.data.category == 4 || res.data.data.category == 3) &&
            (that.data.userType == 1 || that.data.userType == 2)
          ) {
            // 1+1产说会
            that.setData({
              productionTalk: true,
            });
          } else {
            that.setData({
              productionTalk: false,
            });
          }
          // 处理页面元素

          this.handlePageStyle(res.data.data);
          if (res.data.data.category == 3) {
            //   //商品模式选择项目
            let item = res.data.data.activityCustomList[0]; //activityCustomList
            //   console.log(item);
            //   if (!item) {
            //     item = that.data.project[0]
            //     console.log('都没有库存');
            //   }
            //   let bannerImg = []

            //   if (item.bannerImg != null && item.bannerImg != '') {
            //     bannerImg = item.bannerImg.split(",")
            //     that.setData({
            //       bannerType: true
            //     })
            //   } else {
            //     bannerImg = that.data.VSwiper
            //     that.setData({
            //       bannerType: false
            //     })
            //   }
            //   console.log(item);

            that.setData({
              projectId: item.id,
              itemObject: item,
              // bannerImg: bannerImg,
              ["activity.totalPlace"]: item.totalPlace,
              ["activity.place"]: item.place,
              ["activity.originalPrice"]: item.originalPrice,
              ["activity.price"]: item.price, //客户现价
              ["activity.minNum"]: item.minNum,
              ["activity.maxNum"]: item.maxNum, //报名次数区间
              ["activity.salesPrice"]: item.salesPrice, //业务员现价
              ["activity.salesMinNum"]: item.salesMinNum,
              ["activity.salesMaxNum"]: item.salesMaxNum,
            });
            wx.setStorageSync("smallTitle", item.title);
          }

          let nowtime = new Date().getTime(); //目前时间
          let createTime = new Date(
            applyStartTime.replace(/-/g, "/")
          ).getTime(); // 活动开始时间时间戳
          let endTime = new Date(applyEndTime.replace(/-/g, "/")).getTime(); // 活动结束时间时间戳毫秒
          // console.log(nowtime, createTime, endTime, 999)
          let sub_time1 = parseInt((nowtime - createTime) / 1000); //正数： 活动开始  负数：活动开始倒计时
          let sub_time2 = parseInt((endTime - nowtime) / 1000); // 正数 结束倒计时   负数： 活动已结束
          that.setData({
            sub_time1,
            sub_time2,
          });
          var subtime = "";
          if (sub_time1 < 0) {
            subtime = parseInt((createTime - nowtime) / 1000); //活动开始倒计时
            that.setData({
              countDownConfig: {
                ...that.data.countDownConfig,
                textStart: that.data.countDownConfig?.endLeftText || "",
                textEnd: that.data.countDownConfig?.endRightText || "",
              },
            });
          }
          if (sub_time2 < 0 && !that.data.audit && !that.data.checkDetail) {
            that.setData({
              isFinish: true,
            });
            wx.showToast({
              title: "活动已结束,请下次参与！",
              icon: "none",
              duration: 2500,
            });
          }
          if (sub_time1 > 0 && sub_time2 > 0) {
            subtime = parseInt((endTime - nowtime) / 1000); //进行中  结束倒计时
            that.setData({
              countDownConfig: {
                ...that.data.countDownConfig,
                textStart: that.data.countDownConfig?.startLeftText || "",
                textEnd: that.data.countDownConfig?.startRightText || "",
              },
            });
          }
          let timer = new wxtimer({
            complete: function () {
              if (sub_time1 < 0) {
                // 活动开始倒计时
                that.getactivity(that.data.ids);
                clearInterval(timer);
              } else if (
                sub_time1 > 0 &&
                sub_time2 > 0 &&
                !that.data.audit &&
                !that.data.checkDetail
              ) {
                that.setData({
                  isFinish: true,
                });
                wx.showModal({
                  title: "提示",
                  content: "活动已结束",
                  showCancel: false,
                  success: function (res) {
                    if (res.confirm) {
                      wx.navigateBack({
                        delta: 1,
                      });
                    }
                  },
                });
              }
            },
          });
          //  计时器
          timer.start(that, subtime, false);
        } else {
          resolve(false);
          that.setData({
            myLoadAnimation: false,
          });
          if (
            res.data.code === 500 &&
            res.data.message === "活动火爆，小主请稍后再试" &&
            !that.data.reflashToast
          ) {
            that.setData({
              detailSucess: false,
            });
            that.handleReflashToast();
          } else {
            wx.showToast({
              title: "网络繁忙，请稍后重试",
              icon: "none",
              duration: 2000,
            });
          }
        }
      }
    });

    // }).catch(err => {
    //   console.log('catch', err)
    //   that.setData({
    //     myLoadAnimation: false
    //   })
    //   if (!that.data.reflashToast) {
    //     that.handleReflashToast()
    //   }
    // })
  },

  async getQueryFinancial () {
    const salesCode = wx.getStorageSync("salesCode");
    const getInfoByIdData = wx.getStorageSync("getInfoByIdData");
    const { financialManagerNo } = getInfoByIdData;
    if (financialManagerNo === "") return;

    const res = await HTAPI.getQueryFinancial({
      salesCode,
      managerCode: financialManagerNo,
    });
    console.log("--getQueryFinancial✨🍎", res);
    const { code, message, data } = res.data;
    if (code !== 200) return;
    this.setData({ financialManagerData: data });
  },

  async getHyacinthPerm () {
    let userType = wx.getStorageSync("userType");
    // 获取理财经理信息
    if (userType === 1) this.getQueryFinancial();

    try {
      const res = await HTAPI.getHyacinthPerm();
      const { data, code, message } = res.data;
      if (code === 200) {
        this.setData({ isHt: data.htUser === 1 });
      }
    } catch (error) { }
  },

  // 处理页面元素
  handlePageStyle (res) {
    // console.log(res.activityPageConfig)
    var reg = new RegExp("TXQImgPath/", "ig");
    let dynamicDomainName = wx.getStorageSync("dynamicDomainName");
    let activityPageData =
      res.activityPageConfig &&
      res.activityPageConfig.replace(reg, dynamicDomainName);
    const that = this;
    const activityPageConfig = activityPageData
      ? JSON.parse(activityPageData).find((item) => item.title === "商品详情页")
      : null;
    if (activityPageConfig !== undefined && activityPageConfig !== null) {
      console.log("页面组件元素", activityPageConfig.componentData);
      let backgroundImg = {};
      if (this.data.category == 5) {
        backgroundImg = activityPageConfig.componentData.find(
          (item) => item.type === "backgroundImg"
        );
      } else {
        backgroundImg = activityPageConfig.componentData.find(
          (item) => item.type === "pageBackgroundImg"
        );
      }
      const countDownConfig = activityPageConfig.componentData.find(
        (item) => item.type === "countDown"
      );
      const registrationPrice = activityPageConfig.componentData.find(
        (item) => item.type === "registrationPrice"
      );
      const limitTextConfig = activityPageConfig.componentData.find(
        (item) => item.type === "limitText"
      );
      const VSwiper = activityPageConfig.componentData.find(
        (item) => item.type === "VSwiper"
      );
      const commodityMain = activityPageConfig.componentData.find(
        (item) => item.type === "commodityMain"
      );
      that.setData({
        backgroundImg: backgroundImg?.propValue.url || "",
        componentData: activityPageConfig.componentData,
        countDownConfig,
        registrationPrice,
        limitTextConfig,
        VSwiper,
        commodityMain,
      });
    }
    const activityPosterConfig = activityPageData
      ? JSON.parse(activityPageData).find((item) => item.title === "专属海报")
      : null;
    if (activityPosterConfig !== undefined && activityPosterConfig !== null) {
      const posterBackgroundImg = activityPosterConfig.componentData.find(
        (item) => item.type === "posterPic"
      );
      const posterText = activityPosterConfig.componentData.find(
        (item) => item.type === "text"
      );
      that.setData({
        posterBackgroundImg,
        activityPosterConfig,
        posterText,
      });
    }
    const activityPeriodConfig = activityPageData
      ? JSON.parse(activityPageData).find(
        (item) => item.title === "预约场次配置"
      )
      : null;
    if (activityPeriodConfig !== undefined && activityPeriodConfig !== null) {
      wx.setStorageSync(
        "activityPeriodConfig",
        JSON.stringify(activityPeriodConfig)
      );
    }

    const commdityConfig = activityPageData
      ? JSON.parse(activityPageData).find((item) => item.title === "商品选择页")
      : null;
    console.log(commdityConfig);
    if (commdityConfig !== undefined && commdityConfig !== null) {
      const commdityDialogBg = commdityConfig.componentData.find(
        (item) => item.type === "commdityDialog"
      );
      const projectList = commdityConfig.componentData.find(
        (item) => item.type === "projectList"
      );
      that.setData({
        commdityConfig,
        commdityDialogBg,
        projectList,
      });
    }
  },

  async changeAuth () {
    const res = await this.getactivity(this.data.ids);
    const ret = await this.getUserInfo();

    const userType = wx.getStorageSync("userType");
    const {
      isHt,
      distributionChannel,
      financialManagerShareData,
      changeSalesCode,
    } = this.data;

    const { financialManagerNo, financialManager } = financialManagerShareData;

    // 针对海通活动
    if (distributionChannel === "HTZQ") {
      const params = `?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}`;
      if (userType === 0 && !isHt && financialManagerNo)
        return wx.navigateTo({
          url: `/pages/fuseSpecial/haitong/login/index${params}`,
        });
      if (userType === 1 && !isHt) {
        const url = !financialManagerNo
          ? `/pages/fuseSpecial/haitong/login/index?salesCode=${changeSalesCode}`
          : `/pages/fuseSpecial/haitong/login/index?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}`;
        wx.navigateTo({ url: url });
        return;
      }
    }

    // 判断是否注册过
    this.getIsRegister();
  },

  //用户登录验证
  userInfoHandler () {
    app.getUserInfo(async (userInfo) => {
      if (userInfo.id) {
        this.setData({
          showModalDlg: false,
          InfoShow: false,
          isAuthorize: true,
          userId: userInfo.id,
        });
        this.changeAuth();
      }
    });
  },

  // 获取用户信息
  getUserInfo () {
    const that = this;
    // 获取用户所属门店信息
    return new Promise((resolve, reject) => {
      this.setData({
        myLoadAnimation: true,
      });
      API.getInfoById().then((res) => {
        if (
          res.header["Content-Type"] === "text/html" &&
          !that.data.reflashToast
        ) {
          that.setData({
            myLoadAnimation: false,
          });
          this.handleReflashToast();

          resolve(false);
        } else {
          if (res.data.code == 200) {
            console.log("获取用户信息", res);
            resolve(true);
            const { cardType } = res.data.data;
            const identityValue = that.data.identityType.filter(
              (item) => item.value === cardType
            );
            console.log(identityValue);
            that.setData({
              type: res.data.data.type,
              salesCode: res.data.data.salesCode,
              idCard: CryptoJS.Decrypt(res.data.data.idcard),
              userType: res.data.data.type,
              credits: res.data.data.credits,
              userInfoSuccess: true,
              myLoadAnimation: false,
              cardType: cardType,
              identity: identityValue[0]?.name || "",
            });

            if (
              that.data.detailSucess &&
              that.data.userInfoSuccess &&
              wx.getStorageSync("key")
            ) {
              that.setData({
                reflashToast: false,
              });
            }
            handleUserStorage(res.data.data);
          } else {
            resolve(false);
            that.setData({
              myLoadAnimation: false,
            });
            if (
              res.data.code === 500 &&
              res.data.message === "活动火爆，小主请稍后再试" &&
              !that.data.reflashToast
            ) {
              this.handleReflashToast();
            } else {
              wx.showToast({
                title: res.data.message,
                icon: "none",
                duration: 2000,
              });
            }
          }
        }
      });
      if (wx.getStorageSync("token")) {
        that.setData({
          phonebtn: true,
        });
      } else {
        that.setData({
          phonebtn: false,
          tobind: false,
        });
      }
    });
  },

  async changeClickEvent () {
    const {
      applyStartTime,
      applyEndTime,
      startTime,
      endTime,
      id,
      title,
      distributionChannel,
    } = this.data.activity;
    const userData = wx.getStorageSync("getInfoByIdData");
    const onShowEndTime = new Date().getTime();
    const obj = await http.sensorsData(userData, "太享趣");
    const _data = {
      ...obj,
      name: "活动报名",
      $title: "活动详情页",
      task_id: this.data.taskId,
      activityId: id,
      activityName: title,
      activity_type: "报名活动",
      apply_start_time: applyStartTime,
      apply_end_time: applyEndTime,
      start_time: startTime,
      button_name: "立即报名",
      customer_name: userData.name ? CryptoJS.Decrypt(userData.name) : "",
      agent_id: userData.salesCode ? CryptoJS.Decrypt(userData.salesCode) : "",
      agent_name: userData.salesName
        ? CryptoJS.Decrypt(userData.salesName)
        : "",
      end_time: endTime,
      stayTime: onShowEndTime - this.data.onShowStartTime,
      channel:
        distributionChannel === "TX"
          ? "团险"
          : ["SPDB", "YB_SCE"].includes(distributionChannel)
            ? "银保"
            : "个险",
    };
    app.sensors.track("clickEvent", _data);
  },

  async toThirdApp () {
    try {
      const envVersion = __wxConfig.envVersion;
      const res = await API.getGwUrl({ type: 1, activityId: this.data.ids });
      const { code, message, data } = res.data;
      if (code !== 200)
        return this.setData({
          showGlobalPrompt: true,
          globalPromptMessage: splitText(message),
        });
      const path = `${data.jumpUrl}?token=${data.gwToken}`;
      wx.navigateToMiniProgram({
        appId: app.HOME_APPID,
        envVersion,
        path,
        success (res) { },
      });
    } catch (error) {
      console.log("--✨🍎", error);
    }
  },
  // 立即购买
  async tobuy () {
    const userType = wx.getStorageSync("userType");
    const {
      isHt,
      distributionChannel,
      financialManagerShareData,
      changeSalesCode,
      activity,
    } = this.data;
    const { financialManagerNo, financialManager } = financialManagerShareData;
    const getInfoByIdData = wx.getStorageSync("getInfoByIdData");
    const { fgsCode: fgsKey } = getInfoByIdData;

    // 跳转第三方小程序 0815细胞家园改为普通报名
    // if (this.data.activityReservationTypeList.includes(activity.activityReservationType) && activity.flagMeeting === 2) return this.toThirdApp()
    // const response = await API.getfindByCode({
    //   code: this.data.activity.activityReservationType === "HOME"?"home_pact_open_fgs_key":'centenarian_pact_open_fgs_key',
    // });
    // const { data: fgsList, code: fgsCode, message } = response.data;
    // if (fgsCode !== 200) return;
    // const hasPermission = fgsList && fgsList.includes(fgsKey);
    // if ((this.data.activity.activityReservationType === "HOME"|| (this.data.activity.activityReservationType === "CENTENARIAN"&&activity.flagMeeting === 2))&& hasPermission)
    //   return this.toThirdApp();

    // 针对海通活动
    if (distributionChannel === "HTZQ") {
      if (userType === 2)
        return this.setData({
          isShowOverlay: true,
          message: "此活动仅限海通专区用户参与~",
        });
      if (userType === 3 && !isHt)
        return this.setData({
          isShowOverlay: true,
          message: "仅海通专区营销员可参与活动~",
        });

      if (userType === 1 && !isHt && !financialManagerNo)
        return this.setData({
          isShowOverlay: true,
          message: "此活动仅限海通专区用户参与~",
        });

      const params = `?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}`;
      if (userType === 1 && !isHt && financialManagerNo)
        return wx.navigateTo({
          url: `/pages/fuseSpecial/haitong/login/index${params}`,
        });
    }

    const that = this;
    const { ids, category, shiwutype } = that.data;
    console.log(activity);
    const isRequire = activity.attributes.filter(
      (item) => item.propertyCode === "cardNo"
    )[0]?.required;
    const selBatchId =
      that.data.batchFlag == "1" || that.data.batchFlag == 2
        ? that.data.selBatchId
        : "";
    if (wx.getStorageSync("token") && that.data.userType == 0) {
      // && !that.data.isShare不管是否分享都要判断是否注册
      if (wx.getStorageSync("userId") !== "") {
        // 判断是否注册过
        that.getIsRegister(wx.getStorageSync("userId"));
      }
      return false;
    } else if ([1, 3, 4].includes(category) && that.data.activity.place === 0) {
      wx.showToast({
        title: "活动太火爆，已被抢光啦",
        icon: "none",
        duration: 1500,
        mask: false,
      });
      return;
    } else if (
      that.data.activity.secKill &&
      that.data.chatLst.findIndex((item) =>
        ["root", wx.getStorageSync("chatType")].includes(item)
      ) === -1
    ) {
      wx.showToast({
        title: "您的客户经理渠道不在该活动的允许范围内",
        icon: "none",
        duration: 2500,
        mask: false,
      });
      return false;
      // }
    } else if (
      that.data.userType != 3 &&
      ((that.data.category == 5 && that.data.activeMode === "PLUS") ||
        that.data.category == 4) &&
      that.data.activity.salesPay == 1
    ) {
      wx.showToast({
        title: "请联系您的客户经理进行活动报名",
        icon: "none",
        duration: 1500,
      });
      return false;
    } else if (that.data.sub_time1 < 0) {
      wx.showToast({
        title: "活动尚未开始，请耐心等待！",
        icon: "none",
        duration: 2500,
      });
      return false;
    } else if (that.data.batchFlag == "1" && !that.data.selBatch) {
      wx.showToast({
        title: "请选择批次！",
        icon: "none",
        duration: 2500,
      });
      return false;
    } else if (that.data.batchFlag == "2" && !that.data.selBatch) {
      wx.showToast({
        title: "请选择机构！",
        icon: "none",
        duration: 2500,
      });
      return false;
    } else if (
      that.data.selBatch &&
      that.data.batchFlag == "1" &&
      new Date(that.data.selBatch["appointTimes"]["endTime"]).getTime() <
      new Date().getTime()
    ) {
      wx.showToast({
        title: "该批次的报名已截止,请选择其他批次预约！",
        icon: "none",
        duration: 2500,
      });
      return false;
    } else if (that.data.sub_time2 < 0) {
      that.setData({
        isFinish: true,
      });
      wx.showToast({
        title: "活动已结束,请下次参与！",
        icon: "none",
        duration: 2500,
      });
    } else if (
      !that.data.activity.secKill &&
      wx.getStorageSync("token") &&
      (wx.getStorageSync("idCard") == null ||
        wx.getStorageSync("idCard") == "") &&
      isRequire === 1
    ) {
      that.setData({
        chooseArea: !that.data.chooseArea,
        trueName: wx.getStorageSync("trueName"),
      });
    } else if (that.data.userType == 3) {
      that.checkYwyLabel();
      return false;
    } else {
      //校验是否同意隐私项
      if (http.userAre() && wx.getStorageSync("agree") == "0") {
        //已登录没有同意隐私项
        that.setData({
          disagree: true,
          jumpType: "tobuy",
        });
        return;
      } else {
        that.changeClickEvent();
        const activity = that.data.activity;
        let url = `/pages/activityready/activityready?cate=${that.data.category
          }&id=${ids}&shiwutype=${shiwutype}&batchId=${selBatchId}&activeMode=${that.data.activeMode || ""
          }&groupNo=${that.data.groupNo}&minNum=${activity.minNum || 0}&maxNum=${activity.maxNum
          }&salesMinNum=${activity.salesMinNum || 0}&salesMaxNum=${activity.salesMaxNum || 0
          }&customerPrice=${activity.price || 0}&salesPrice=${activity.salesPrice || 0
          }&salesPay=${activity.salesPay || 0}&lockDate=${activity.lockDate
          }&secKill=${activity.secKill}&isQtn=${that.data.activity.isQtn
          }&creditsSource=${activity.creditsSource}&usableCredits=${activity.usableCredits
          }&projectId=${that.data.projectId}&refer=${that.data.refer}`;
        url += `&taskId=${that.data.taskId}`;
        if (activity.secKill) {
          url += "&title=" + activity.title + "&coverImg=" + activity.coverImg;
        }
        if (this.data.onceToken) {
          url += "&isReplace=2";
        }
        wx.navigateTo({
          url,
        });
      }
    }
  },
  agreePrivacy () {
    //同意协议
    let that = this;
    that.toActivityReady();
  },
  // 跳转报名页的判断
  toActivityReady () {
    let that = this;
    if (
      that.data.userType != 3 &&
      that.data.activeMode === "PLUS" &&
      that.data.salesPay == 1
    ) {
      wx.showToast({
        title: "请联系您的客户经理进行活动报名",
        icon: "none",
        duration: 1500,
      });
      return false;
    }
    if (that.data.selTimes) {
      wx.setStorageSync("currentIndex", that.data.currentIndex);
      wx.setStorageSync("selTimes", that.data.selTimes);
      wx.setStorageSync("currentperiodList", that.data.currentperiodList);
      console.log("秒杀", that.data.secKill);
      let url = `/pages/activityready/activityready?selTimes=${that.data.selTimes
        }&golfId=${that.data.areaInfo.id}&id=${that.data.activityId}&maxNum=${that.data.maxNum
        }&minNum=${that.data.minNum}&salesMaxNum=${that.data.salesMaxNum
        }&salesMinNum=${that.data.salesMinNum}&activeMode=${that.data.activeMode
        }&customerPrice=${that.data.activity.price}&salesPrice=${that.data.activity.salesPrice || 0
        }&salesPay=${that.data.salesPay}&cate=5&lockDate=${that.data.lockDate
        }&secKill=${that.data.secKill}&isQtn=${that.data.isQtn}&creditsSource=${that.data.activity.creditsSource
        }&usableCredits=${that.data.activity.usableCredits}&projectId=${that.data.projectId
        }&refer=${that.data.refer}`;
      url += `&taskId=${that.data.taskId}`;

      if (that.data.secKill) {
        url += `&title=${that.activity.title}&coverImg=${that.activity.coverImg}`;
      }

      if (this.data.onceToken) {
        url += "&isReplace=2";
      }
      wx.navigateTo({
        url,
        success: function (res) { },
      });
    } else {
      wx.showToast({
        title: "请选择预约时间",
        icon: "none",
        duration: 1500,
      });
    }
  },
  // 业务员标签校验
  checkYwyLabel () {
    const that = this;
    const selBatchId =
      that.data.batchFlag == "1" || that.data.batchFlag == 2
        ? that.data.selBatchId
        : "";
    const data = {
      activityId: that.data.ids,
    };
    API.checkYwyLabel(data).then((res) => {
      if (res.data.code == 200) {
        if (!res.data.data) {
          wx.showToast({
            title: "当前为限定业务员标签活动，您无法参与！",
            icon: "none",
            duration: 2000,
          });
        } else {
          //校验是否同意隐私项
          if (http.userAre() && wx.getStorageSync("agree") == "0") {
            //已登录没有同意隐私项
            that.setData({
              disagree: true,
              jumpType: "tobuy",
            });
            return;
          } else {
            that.changeClickEvent();
            const activity = that.data.activity;
            console.log("标签校验");
            let url =
              "/pages/activityready/activityready?cate=" +
              that.data.category +
              "&id=" +
              that.data.ids +
              "&shiwutype=" +
              that.data.shiwutype +
              "&batchId=" +
              selBatchId +
              "&activeMode=" +
              that.data.activeMode +
              "&minNum=" +
              activity.minNum +
              "&maxNum=" +
              activity.maxNum +
              "&salesMinNum=" +
              activity.salesMinNum +
              "&salesMaxNum=" +
              activity.salesMaxNum +
              "&customerPrice=" +
              activity.price +
              "&salesPrice=" +
              activity.salesPrice +
              "&salesPay=" +
              activity.salesPay +
              "&lockDate=" +
              activity.lockDate +
              "&secKill=" +
              activity.secKill +
              "&isQtn=" +
              that.data.activity.isQtn +
              "&creditsSource=" +
              that.data.activity.creditsSource +
              "&usableCredits=" +
              that.data.activity.usableCredits +
              "&projectId=" +
              that.data.projectId +
              "&refer=" +
              that.data.refer;
            url += `&taskId=${that.data.taskId}`;

            if (activity.secKill) {
              url +=
                "&title=" +
                activity.title +
                "&coverImg=" +
                activity.coverImg +
                "&refer=" +
                that.data.refer;
            }

            if (this.data.onceToken) {
              url += "&isReplace=2";
            }
            wx.navigateTo({
              url,
            });
          }
        }
      } else {
        wx.showToast({
          title: res.data.message,
          icon: "none",
          duration: 1500,
        });
      }
    });
  },
  // 分享活动
  async onShareAppMessage () {
    if (this.data.audit || this.data.checkDetail) return;
    const that = this;
    const userId = wx.getStorageSync("userId");
    const salesCode = wx.getStorageSync("salesCode");
    const saleName = wx.getStorageSync("saleName");
    const userType = wx.getStorageSync("userType");

    const {
      applyStartTime,
      applyEndTime,
      startTime,
      endTime,
      id,
      distributionChannel: channel,
    } = this.data.activity;
    const userData = wx.getStorageSync("getInfoByIdData");
    const onShowEndTime = new Date().getTime();
    const obj = await http.sensorsData(userData, "太享趣");
    const data = {
      ...obj,
      name: "活动分享",
      $title: "活动详情页",
      task_id: this.data.taskId,
      activityId: id,
      activityName: this.data.activity.title,
      activity_type: "报名活动",
      apply_start_time: applyStartTime,
      apply_end_time: applyEndTime,
      start_time: startTime,
      end_time: endTime,
      share_form: "微信好友",
      button_name: "分享客户",
      customer_name: userData.name ? CryptoJS.Decrypt(userData.name) : "",
      agent_id: userData.salesCode ? CryptoJS.Decrypt(userData.salesCode) : "",
      agent_name: userData.salesName
        ? CryptoJS.Decrypt(userData.salesName)
        : "",
      stayTime: onShowEndTime - this.data.onShowStartTime,
      channel:
        channel === "TX"
          ? "团险"
          : ["SPDB", "YB_SCE"].includes(channel)
            ? "银保"
            : "个险",
    };
    app.sensors.track("eventShare", data);

    const { distributionChannel, financialManagerData } = this.data;
    // pages/activitydetail/activitydetail?id=285&isShare=1&shareUserId=36
    const title = that.data.activity.shareText
      ? that.data.activity.shareText
        .replace("#微信昵称#", wx.getStorageSync("nickName"))
        .replace("#活动名称#", this.data.activity.title)
      : "";

    // 客户是理财经理分享带出
    let HTPATH = "";
    if (userType === 1 && distributionChannel === "HTZQ") {
      const { financialManager, financialManagerNo } = financialManagerData;
      HTPATH = `&financialManager=${financialManager}&financialManagerNo=${financialManagerNo}`;
    }

    const path = `/pages/activityTemplate/general/index?id=${that.data.ids}&isShare=1&shareUserId=${userId}&clientShareUserId=${userId}&salesCode=${salesCode}&saleName=${saleName}&userType=${userType}${HTPATH}`;

    console.log("--✨🍎", path);

    const onceToken = that.data.onceToken ? that.data.onceToken : "";
    return {
      title: title,
      path: `/pages/activityTemplate/general/index?id=${that.data.ids}&isShare=1&shareUserId=${userId}&clientShareUserId=${userId}&salesCode=${salesCode}&saleName=${saleName}&userType=${userType}${HTPATH}&onceToken=${onceToken}`,
    };
  },
  preventTouchMove: function () {
    //阻止触摸
  },
  // 选择批次或者机构预约
  selectBatch (e) {
    console.log(e);
    if (this.data.audit || this.data.checkDetail) return;
    let batch = e.currentTarget.dataset.item;
    let id = e.currentTarget.dataset.id;
    let index = e.currentTarget.dataset.selindex;
    console.log(batch, index);
    if (this.data.batchFlag == "2" && !batch.buyable) {
      return;
    }
    if (!batch.selflag) {
      // 当前列表其他恢复
      this.data.golfActivityArea.forEach((ele, elindex) => {
        console.log("选中的批次", elindex);
        let curSelFlag = `golfActivityArea[${elindex}].selFlag`;
        this.setData({
          [curSelFlag]: batch.selFlag,
        });
      });
      console.log(this.data.golfActivityArea);
    }
    let selIndex = `golfActivityArea[${index}].selFlag`;
    //  选中与不选中
    this.setData({
      [selIndex]: !e.currentTarget.dataset.selflag,
      selBatchId: id,
      selBatch: batch,
    });
    console.log("选中的批次号", this.data.selBatchId);
  },
  // 去高尔夫活动详情
  jumpToGolf (e) {
    let that = this;
    const applyStartTime = new Date(
      this.data.applyStartTime.replace(/-/g, "/")
    ).getTime();
    const nowtime = new Date().getTime();
    if (nowtime < applyStartTime) {
      return wx.showToast({
        title: "活动尚未开始,请耐心等待!",
        icon: "none",
      });
    }
    //活动模式 PLUS:1+1模式； PRODUCT:商品模式
    if (wx.getStorageSync("token") && that.data.userType == 0) {
      // && !that.data.isShare不管是否分享都要判断是否注册
      if (wx.getStorageSync("userId") !== "") {
        // 判断是否注册过
        that.getIsRegister(wx.getStorageSync("userId"));
      }
    } else {
      let idCard = wx.getStorageSync("idCard");
      if (wx.getStorageSync("token") && (idCard == null || idCard == "")) {
        return wx.showModal({
          title: "提示",
          content: "请完善个人信息",
          confirmText: "立即前往",
          success (res) {
            if (res.confirm) {
              wx.navigateTo({
                url: `/pages/userinfo/userinfo?isActivity=2`,
                success: function (res) { },
              });
            } else if (res.cancel) {
              return false;
            }
          },
        });
      }
      wx.setStorageSync("applyStartTime", that.data.applyStartTime);
      wx.setStorageSync("applyEndTime", that.data.applyEndTime);
      wx.setStorageSync(
        "golfActivityArea",
        JSON.stringify(this.data.golfActivityArea)
      );
      wx.setStorageSync("activityId", that.data.ids);
      wx.setStorageSync("sub_time1", that.data.sub_time1);
      wx.setStorageSync("sub_time2", that.data.sub_time2);
      console.log(that.data.activity);
      let url = `/pages/activityTemplate/general/selectArea/index?id=${that.data.activity.id
        }&activeMode=${that.data.activeMode}&customerPrice=${that.data.activity.price || 0
        }&salesPrice=${that.data.activity.salesPrice || 0}&salesPay=${that.data.activity.salesPay
        }&lockDate=${that.data.lockDate}&secKill=${that.data.activity.secKill
        }&isQtn=${that.data.activity.isQtn
        }&applyStartTime=${applyStartTime}&creditsSource=${that.data.activity.creditsSource
        }&usableCredits=${that.data.activity.usableCredits}`;
      if (that.data.activity.secKill) {
        url += `&title=${that.activity.title}&coverImg=${that.activity.coverImg}`;
      }
      wx.navigateTo({
        url,
      });
    }
  },
  // 提醒我
  handleWarnMe () {
    let {
      warnInfo,
      activity: { id },
    } = this.data;
    if (warnInfo === "已预约") {
      API.cardCollectNewWarn({ activityId: id, status: 0 }).then((res) => {
        if (res.data.code === 200) {
          this.setData({
            warnInfo: "提醒我",
          });
        }
      });
      return;
    }
    let message = "Y7lBG9cAPzsB1QzpK9MASEp8Xv2YFH4YnkCmILWTlCA";
    wx.getSetting({
      withSubscriptions: true, //是否同时获取用户订阅消息的订阅状态，默认不获取
      success: (res) => {
        if (res.subscriptionsSetting.mainSwitch) {
          //用户是否打开了接收消息的总开关
          if (
            res.subscriptionsSetting.itemSettings != null &&
            res.subscriptionsSetting.itemSettings[message]
          ) {
            // 用户同意总是保持是否推送消息的选择, 这里表示以后不会再拉起推送消息的授权
            const status = res.subscriptionsSetting.itemSettings[message];
            if (status == "accept") {
              // accept：接收，reject：拒绝，ban：已被后台禁止
              wx.requestSubscribeMessage({
                tmplIds: [message],
                success: (item) => {
                  if (item[message] == "accept") {
                    API.cardCollectNewWarn({ activityId: id, status: 1 }).then(
                      (res) => {
                        if (res.data.code === 200) {
                          this.setData({
                            warnInfo: "已预约",
                          });
                        }
                      }
                    );
                  }
                },
                fail: (res) => {
                  console.log("2", res);
                },
              });
            } else {
              wx.openSetting({
                withSubscriptions: true,
                success: (rej) => {
                  if (
                    rej.subscriptionsSetting.itemSettings != null &&
                    rej.subscriptionsSetting.itemSettings[message] == "accept"
                  ) {
                    wx.showToast({
                      title: "权限修改成功，请点击提醒我",
                      icon: "none",
                      duration: 3000,
                    });
                  }
                },
              });
            }
          } else {
            wx.requestSubscribeMessage({
              tmplIds: [message],
              success: (res) => {
                if (res[message] == "accept") {
                  API.cardCollectNewWarn({ activityId: id, status: 1 }).then(
                    (res) => {
                      if (res.data.code === 200) {
                        this.setData({
                          warnInfo: "已预约",
                        });
                      }
                    }
                  );
                }
              },
              fail: (res) => {
                console.log("2", res);
              },
            });
          }
        }
      },
    });
  },
  // // 商品活动选择项目
  // selectProject (e) {
  //   console.log(e.detail);
  //   let item = e.detail
  //   let bannerImg = []
  //   if (item.bannerImg != null && item.bannerImg != '') {
  //     bannerImg = item.bannerImg.split(",")
  //     this.setData({
  //       bannerType: true,
  //       bannerImg
  //     })
  //   } else {
  //     bannerImg = this.data.VSwiper
  //     this.setData({
  //       bannerType: false,
  //       bannerImg
  //     })
  //   }

  // },
  // sureProject (e) {
  //   let item = e.detail
  //   this.setData({
  //     projectId: item.id,
  //     itemObject: item,
  //     showParamModal: false,
  //     ['activity.totalPlace']: item.totalPlace,
  //     ['activity.place']: item.place,
  //     ['activity.originalPrice']: item.originalPrice,
  //     ['activity.price']: item.price,//客户现价
  //     ['activity.minNum']: item.minNum,
  //     ['activity.maxNum']: item.maxNum,//报名次数区间
  //     ['activity.salesPrice']: item.salesPrice,//业务员现价
  //     ['activity.salesMinNum']: item.salesMinNum,
  //     ['activity.salesMaxNum']: item.salesMaxNum,
  //   })
  //   wx.setStorageSync('smallTitle', item.title)
  // },
  selectProgram (e) {
    if (this.data.audit || this.data.checkDetail) return;
    console.log(e.currentTarget.dataset.selectindex);
    let index = e.currentTarget.dataset.selectindex;
    let pragram = this.data.project;
    let item = pragram[index];
    console.log(item, "选择的项目");
    // let VSwiper = this.data.VSwiper
    // if(item.bannerImg != null && item.bannerImg != ''){
    //   VSwiper.imgOptions = []
    //   item.bannerImg.split(",").map((list)=>{
    //     VSwiper.imgOptions.push({url:list})
    //   })
    // }else{
    //   VSwiper.imgOptions = VSwiper.imgOptions
    // }
    // console.log(VSwiper);

    this.setData({
      projectId: item.id,
      selectIndex: index,
      ["activity.totalPlace"]: item.totalPlace,
      ["activity.place"]: item.place,
      // ['activity.title']: item.title,
      ["activity.originalPrice"]: item.originalPrice,
      ["activity.price"]: item.price, //客户现价
      ["activity.minNum"]: item.minNum,
      ["activity.maxNum"]: item.maxNum, //报名次数区间
      ["activity.salesPrice"]: item.salesPrice, //业务员现价
      ["activity.salesMinNum"]: item.salesMinNum,
      ["activity.salesMaxNum"]: item.salesMaxNum,
      // VSwiper:VSwiper
    });
    wx.setStorageSync("smallTitle", item.title);
  },
  //开启项目选择弹窗
  // clickProject () {
  //   if (this.data.audit) return
  //   this.setData({
  //     showParamModal: true
  //   })

  // },
  // 缺少身份证号展示弹框
  closeChooseAreaModal () {
    this.setData({
      chooseArea: !this.data.chooseArea,
    });
  },
  //身份证号输入
  idCardValue (e) {
    const value = e.detail.value;
    this.setData({ idCard: value });
  },
  // 身份证号输入后确认逻辑
  confrimChooseArea () {
    const idCard = this.data.idCard;
    const cardType = this.data.cardType;
    if (idCard == "" || idCard === null) {
      wx.showToast({
        title: "证件号输入格式有误",
        icon: "none",
        duration: 2000,
      });
      return false;
    }
    this.updateUserInfo();
  },
  // 更新用户信息
  async updateUserInfo () {
    const that = this;
    const params = {
      idcard: CryptoJS.Encrypt(that.data.idCard),
      cardType: that.data.cardType,
    };
    const res = await API.updateUserInfo(params);
    if (res.data.code === 200) {
      wx.showToast({
        title: "保存成功",
        icon: "none",
        duration: 2000,
      });
      wx.setStorageSync("idCard", that.data.idCard);
      wx.setStorageSync("cardType", that.data.cardType);
      that.closeChooseAreaModal();
    } else {
      wx.showToast({
        title: res.data.message,
        icon: "none",
        duration: 2000,
      });
    }
    console.log(res);
  },
  // 关闭专属海报弹框
  onClickHide () {
    this.setData({
      showPoster: false,
    });
  },
  // 下载海报
  downPoster () {
    this.setData({
      showPoster: true,
    });
  },
  close (e) {
    wx.showToast({
      title: "请选择身份登录",
      duration: 2000,
      icon: "none",
    });
    this.setData({
      personLogin: true,
    });
  },
  goMyorder () {
    wx.navigateTo({
      url: `/pages/myactivity/myactivity`,
    });
  },
  reject () {
    this.setData({
      showReject: true,
      rejectReason: "",
    });
  },
  triggerEvent (e) {
    console.log(e);
    wx.navigateBack({
      delta: 1,
    });
  },
  nextStep () {
    wx.navigateTo({
      url: "/pages/activityAudit/activityDetail/index",
    });
  },
  confirmChangeTieModal () {
    let avatarUrl = wx.getStorageSync("headImgUrl");
    let phone = wx.getStorageSync("phonebtn"); //加密1
    let gender = wx.getStorageSync("gender"); //1
    let name = wx.getStorageSync("trueName"); //加密1
    let nikename = wx.getStorageSync("nickName"); //1
    let idCard = wx.getStorageSync("idCard"); //加密1,
    let salesCode = this.data.changeSalesCode; //加密

    if (idCard == "" || idCard == null) {
      // 身份证为空时保存
      var data = {
        name: CryptoJS.Encrypt(name),
        phone: CryptoJS.Encrypt(phone),
        gender: gender,
        nikename: nikename,
        avatarUrl: avatarUrl,
        serialCode: CryptoJS.Encrypt(salesCode),
        idCard: "",
      };
    } else {
      var data = {
        name: CryptoJS.Encrypt(name),
        phone: CryptoJS.Encrypt(phone),
        gender: gender,
        nikename: nikename,
        avatarUrl: avatarUrl,
        serialCode: CryptoJS.Encrypt(salesCode),
        idCard: CryptoJS.Encrypt(idCard),
      };
    }
    API.updateBinding(data).then((res) => {
      if (res.data.code == 200) {
        this.setData({
          showSwitchSale: !this.data.showSwitchSale,
        });
      }
    });
  },
  closeChangeTieModal () {
    this.setData({
      showSwitchSale: false,
    });
  },
  openChangeScole () {
    let salesCode = wx.getStorageSync("salesCode"); //加密
    let saleName = wx.getStorageSync("saleName");
    if (
      salesCode != this.data.changeSalesCode ||
      saleName != this.data.changeSaleName
    ) {
      this.setData({
        showSwitchSale: true,
        currentId: saleName + salesCode,
        acceptId: this.data.changeSaleName + this.data.changeSalesCode,
      });
    }
  },
  // 更改证件类型
  identityChange (e) {
    const that = this;
    const identityType = that.data.identityType;
    console.log("picker发送选择改变，携带值为", e.detail.value); //index为数组点击确定后选择的item索引
    this.setData({
      identityIndex: e.detail.value,
      identity: identityType[e.detail.value].name,
      cardType: identityType[e.detail.value].value,
    });
  },
});
