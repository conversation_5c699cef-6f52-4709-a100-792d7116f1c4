/* pages/activityTemplate/vote/pk-home/index.wxss */

.equity-home-nav {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 10;
}
.equity-home-nav .home_nav_menu {
  position: absolute;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.equity-home-nav .home_nav_menu .home_nav_menu_icon {
  position: absolute;
  left: 0;
}

.mymask {
  position:fixed;
  z-index:49;
  width:750rpx;
  height:100%;
  border:none;
  border-radius:0;
}
.mask_content{
  margin-top: 15vh;
}
.reflash_img{
  width: 590rpx;
  height: 880rpx;
  display: block;
  margin: 0 auto;
}
.reflash_text_wrap{
  width: 100%;
  position: absolute;
  top: 20%;
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 46rpx;
  text-align: center;
}
.reflash_btn_wrap{
  width: 100%;
  height: 88rpx;
  position: absolute;
  bottom: 20%;
  text-align: center;
}
.reflash_btn{
  width: 512rpx;
  height: 100%;
  background: #FFAE54;
  border-radius: 16px;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
}

.contain{
  position: relative;
  /* height: 100vh; */
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.btn{
  position: absolute;
  box-sizing: border-box;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.img-com{
  position: absolute;
}

.activity-img{
  position:absolute;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.rick-text{
  position: absolute;
}