const app = getApp();
const API = app.require('/api/request.js')

Page({

  /**
   * 页面的初始数据
   */
  data:{
    list:[],
    rejectReason:'',
    backgroundImg:'',
    activityId:'',
    start:'',
    end:'',
    audit:'', //活动审核判断
    showReject:false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options)
    this.setData({
      activityId:options.activityId,
      audit:options.audit || ''
    })
    this.getBasicInfo()
   
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },
  //获取基本信息
  async getBasicInfo(){
    let that = this
    let params = {
      id:this.data.activityId,
      drawMode:'DRAW'
    }
    let response = null
    if(that.data.audit == 1){
      response = await API.auditDetail({id:this.data.activityId})
    }else {
      response = await API.getActivityDetail(params)
    }

    let activityPageConfig = JSON.parse(response.data.data.activityPageConfig)
    console.log(activityPageConfig)
    if(response.data.code === 200){
      let rule = activityPageConfig.find((item) => item.title === '活动规则')
      console.log(68, rule.componentData)
      let backgroundImg = rule.componentData.find((item) => item.type === 'backgroundImg')
      
      that.setData({
        backgroundImg:backgroundImg.propValue.url,
        list:rule.componentData,
        content:response.data.data.contents,
        start:response.data.data.startTime,
        end:response.data.data.endTime,
        auditStatus:response.data.data.auditStatus,
        financeStatus:response.data.data.financeStatus,
      })

    } else {
      wx.showToast({
        title: response.data.message,
        icon: 'none',
        duration: 2000
      })
    }
  },
  reject() {
    console.log('触发了')
    this.setData({
      showReject:true,
      rejectReason:'',
    })
  },



  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

})