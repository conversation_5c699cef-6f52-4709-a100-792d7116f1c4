// pages/activityTemplate/components/switchSale/switchSale.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isShowClose:{
      type: Boolean,
      value: true,
    },
    isShowCancel:{
      type: Boolean,
      value: true,
    },
    show: {
      type: <PERSON>olean,
      value: false,
      observer: function (newVal, oldVal, changePath) {
        console.log(newVal,oldVal)
        const userType = wx.getStorageSync('userType')
        if (newVal) {
          if((Number(userType) === 1 || Number(userType) === 2)) {
            this.setData({
              _show: true
            })
          }
        } else {
          this.setData({
            _show: newVal
          })
        }
        // if (newVal && ) {
        //   console.log(newVal && Number(this.data.shareUserType) === 3 && (Number(userType) === 1 || Number(userType) === 2))
        //   this.setData({
        //     _show: true
        //   })
        // }
      }
    },
    currentId: {
      type: String,
      value: ''
    },
    shareUserType: {
      type: String,
      value: ''
    },
    acceptId: {
      type: String,
      value: ''
    }

  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    closeChangeTieModal () {
      this.setData({
        _show: !this.data._show,
      })
      this.triggerEvent('close')
    },
    idCardValue (e) {
      const value = e.detail.value
      this.setData({ idCard: value })
      this.triggerEvent('idCardValue', { idCard: value })
    },
    confirmChangeTieModal () {
      this.triggerEvent('sureSwitch')
    },
  }

})
