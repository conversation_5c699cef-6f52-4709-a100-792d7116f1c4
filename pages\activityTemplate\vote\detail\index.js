const API = require('../../../../api/request.js')
const http = require('../../../../utils/util.js');
const { showTab, handleUserStorage } = require('../../../../utils/aboutLogin')

const { setSalesCode, getWhetherAuth } = require('../../../../utils/querySalesCode')
const app = getApp();
Page({
  data: {
    message: '',
    productInfo: {
      productId: '',
      activityId: ''
    },
    btnDisabled: false,
    voteInfo: {},
    personLogin: false, // 登陆者身份框
    isClose: true, // 登陆者身份框
    showModalDlg: false, // 遮挡框不能点击（未登录）
    goBackHome: '',
    applyStartTime: '', // 活动开始时间
    voteSuccess: false,
    title:'',
    isShare: false,
    shareUserType: '',
    showSwitchSale: false,
    changeSalesCode: '',
    currentId: '',
    acceptId: '',

    goThirdDialog: false,
  },
  onLoad: function (options) {
    console.log('投票详情页options', options);
    this.data.productInfo.productId = options.productId
    this.data.goBackHome = options.activityId
    this.setData({
      shareUserId: options.shareUserId,
      flagConnect: options.flagConnect,
      title: options.title,
    })

    if (options.isShare) {
      this.setData({
        isShare: true,
        changeSalesCode: options.salesCode,
        changeSaleName: options.saleName,
        shareUserType: options.userType
      })
    }
  },
  onShow: function () {
    this.handleSign()
  },
  toPreview (e) {
    console.log(e.currentTarget.dataset.url);
    wx.previewImage({
      urls: this.data.voteInfo.mediaSrc,
      current: e.currentTarget.dataset.url
    })
  },
  goBack () {
    if (this.data.goBackHome) {
      wx.switchTab({
        url: "/pages/activityTemplate/vote/home/<USER>",
      })
    } else {
      wx.navigateBack({
        delta: 1
      })
    }
  },
  handleSign () {
    var that = this
    if (http.userAre() == '') { // 用户未登录，要求先点击加入活动
      // 用户id判断用户是否登录（微信弹窗）
      that.setData({
        showModalDlg: true
      })
      return
    } else {
      that.setData({
        showModalDlg: false
      })
      if (wx.getStorageSync('refreshUserInfo')) {
        that.getUserInfo();
      } else {
        if (wx.getStorageSync('salesCode') || wx.getStorageSync('pcode')) {
          that.setData({
            salesCode: wx.getStorageSync('salesCode'),
            credits: wx.getStorageSync('credits'),
            type: wx.getStorageSync('userType'),
          })
        }
        that.getIsRegister()
      }
    }
  },
  //用户微信登录授权(点击进入)
  userInfoHandler () {
    console.log('执行了用户微信登录')
    var that = this;
    wx.removeStorageSync('defaultPerson');
    wx.removeStorageSync('refreshUserInfo')
    app.getUserInfo(function (userInfo) {
      console.log(userInfo)
      wx.setStorageSync('userId', userInfo.id);
      wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
      wx.setStorageSync('nickName', userInfo.nikename);
      wx.setStorageSync('openId', userInfo.openId);
      wx.showToast({
        title: '授权成功',
        icon: "none",
        duration: 1500,
      })
      that.setData({
        userId: userInfo.id,
        isAuthorize: true
      })
    })
    var timer3 = setInterval(() => {
      if (http.userAre() != '') {
        that.getUserInfo();
        clearInterval(timer3);
      }
    }, 100)
  },
  //   获取用户信息
  getUserInfo () {
    let that = this;
    var userId = wx.getStorageSync("userId")
    var token = wx.getStorageSync("token")
    that.setData({
      headImgUrl: (wx.getStorageSync('headImgUrl') == '') ? '../../image/user.png' : wx.getStorageSync('headImgUrl'),
      nickName: (wx.getStorageSync('nickName') == '') ? '请点击登录' : wx.getStorageSync('nickName'),
    })
    // 获取用户所属门店信息
    if (token) {
      API.getInfoById().then(res => {
        console.log('获取用户所属门店信息', res)
        if (res.data.code == 200) {
          console.log(res.data)
          that.setData({
            credits: res.data.data.credits,
            type: res.data.data.type,
            salesCode: res.data.data.salesCode,
          })
          console.log(that.data.salesCode);

          handleUserStorage(res.data.data)
          if (wx.getStorageSync('userId') !== '') {
            console.log('判断是否注册过')
            // 判断是否注册过
            that.getIsRegister(wx.getStorageSync('userId'))
          }
        }
      })
    }
  },
  close (e) {
    console.log(e)
    var that = this
    that.setData({
      personLogin: false,
    })
    wx.showToast({
      title: '请选择身份登录',
      duration: 2000,
      icon: "none",
    })
  },
  getIsRegister (userId) {
    let that = this
    if (http.unRegister()) {
      // 没有注册
      // 查询绑定的业务员
      that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode,that.data.changeSaleName)
      
      this.setData({
        personLogin: true,
        showModalDlg: false
      })
      console.log('--🏀this.data.personLogin--', this.data.personLogin)
    } else {
      showTab()
      that.setData({
        showModalDlg: false
      })

    }
    this.getVoteInfo()

    this.updatedSalesCode()
  },
  updatedSalesCode () {
    if (this.data.isShare) {
      let salesCode = wx.getStorageSync('salesCode')
      let saleName = wx.getStorageSync('saleName')
      if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
        this.setData({ showSwitchSale: true, currentId: saleName + salesCode, acceptId: this.data.changeSaleName + this.data.changeSalesCode })
      }
    }
  },


  getVoteInfo () {
    const that = this
    let { voteInfo, productInfo, message, btnDisabled } = this.data
    API.workDeatil({ productId: this.data.productInfo.productId }).then(res => {
      if (res.data.code === 200) {
        const { votes, productVoteLeft, leftVote, id, ownerAvatar, ownerName, mediaType, mediaSrc, cfgInfo, activityId, gap, rank, applyStartTime,salesName ,terminal} = res.data.data
        this.setData({
          applyStartTime: new Date(applyStartTime.replace(/-/g, '/')).getTime()
        })
        productInfo.activityId = activityId
        btnDisabled = leftVote ? false : true
        voteInfo = { votes, activityId, gap, rank, id, ownerAvatar, ownerName, leftVote, productVoteLeft,salesName ,terminal}
        if (mediaType === 'VIDEO') {
          voteInfo.mediaSrc = mediaSrc
          message = 'video'
        } else {
          voteInfo.mediaSrc = mediaSrc.split(',')
          message = 'image'
        }
        let cfgInfoNew = {}
        cfgInfo ? Array.isArray(JSON.parse(cfgInfo)) ? JSON.parse(cfgInfo).forEach((item) => {
          cfgInfoNew = {
            ...cfgInfoNew,
            ...item
          }
        }) : cfgInfoNew = {
          ...JSON.parse(cfgInfo)
        } : ''
        console.log(cfgInfoNew)
        voteInfo.cfgInfo = cfgInfoNew['拉票宣言']
        console.log(cfgInfo)
        voteInfo.title = cfgInfoNew['参赛名称']
        var reg=new RegExp('TXQImgPath/','ig');
        let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
        let activityData  =  res.data.data
        let activityPageData = activityData.activityPageConfig&&activityData.activityPageConfig.replace(reg,dynamicDomainName)
        const activityPageConfig = JSON.parse(activityPageData).find((item) => item.title === '作品详情')
        if (activityPageConfig !== undefined) {
          activityPageConfig.componentData.map((item) => {
            if (item.type === 'button' && (item.btEvent === 'canvass' || item.btEvent === 'vote')) {
              delete item.style.top
              let backgroundImage = ''
              if (item.bgStatus == 1) {
                backgroundImage = `url(${item.imgOptions.url})`
              }
              item.style = {
                ...item.style,
                backgroundImage
              }
            }
            item['styleStr'] = that.handlerStyle(item.style)
          })
          console.log('页面组件元素', activityPageConfig.componentData)
          const backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
          const statisticsConfig = activityPageConfig.componentData.find((item) => item.type === 'statistics')
          that.setData({
            backgroundImg: backgroundImg.propValue.url,
            componentData: activityPageConfig.componentData,
            statisticsConfig: statisticsConfig.style,
            statisticsConfigList:statisticsConfig.imgOptions
          })
        }
        console.log('页面数据', activityPageConfig)
        const activityVoteConfig = activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '投票成功') : {}
        if (activityVoteConfig !== undefined) {
          activityVoteConfig.componentData.map((item) => {
            item['styleStr'] = that.handlerStyle(item.style)
          })
          const backgroundImgVote = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
          that.setData({
            componentDataVote: activityVoteConfig.componentData,
            backgroundImgVote: backgroundImgVote.propValue.url
          })
        }
        console.log(activityVoteConfig)
        console.log('voteInfo',voteInfo)
        this.setData({
          voteInfo,
          productInfo,
          message,
          btnDisabled
        })
        that.activityDetail(activityId)
      } else {
        wx.showToast({
          title: res.data.message,
          duration: 2000,
          icon: "none",
        })
      }
    })
  },
  handlerStyle (style) {
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },

  closeChangeTieModal () {
    this.setData({ showSwitchSale: false })
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  onBuriedPoint (name, title) {
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let time = new Date().toLocaleString()
    app.sensors.track(name, {
      name: title,
      personScan: nickName,
      scanTime: time,
      userId: userId
    });
  },
  handleXuanyan (data, num) {
    const value1 = data.slice(1)
    const value2 = value1.substring(0, value1.length - 1)
    const value3 = value2.split(',')[num - 1].split(':')[1]
    const value4 = value3.slice(1)
    const value = value4.substring(0, value4.length - 1)
    return value
  },
  async handleVote () {
    const that = this
    if (this.data.voteInfo.leftVote) {
      if (this.data.voteInfo.productVoteLeft) {
        this.onBuriedPoint('castVote', '投票')
        const flag = await getWhetherAuth(this.data.productInfo.activityId)
        console.log(flag)
        if (!flag) return
        API.voteTrigger(this.data.productInfo).then(res => {
          if (res.data.code === 200) {
            if(this.data.isFirst){
              // 跳转进入第三方对应ID的问卷/文章页面；投票操作
              this.setData({goThirdDialog: true, isFirst: false})
            }else{
              // 展示投票成功弹窗
              that.closeVoteSuccessModal()
            }

            this.getVoteInfo()
            
            --this.data.voteInfo.leftVote
          } else {
            wx.showToast({
              title: res.data.message,
              icon: "none",
              duration: 2500,
            })
          }
        })
      } else {
        wx.showToast({
          title: '每天为同一作品投票次数已达上限',
          duration: 2000,
          icon: "none",
        })
      }
    }
  },
  goThird(){
    this.setData({goThirdDialog: false})
    const src = encodeURIComponent(this.getUrlRes.returnUrl)
    wx.navigateTo({ url: `/pages/activityTemplate/vote/webview/index?id=${this.data.activityInfo.id}&src=${src}`})
  },
  closeShowGoThird(){
    this.setData({goThirdDialog: false})
  },
  // 马上拉票
  onShareAppMessage: async function (e) {
    console.log('--✨🍎', e)
    this.onBuriedPoint('solicitVote', '拉票')
    let userId = wx.getStorageSync('userId')
    const applyStartTime = this.data.applyStartTime
    const nowtime = new Date().getTime()
    const { canvassText } = this.data.activityInfo.voteConfig
    let salesCode = wx.getStorageSync('salesCode')
    let saleName = wx.getStorageSync('saleName')
    let userType = wx.getStorageSync('userType')

    if (e.from === 'button' && nowtime < applyStartTime) {
      wx.showToast({
        title: '活动尚未开始,请耐心等待',
        icon: 'none'
      })
    } else {
      let title = canvassText.replace('#微信昵称#', wx.getStorageSync('nickName')).replace('#活动名称#', this.data.activityInfo.title).replace('#作品名称#', this.data.voteInfo.cfgInfo)
      let imageUrl = this.data.activityInfo.shareUrl
      // let title = this.data.voteInfo.cfgInfo ? this.data.voteInfo.cfgInfo : '快来投我一票吧~'
      const path = `/pages/activityTemplate/vote/detail/index?productId=${this.data.productInfo.productId}&activityId=${this.data.productInfo.activityId}&shareUserId=${wx.getStorageSync('userId')}&clientShareUserId=${userId}`
      return {
        title: title,
        imageUrl: imageUrl,
        path: `${path}&salesCode=${salesCode}&saleName=${saleName}&isShare=1&userType=${userType}`,
      }
    }

  },
  // 活动详情
  activityDetail (activityId) {
    let that = this
    let data = {
      activityId
    }
    API.voteDetail(data).then(async res => {
      console.log('活动详情', res)
      if (res.data.code == '200') {
        that.setData({
          activityInfo: res.data.data,
          voteConfig:res.data.data?.voteConfig || {}
        })

        // 投票活动如有配置【奖励领取条件】，则在每日投票模式下，当所有用户每日第一次投票时 如正常返回ID，则点击投票成功弹窗中的“立即前往”按钮，直接跳转进入第三方对应ID的问卷/文章页面；同时进行投票操作
        // ("DRAW","抽奖活动"), ("SIGN_DRAW","签到抽奖 "), ("VOTE","投票"),
        // receiveType 奖励领取条件1问卷2文章/视频
        // const {receiveType} = this.data.activityInfo.voteConfig
        // if(receiveType){
        //   const activityId = this.data.activityInfo.id
        //   const backUrl = encodeURIComponent(`#/results?activityType=VOTE&activityId=${activityId}`)
        //   const res = await API.signInGetReceiveUrl({activityId, backUrl, receiveType, type: "VOTE"})
        //   if(res.data.code == 200){
        //     // 今日未跳转第三方 展示弹窗时不做投票操作
        //     if(res.data.data.isNot === 'n'){
        //       this.getUrlRes = res.data.data
        //       // . 如正常返回ID，并未领奖
        //       this.setData({isFirst: this.getUrlRes.isNot === 'n'})
        //     }
        //   }else{
        //     wx.showToast({
        //       title: res.data.message,
        //       duration: 2000,
        //       icon: "none",
        //     })
        //   }
        // }
        //注销掉
      } else {
        wx.showToast({
          title: res.data.message,
          icon: 'none'
        })
      }
      that.setData({
        loadAnimation: false
      })
    })
  },
  closeVoteSuccessModal () {
    this.setData({
      voteSuccess: !this.data.voteSuccess
    })
  },
  goLottery () {
    const { drawTrigger, drawId } = this.data.activityInfo.voteConfig
    console.log(this.data.activityInfo.voteConfig)
    this.closeVoteSuccessModal()
    if (drawTrigger == '1') {
      wx.navigateTo({
        url: `/pages/activityTemplate/lottery/lottery?activityId=${drawId}&mode=VOTE&qtnId=${this.data.activityInfo.id}`,
      })
    }
  }
})