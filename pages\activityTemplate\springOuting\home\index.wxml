<!-- pages/activityTemplate/springOuting/home/<USER>
<view class="spring">
  <!-- 导航栏 -->
  <!-- <view class="equity-home-nav" style="height: {{navBarData.navBarHeight}}px">
    <view class="home_nav_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px">
      <view class="{{isShowTitle ? 'fade-in' : 'fade-out'}}">翻牌踏青</view>
      <van-icon name="arrow-left" size="1.3em" color="#fff" bind:click="back" class="home_nav_menu_icon" style="left:{{navBarData.menuRight}}px;" />
    </view>
  </view> -->
  <!--  -->
  <view class="contnet {{ audit ? 'contnet_audit' : ''}}">
    <view wx:for="{{homePage}}" wx:key="index">
      <view wx:if="{{!item.isHide}}">
        <view class="contnet_item  {{ item.component === 'VButton' ? 'contnet_item_VButton' : ''}}" style="{{item.styleStr}}">
          <image wx:if="{{item.component === 'Picture'}}" class="contnet_item_img" src="{{item.propValue.url}}"></image>
          <view wx:if="{{item.component === 'VButton'}}" data-item="{{item}}" bind:tap="onclickBtn" class="contnet_item_btn">
            {{item.propValue}}
          </view>
          <!--  -->
          <view wx:if="{{item.component === 'flip'}}" class="contnet_item_flip">
            <image class="contnet_item_flip_img" src="{{item.imgOptions.url}}"></image>
            <view class="contnet_item_flip_card">
              <view wx:for="{{cardList}}" wx:key="j" wx:for-item="ele" wx:for-index="j" class="contnet_item_flip_card_item" data-index="{{j}}" data-item="{{ele}}" bind:tap="changeCrad">
                <image class="contnet_item_flip_card_item_img" src="{{ ele.drawn ? ele.backUrl :  ele.url }}"></image>
              </view>
            </view>
          </view>
          <!--  -->
          <view wx:if="{{item.component === 'RectShape'}}" class="contnet_item_RectShape">
            剩余抽奖次数
            <text class="contnet_item_RectShape_text">{{restDrawNum}}</text>
            次
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
<!-- 未中奖 -->
<van-overlay show="{{ showTipsPage }}" z-index="99999999">
  <view class="wrapper">
    <view class="wrapper_content">
      <view wx:for="{{tipsPage}}" wx:key="index">
        <view wx:if="{{!item.isHide}}">
          <view class="wrapper_content_item {{ item.component === 'VButton' ? 'wrapper_content_item_btn' : '' }}" style="{{item.styleStr}}">
            <image wx:if="{{item.component === 'Picture'}}" class="wrapper_content_item_img" src="{{item.propValue.url}}"></image>
            <view wx:if="{{item.component === 'VButton'}}" data-item="{{item}}" bind:tap="closeNothingPage" class="wrapper_content_item_btn_info">
              {{item.propValue}}
            </view>
            <view wx:if="{{ item. component === 'plainText' }}">{{item.propValue}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</van-overlay>
<!-- 活动审核显示底部操作按钮 -->
<audit linkType="draw" activityId="{{activityId}}" wx:if="{{audit}}" bind:reject="reject"></audit>
<!-- 专属海报弹框 -->
<post-popup show="{{showPoster}}" wx:if="{{showPoster}}" id="poster" codeUrl="{{qrcode}}" activityName="{{title}}" isShowBtn="{{false}}" banner="{{postersBanner}}" title="{{posterText}}" textStyle="{{textStyle}}" bind:onClickHide="onClickHide" audit="{{audit}}"></post-popup>
<!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" salesCode="{{changeSalesCode}}" bind:close="closeChangeTieModal"></switch-sale>
<!-- 活动审核驳回弹框 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" audit="{{auditStatus}}" financeStatus="{{financeStatus}}" rejectReason="{{rejectReason}}"></reject-reason>
<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close" allShow='{{ true }}'></login-box>
<!-- 登录授权弹框 -->
<van-overlay show="{{ showModalDlg }}" z-index="99999999">
  <view class="wrapper">
    <view class="wrapper_info">
      <view class="wrapper_info_title">参与活动</view>
      <view class="wrapper_info_btn" bindtap="userInfoHandler">点击参与</view>
    </view>
  </view>
</van-overlay>
<!--  -->
<van-overlay show="{{ isShowAnimation }}" z-index="99999999">
  <view class="wrapper">
    <view class="card {{ isShowAnimationCard? 'active' : '' }}  ">
      <image class="card-face" src="{{ cardUrl }}"></image>
      <image class="card-face card-back" src="{{ cardBackUrl }}"></image>
    </view>
  </view>
</van-overlay>

<overTips content="{{ message }}" showClose="{{false}}" isShowOverlay="{{isShowOverlay}}" bind:closeOverTip="closeOverTip"></overTips>
