<!--pages/activityTemplate/vote/pk-result/index.wxml-->

<view class="equity-home-nav" style="height: {{navBarData.navBarHeight}}px">
  <view class="home_nav_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px">
    <view>财富女王 梦想飞翔</view>
    <van-icon name="arrow-left" size="1.3em" color="#fff" bind:click="back" class="home_nav_menu_icon" style="left:{{navBarData.menuRight}}px;" />
  </view>
</view>

<view class="contain" style="height: {{height*2}}rpx; background-image:url({{backgroundImg}})">
  <block wx:for="{{componentData}}" wx:key="index">
    <view wx:if="{{item.component == 'VButton' && item.type != 'shareBtn'}}" style="{{item.styleStr}}; background-image: url({{item.bgStatus ==1 ? item.imgOptions.url : null}}); background-color: {{item.bgStatus == 1 ? 'transparent' : item.style.backgroundColor }};border-style: {{item.style.borderStyle}};border-width: {{item.style.borderWidth ? item.style.borderWidth*2 : 0}}rpx;" class="btn" bindtap="btnClick" data-type="{{item.type}}">
      {{item.propValue}}
    </view>
    <button open-type="share" wx:if="{{item.type == 'shareBtn' && !item.isHide}}" style="{{item.styleStr}}; background-image: url({{item.bgStatus ==1 ? item.imgOptions.url : null}}); background-color: {{item.bgStatus == 1 ? 'transparent' : item.style.backgroundColor }};border-style: {{item.style.borderStyle}};border-width: {{item.style.borderWidth ? item.style.borderWidth*2 : 0}}rpx;" class="btn" bindtap="btnClick" data-type="{{item.type}}">
      {{item.propValue}}
    </button>

    <view wx:if="{{item.type == 'works'}}">
      <swiper wx:if="{{voteConfig.mediaType == 'IMG'}}" class="work-swiper" style="{{item.styleStr}}" autoplay="true" interval='3000' circular='true'>
        <swiper-item wx:for="{{voteProductList[userChooseIdx].mediaList}}" wx:for-index="imgindex" wx:for-item="imgItem" wx:key="imgindex">
          <image class="work-img" src="{{imgItem}}" alt=""></image>
        </swiper-item>
      </swiper>
      <view wx:else>
        <video class="work-video" style="{{item.styleStr}}" show-center-play-btn="{{false}}" controls="controls" src="{{voteProductList[userChooseIdx].mediaSrc}}"></video>
      </view>
    </view>

    <view wx:if="{{item.type == 'text'}}" class="rick-text" style="{{item.styleStr}}">
      <rich-text nodes="{{item.propValue}}"  class="ql-editor"></rich-text> 
    </view>

    <view wx:if="{{item.type == 'progressBar'}}" class="vote-pk-progress-wrap" style="{{item.styleStr}}">
      <view class="vote-pk-progress">
        <view class="pk-progress left-progress" style="background-color: {{item.propValue.options.mode == 0 ? item.propValue.options.left : '' }}; background-image: url({{item.propValue.options.mode == 0 ? '' : item.propValue.options.leftImg.url}}); background-size: {{leftProgressW}}rpx 100%; width: {{leftProgressW}}rpx; color: {{item.propValue.textColor.left}}">
          <view class="progress-num left-progress-num">{{leftVotePercent}}%</view>
          <view class="progress-text left-progress-text">{{voteProductList[0].chooseName}}</view>
        </view>
        <image class="separate-img" src="{{item.propValue.separateImg.url}}" style="left: {{leftProgressW - 50}}rpx"></image>
        <view class="pk-progress right-progress" style="background-color: {{item.propValue.options.mode == 0 ? item.propValue.options.right : '' }}; background-image: url({{item.propValue.options.mode == 0 ? '' : item.propValue.options.rightImg.url}}); background-size: {{650 - leftProgressW}}rpx 100%; width: {{650 - leftProgressW}}rpx;  color: {{item.propValue.textColor.right}}">
          <view class="progress-num right-progress-num">{{rightVotePercent}}%</view>
          <view class="progress-text right-progress-text">{{voteProductList[1].chooseName}}</view>
        </view>
      </view>
    </view>

    <block wx:if="{{item.component === 'Picture'}}">
      <image class="img-com" style="{{item.styleStr}}" src="{{item.propValue.url}}"></image>
    </block>
  </block>
</view>