const API = require('../../../../api/request.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showReject:false,
    rejectReason:'',
    formList: [
      {
        title: '活动说明',
        value: '',
        label: 'contents'
      },
      {
        title: '投票时间',
        value: '',
        label: 'voteTime'
      },
      {
        title: '投票须知',
        value: '',
        label: 'voteNotes',
      },
      {
        title: '报名时间',
        value: '',
        label: 'applyTime'
      },
      {
        title: '报名须知',
        value: '',
        label: 'applyNotes'
      },
    ],
    title: '',
    activityId: ''
  },
  onLoad: function (options) {
    this.setData({
      activityId:options.activityId,
      audit:options.audit
    })
  },
  onShow: function () {
    this.handleformValue()
  },
  async handleformValue() {
    const that = this
    let { title, formList } = this.data
    let res = null
    if(that.data.audit == 1){
      res = await API.auditVoteDetail({id:that.data.activityId})
    }else {
      res = await API.voteDetail({activityId: this.data.activityId})
    }
    console.log('--🏀res🏀--', res)
    if(res.data.code === 200) {
        
      const {data} = res.data
      title = data.title
      formList.forEach(item => {
        if(item.label === 'voteTime') {
          item.value = `${data.voteConfig.voteBegin} ~ ${data.voteConfig.voteEnd}`
        } else if(item.label === 'applyTime') {
          item.value = `${data.applyStartTime} ~ ${data.applyEndTime}`
        } else if(item.label === 'voteNotes') {
          item.value = data.voteConfig[item.label]
        } else if(item.label === 'applyNotes') {
          item.value = data.voteConfig[item.label]
        } else {
          item.value = data[item.label]
        }
      })
      const activityPageConfig = JSON.parse(data.activityPageConfig).find((item) => item.title === '活动说明')
      if(activityPageConfig !== undefined){
        activityPageConfig.componentData.map((item) => {
          item['styleStr'] = that.handlerStyle(item.style)
        })
        console.log('页面组件元素',activityPageConfig.componentData)
        const backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
        that.setData({
          backgroundImg: backgroundImg.propValue.url,
          componentData:activityPageConfig.componentData
        })
      }
      console.log(activityPageConfig)
      this.setData({
        title,
        formList,
        financeStatus: res.data.data.financeStatus||'',
        auditStatus: res.data.data.auditStatus||''
      })
    }
  },
  //处理组件样式
  handlerStyle(style) {
    console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width',{
        name:'width',
        type:1
      }],
      ['height', {
        name:'height',
        type:1
      }],
      ['borderRadius', {
        name:'border-radius',
        type:1
      }],
      ['top', {
        name:'top',
        type:1
      }],
      ['left', {
        name:'left',
        type:1
      }],
      ['borderColor', {
        name:'border-color',
        type:2
      }],
      ['fontSize', {
        name:'font-size',
        type:1
      }],
      ['fontWeight', {
        name:'font-weight',
        type:2
      }],
      ['textAlign', {
        name:'text-align',
        type:2
      }],
      ['color', {
        name:'color',
        type:2
      }],
      ['backgroundColor', {
        name:'background-color',
        type:2
      }]
    ])
    for(let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key),style[key])
    }
    return newStyle
  },
  reject(){
    this.setData({
      showReject:true,
      rejectReason:''
    })

  },
  handlerCssStyleStr(keyValueStr,value){
    console.log(keyValueStr)
    if(keyValueStr === undefined) return ''
    if(keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if(keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
})