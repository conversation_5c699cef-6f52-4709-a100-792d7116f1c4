<!--pages/activityTemplate/vote/pk-home/index.wxml-->

<!-- 加载动画 -->
<loading-animation wx:if="loadAnimation"></loading-animation>

<view class="equity-home-nav" style="height: {{navBarData.navBarHeight}}px">
  <view class="home_nav_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px">
    <view>财富女王 梦想飞翔</view>
    <van-icon name="arrow-left" size="1.3em" color="#fff" bind:click="back" class="home_nav_menu_icon" style="left:{{navBarData.menuRight}}px;" />
  </view>
</view>

<view class="contain" style="height: {{height*2}}rpx; background-image:url({{backgroundImg}})">
  <block wx:for="{{componentData}}" wx:key="index">
    <view wx:if="{{item.component == 'VButton' && item.btEvent != 'poster' && item.type != 'shareBtn'}}" style="{{item.styleStr}}; background-image: url({{item.bgStatus ==1 ? item.imgOptions.url : null}}); background-color: {{item.bgStatus == 1 ? 'transparent' : item.style.backgroundColor }};border-style: {{item.style.borderStyle}};border-width: {{item.style.borderWidth ? item.style.borderWidth*2 : 0}}rpx;" class="btn" bindtap="btnClick" data-type="{{item.type}}">
      {{item.propValue}}
    </view>
    <view wx:if="{{item.type == 'text'}}" class="rick-text" style="{{item.styleStr}}">
      <rich-text nodes="{{item.propValue}}"  class="ql-editor"></rich-text> 
    </view>
    <button open-type="share" wx:if="{{item.type == 'shareBtn' && !item.isHide}}" style="{{item.styleStr}}; background-image: url({{item.bgStatus ==1 ? item.imgOptions.url : null}}); background-color: {{item.bgStatus == 1 ? 'transparent' : item.style.backgroundColor }};border-style: {{item.style.borderStyle}};border-width: {{item.style.borderWidth ? item.style.borderWidth*2 : 0}}rpx;" class="btn" bindtap="btnClick" data-type="{{item.type}}">
      {{item.propValue}}
    </button>
    <block wx:if="{{item.type === 'poster' && activityInfo.flagPoster == 1}}">
      <!-- 专属海报弹框 -->
      <post-popup show="{{showPoster}}" codeUrl="{{activityInfo.qrcode}}" activityName="{{activityInfo.title}}" btnText="{{item}}" banner="{{posterBackgroundImg.propValue.url}}" title="{{posterText.propValue}}" textStyle="{{posterText}}" bind:onClickHide="onClickHide" bind:clickDownPoster="downPoster" audit="{{audit}}"></post-popup>
      <!-- 专属海报弹框 -->
    </block>
    <block wx:if="{{item.component === 'Picture'}}">
      <image class="img-com" style="{{item.styleStr}}" src="{{item.propValue.url}}"></image>
    </block>
  </block>
</view>

<!-- 活动审核显示底部操作按钮 -->
<audit activityId="{{activityId}}" activityType="9" linkType="{{linkType}}" wx:if="{{audit}}" bind:reject="reject"></audit>
<!-- 活动审核显示底部操作按钮 -->
<!-- 投票内容 -->
<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close" allShow='{{isClose}}'></login-box>
<!-- 登陆者身份选择 -->
<!-- 参与活动触发弹框 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler">点击参与</button>
  </view>
</view>
<!-- 参与活动触发弹框 -->

<!-- 拉票活动选择 -->
<!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" salesCode="{{changeSalesCode}}" bind:close="closeChangeTieModal"></switch-sale>
<!-- 分享页面是否切换业务员 -->

<!-- 活动审核驳回弹框 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" audit="{{auditStatus}}" financeStatus="{{financeStatus}}" rejectReason="{{rejectReason}}" ></reject-reason>
<!-- 活动审核驳回弹框 -->