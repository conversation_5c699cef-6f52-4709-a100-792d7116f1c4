Page{
  line-height: 1.15;
  /* background: #F3301C; */
}

.flip-bg{
	position: absolute;
	left: 32rpx;
	top: 508rpx;
	width: 686rpx;
	height: 1028rpx;
	border-radius: 24rpx;
	overflow: hidden;
	box-sizing: border-box;
  padding: 122rpx 76rpx 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.flip-card{
	position: absolute;
	left: 32rpx;
	top: 508rpx;
	width: 686rpx;
  height: 1028rpx;
  overflow: hidden;
  box-sizing: border-box;
	box-sizing: border-box;
}
.cardItem{
  display: flex;
  position: relative;
  align-items: center;
  width: 100%;
  height: 200rpx;
  border-radius: 24rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
  overflow: hidden;
}
.active{
	border: 4rpx solid #FF5FA2;
}
  .product{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 200rpx;
    border-radius: 24rpx;
    /* margin-right: 11rpx; */
  }
  /* .title{
    width: 324rpx;
    margin-right: 60rpx;
    font-size: 34rpx;
    font-weight: 600;
    color: #5737B6;
  } */
  .select{
    position: absolute;
    width: 44rpx;
    height: 44rpx;
    top: 82rpx;
    right: 54rpx;
  }
  .flipDetails{
    position: absolute;
  }
  .richText{
    position: absolute;
    left: 0;
    top: 0;
    padding: 40rpx 60rpx 20rpx;
    box-sizing: border-box;
  }
image{
    width: 100%;
    height: 100%;
  }

.flip_crad{
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
	height: 100%;
	overflow: hidden;
	overflow-y: auto;
}
.flip_crad_item{
	width: 123px;
	height: 128px;
	border-radius: 17px;
	box-sizing: border-box;
	margin-bottom: 32rpx;
}

.flip-img{
	width: 100%;
	height: 100%;
	border-radius: 17px;
	display: block;
}

.task-wrap{
	height: 320rpx;
	overflow-y: auto;
	padding: 0 40rpx 40rpx;
	box-sizing: border-box;
	
}
.task-wrap::-webkit-scrollbar {
  width: 0 !important;
}
.task-item{
  padding: 25rpx 0 12rpx;
  box-sizing: border-box;
  border-bottom: 2rpx dashed rgba(127, 127, 127, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.task-item:first-child{
  padding-top: 12rpx;
}
.task-name{
  font-size: 29rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #434343;
}
.task-desc{
  font-size: 22rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #7F7F7F;
}
.task-btn{
  width: 122rpx;
  height: 49rpx;
  /* background: linear-gradient(0deg, #FF3232, #FF764C); */
  border-radius: 24rpx;
  font-size: 27rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  text-align:center;
  line-height: 49rpx;
  margin: 0;
}
.task-btn-complete{
  opacity: 0.3;
}
.share-btn{
	position: absolute;
	top: 37rpx;
	right: 37rpx;
	width: 114rpx;
	height: 49rpx;
	background: rgba(0, 0, 0, 0.26);
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 29rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #FFFFFF;
}
.share-img{
  height: 24rpx;
  width: 29rpx;
  padding-right: 6rpx;
}
.back{
  position: fixed;
  left: 20rpx;
  top: 65rpx;
  z-index: 99;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  /* background: rgb(#fff,#fff,#fff,0.5); */
}
.modalDlg {
  width: 80%;
  height: 300rpx;
  position: fixed;
  top: 62%;
  left: -1%;
  z-index: 100;
  margin: -370rpx 85rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
}
.modalDlg text {
  font-size: 30rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 180rpx;
  width: 100%;
  font-weight: bold;
}
.modalDlg button {
  width: 500rpx;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #FF564AFF;
  background-color: #FF564AFF;
  color: #fff;
}
.mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 10;
  opacity: 0.7;
}

.activity{
  width: 100vw;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.lottery{
  width: 100vw;
  position: relative;
  background: #285FF6;
}
.ql-editor{
  padding: 0;
  height: auto;
}
.rule{
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18rpx;
  font-family:' PingFang SC';
  /* font-weight: bold; */
  position: absolute;
  word-wrap: break-word;
  text-align: center;
  box-sizing: border-box;
  z-index: 1;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border:2rpx solid transparent;
}
.rule view {
  border: 1px solid transparent;
}
.myReward{
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(0deg, #FFE4DE 0%, #FFFFFF 100%);
  /* border-radius: 6rpx 0px 0px 6rpx; */
  font-size: 18rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #BB0F05;
  text-align: center;
  position: absolute;
  word-wrap: break-word;
  border-style: solid;
  box-sizing: border-box;
  z-index: 1;
  background-repeat: no-repeat;

}
.title{
  width: 750rpx;
  height: 380rpx;
  position: absolute;
  top:0;
  left: 0;
}
.wheel{
  width: 690rpx;
  height: 690rpx;
  position: absolute;
  top:392rpx;
  left: 30rpx;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.luckWheel,.luckGrid{
  width: 690rpx;
  height: 690rpx;
  position: relative;
}
.frequency{
  font-size: 29rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  position: absolute;
}
.roster{
  /* background-color: #FCE598; */
  border-radius: 10rpx;
  position: absolute;
  margin-bottom: 68rpx;
  padding:12rpx;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.roster_title{
  font-size: 37rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #F63019;
  text-align: center;
  margin:10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.roster_title image {
  width: 141rpx;
  height: 39rpx;
}
.roster_title .egg_roster{
  width: 54rpx;
  height: 20rpx;
}
.roster_title view {
  margin: 0 24rpx;
}
.roster_title image:first-child{
  transform: rotateY(-180deg);
}
.roster_title .egg_roster:first-child{
  transform: rotateY(0deg);
}
.roster_title .egg_roster:last-child{
  transform: rotateY(-180deg);
}
.roster_main{
  height: 100%;
  padding:0rpx 10rpx;
  overflow: hidden;
  background: #fff;
}
.roster_item{
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #434343;
  height: 70rpx !important;
  border-bottom: 1px solid rgba(127, 127, 127, .31);
  line-height: 70rpx;
  overflow: hidden;

}
.swiperItem{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 37rpx;
}
.swiperItem view:first-child{
  margin-right: 20rpx;
  overflow: hidden;
}
.swiperItem view:last-child{
  width:300rpx;
  overflow: hidden;
  height: 70rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.userinfo-user {
  position:absolute;
  z-index:40;
  width:750rpx;
  height:100vh;
  background:transparent;
  border:none;
  border-radius:0;
  margin: 0;
}
.roster_play_animation{
  z-index: 3;
  height:70%;
  position: absolute;
  overflow: hidden;
}
.item {
  position: absolute;
  white-space: nowrap;
  /* 防止向下换行*/
  animation-timing-function: linear;
  animation-fill-mode: none;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #434343;
}
@keyframes first {
  from {
      transform: translateX(750rpx);
  }
  to {
      transform: translateX(-1000rpx);
      display: none;
  }
}
.smashingGoldenEggs{
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.smashingGoldenEggs_main{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  /* padding-top: 60rpx; */
}
.smashingGoldenEggs_main_img{
  width: 184rpx;
  height: 240rpx;
  position: relative;
}
.smashingGoldenEggs_main_img .eggImg{
  width: 184rpx;
  height: 240rpx;
}
.smashingGoldenEggs_main_img .hammer{
  width: 100rpx;
  height: 122rpx;
  position: absolute;
  top:0;
  right: 0;
  animation-name: move;
  animation-duration: 3s;
}
@keyframes move {
  /* 开始状态 */
  0% {
    transform: rotate(45deg);
  }
  /* 结束状态 */
  100% {
      transform: rotate(0deg);
  }
}
.twistingEggs{
  width: 486rpx;
  height: 374rpx;
  position: absolute;
  top: 48rpx;
  left: 86rpx;
}
.twistingEggsImage{
  position: absolute;
  top: 582rpx;
  left: 128rpx;
  width: 61rpx;
  height: 61rpx;
}
.twistingEggs .image0{
  width: 102rpx;
  height: 102rpx;
  position: absolute;
  top:245rpx;
  left: 35rpx;
  z-index: 2;
}
.twistingEggs .image1{
  width: 87rpx;
  height: 87rpx;
  position: absolute;
  top: 256rpx;
  left: 285rpx;
  z-index: 2;
}
.twistingEggs .image2{
  width: 84rpx;
  height: 84rpx;
  position: absolute;
  top: 256rpx;
  left: 375rpx;
}
.twistingEggs .image3{
  width: 75rpx;
  height: 75rpx;
  position: absolute;
  top: 256rpx;
  left: 120rpx;
}
.twistingEggs .image4{
  width: 70rpx;
  height: 70rpx;
  position: absolute;
  top: 215rpx;
  left: 258rpx;
}
.twistingEggs .image5{
  width: 99rpx;
  height: 99rpx;
  position: absolute;
  top: 250rpx;
  left: 180rpx;
  z-index: 2;
  
}
.weiyi_0 {
  animation: around1 1.5s linear infinite;
 }
 .weiyi_1 {
  animation: around2 1.5s linear infinite;
 }
 .weiyi_2 {
  animation: around3 1.5s linear infinite;
 } 
 .weiyi_3 {
  animation: around4 1.5s linear infinite;
 }
 .weiyi_4 {
  animation: around5 1.5s linear infinite;
 }
 .weiyi_5 {
  animation: around6 1.5s linear infinite;
 }
 .go{
  animation: around 0.3s linear 1;
 }
 @keyframes around{
  100% {
  -webkit-transform: rotate(-180deg)
  }
 }
  
 @keyframes around1 {
  0% {
  -webkit-transform: translate(0rpx, 0rpx)
  }
  20% {
  -webkit-transform: translate(-16rpx, -200rpx)
  }
  40% {
  -webkit-transform: translate(150rpx, -230rpx)
  }
  60% {
  -webkit-transform: translate(340rpx, -200rpx)
  }
  80% {
  -webkit-transform: translate(180rpx, -50rpx)
  }
  100% {
  -webkit-transform: translate(0, 0)
  }
 }
  
 @keyframes around2 {
  0% {
  -webkit-transform: translate(0rpx, 0rpx)
  }
  20% {
  -webkit-transform: translate(100rpx, -180rpx)
  }
  40% {
  -webkit-transform: translate(-20rpx, -240rpx)
  }
  60% {
  -webkit-transform: translate(-150rpx, -200rpx)
  }
  80% {
  -webkit-transform: translate(-150rpx, -50rpx)
  }
  100% {
  -webkit-transform: translate(0, 0)
  }
 }
  
 @keyframes around3 {
  0% {
  -webkit-transform: translate(0rpx, 0rpx)
  }
  20% {
  -webkit-transform: translate(-180rpx, 10rpx)
  }
  40% {
  -webkit-transform: translate(-240rpx, -110rpx)
  }
  60% {
  -webkit-transform: translate(-100rpx, -240rpx)
  }
  80% {
  -webkit-transform: translate(-50rpx, -130rpx)
  }
  100% {
  -webkit-transform: translate(0, 0)
  }
 }
  
 @keyframes around4 {
  0% {
  -webkit-transform: translate(0rpx, 0rpx)
  }
  20% {
  -webkit-transform: translate(-110rpx, 10rpx)
  }
  40% {
  -webkit-transform: translate(50rpx, -110rpx)
  }
  60% {
  -webkit-transform: translate(-100rpx, -240rpx)
  }
  80% {
  -webkit-transform: translate(50rpx, -130rpx)
  }
  100% {
  -webkit-transform: translate(0, 0)
  }
 }
  
 @keyframes around5 {
  0% {
  -webkit-transform: translate(0rpx, 0rpx)
  }
  20% {
  -webkit-transform: translate(40rpx, 70rpx)
  }
  40% {
  -webkit-transform: translate(50rpx, -210rpx)
  }
  60% {
  -webkit-transform: translate(-80rpx, -100rpx)
  }
  80% {
  -webkit-transform: translate(150rpx, -50rpx)
  }
  100% {
  -webkit-transform: translate(0, 0)
  }
 }
  
 @keyframes around6 {
  0% {
  -webkit-transform: translate(0rpx, 0rpx)
  }
  20% {
  -webkit-transform: translate(-150rpx, -50rpx)
  }
  40% {
  -webkit-transform: translate(130rpx, -140rpx)
  }
  60% {
  -webkit-transform: translate(-110rpx, -250rpx)
  }
  80% {
  -webkit-transform: translate(-130rpx, -20rpx)
  }
  100% {
  -webkit-transform: translate(0, 0)
  }
 }
  
 @keyframes around7 {
  0% {
  -webkit-transform: translate(0rpx, 0rpx)
  }
  20% {
  -webkit-transform: translate(80rpx, -50rpx)
  }
  40% {
  -webkit-transform: translate(-180rpx, -100rpx)
  }
  60% {
  -webkit-transform: translate(50rpx, -150rpx)
  }
  80% {
  -webkit-transform: translate(-180rpx, -20rpx)
  }
  100% {
  -webkit-transform: translate(0, 0)
  }
 }