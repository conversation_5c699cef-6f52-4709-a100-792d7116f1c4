const CryptoJS = require('../../../../utils/CryptoJs.js');
const API = require('../../../../api/request.js');
const app = getApp();
Page({
  data: {
    uploadUrl: '', // 上传地址
    voteConfig: {
      mediaType: ''
    }, // 动态模板
    activityId: '', // 活动id
    shortImgList: [], // 参赛作品
    title: '', // 参赛名称
    tip: '', // 拉票宣言
    page:1,
    radio: '',
    name:'',
    phone:'',
    noSwitch:true,
    shoAddress:false,
    hasMore:false,
    defaultSwitch:false,
    formItem: [],
    addressList:[],
    form:{},
    index:0,
    placeholder:'',
    isYwy:false,
    loadAnimation: false, // 加载中动画关闭
    selectedArry:'',//多选框的值
    selectedCode:'',//多选框的code
    optionArry:[],
    activityPageConfig:''
  },

  onLoad: function (options) {
    console.log(options)
    let that = this
    let nickName = wx.getStorageSync('nickName')
    that.setData({
      activityId: options.activityId,
      tip: `大家好，我是${nickName}，请大家进入活动来为我的精彩作品打call！！！`
    })
    const eventChannel = that.getOpenerEventChannel()
    eventChannel.on('acceptDataFromOpenerPage', function(data) {
      console.log(data)
      that.setData({
        contactCfg: data.data,
        voteConfig: data.voteConfig,
        activityPageConfig:data.activityPageConfig
      })
      // 初始化form
      let attribute =  that.data.contactCfg
      let form = that.data.form
      attribute&&attribute.map((item,index)=>{
        console.log(item);
        form[item.propertyName] = item.defaultValue!=null?item.defaultValue:''
      })
      const activityPageConfig = JSON.parse(that.data.activityPageConfig).find((item) => item.title === '报名信息')
      if(activityPageConfig !== undefined){
        activityPageConfig.componentData.map((item) => {
          if(item.type === 'button'&& (item.btEvent === 'confirm' || item.btEvent === 'cancel')){
            delete item.style.top
            let backgroundImage =  ''
            if(item.bgStatus == 1){
              backgroundImage = `url(${item.imgOptions.url})`
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        console.log('页面组件元素',activityPageConfig.componentData)
        const backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
        that.setData({
          backgroundImg: backgroundImg.propValue.url,
          componentData:activityPageConfig.componentData
        })
      }
      console.log(activityPageConfig)
      that.setData({
        form,
        formItem:attribute
      })
    })
    
  },
  handlerStyle(style) {
    console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width',{
        name:'width',
        type:1
      }],
      ['height', {
        name:'height',
        type:1
      }],
      ['borderRadius', {
        name:'border-radius',
        type:1
      }],
      ['top', {
        name:'top',
        type:1
      }],
      ['left', {
        name:'left',
        type:1
      }],
      ['borderColor', {
        name:'border-color',
        type:2
      }],
      ['fontSize', {
        name:'font-size',
        type:1
      }],
      ['fontWeight', {
        name:'font-weight',
        type:2
      }],
      ['textAlign', {
        name:'text-align',
        type:2
      }],
      ['color', {
        name:'color',
        type:2
      }],
      ['backgroundColor', {
        name:'background-color',
        type:2
      }],
      ['backgroundImage', {
        name:'background-image',
        type:2
      }]
    ])
    for(let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key),style[key])
    }
    return newStyle
  },
  handlerCssStyleStr(keyValueStr,value){
    console.log(keyValueStr)
    if(keyValueStr === undefined) return ''
    if(keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if(keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  onShow: function () {
    var that = this
    that.setData({
      uploadUrl: app.baseUrl+"/upload/inputFileDataFormList"
    })

  },
  // 子传父
  uploadFile (e) {
    console.log('e',e)
    console.log('子传父', e.detail.picsList)
    let that = this
    that.setData({
      shortImgList: e.detail.picsList
    })
  },
  selectAddr(event) {
    console.log(event)
    this.setData({
      radio: event.detail,
    });
  },
  // 取消
  onclose () {
    wx.navigateBack({
      delta: 1,
    })
  },
  // 参赛名称改变
  titleChange (e) {
    console.log('参赛名称改变', e);
    let that = this
    that.setData({
      title: e.detail.value
    })
  },
  // 拉票宣言
  tipChange (e) {
    console.log('拉票宣言改变', e);
  },
  // 拉票宣言失去焦点
  tipBlur (e) {
    console.log('拉票宣言失去焦点', e);
    this.setData({
      tip: e.detail.value
    })
  },
  //输入框失去焦点
  inputBlur(e){
    let value = e.detail.value
    let index = e.target.dataset.key //属性index
    let defaultValue =  e.target.dataset.name
    let formItem = this.data.formItem
    let form = this.data.form
    formItem[index].defaultValue = value //修改默认值
    formItem[index].defaultName = value //修改默认值
    form[defaultValue] = value
    this.setData({
      show: false,
      formItem,
      form
    })
  },
   //提交
   formSubmit: function (e) {
    console.log(e)
    let that = this; 
    that.onBuriedPoint('registeVote', '活动报名')
    let cfgInfo = {'参赛名称': that.data.title, '拉票宣言': that.data.tip}
    let form = that.data.form
    let mediaType = that.data.shortImgList.join(',')
    console.log('cfgInfo', JSON.stringify(cfgInfo))
    console.log('contactInfo', JSON.stringify(form))
    let isVoid = that.data.formItem.filter((i,key)=>{
      return  !i.defaultName
    })//判断是否为空
    let isTrue =  that.data.formItem.filter((item,key)=> {
      let reg = new RegExp(item.rule)
      if(item.rule){
        let valid = reg.test(item.defaultName)
        return !valid
      }
    });
    console.log('isTrue', isTrue)
    console.log('isVoid',isVoid);
    if(isVoid&&isVoid.length!=0){
      wx.showToast({
        icon:'none',
        title: `${isVoid[0].propertyName}为必填信息！`,
      })
      return
    }
    if(isTrue&&isTrue.length!=0){
      wx.showToast({
        icon:'none',
        title: `${isTrue[0].propertyName}不符合校验规则！`,
      })
      return
    }
    if (that.data.title.length == 0) {
      wx.showToast({
        title: '请输入参赛名称',
        icon: 'none',
        duration: 2000
      })
      return false;
    } else if(that.data.tip == 0) {
      wx.showToast({
        title: '请输入拉票宣言',
        icon: 'none',
        duration: 2000
      })
      return false;
    } else if (that.data.shortImgList.length == 0){
      let type = that.data.voteConfig.mediaType == 'IMG' ? '参赛图片' : '参赛视频'
      let title = `请上传${type}`
      wx.showToast({
        title: title,
        icon: 'none',
        duration: 2000
      })
      return false;
    } else {
      let data = {
        activityId: that.data.activityId,
        auditStat: "",
        cfgInfo: JSON.stringify(cfgInfo),
        contactInfo: JSON.stringify(form),
        createTime: "",
        id: '',
        mediaSrc: mediaType,
        ownerId: 0,
        terminal: "API",
        updateTime: "",
        version: 0,
        votes: 0
      }
      console.log('提交参数', data)
      debugger
      API.votePublish(data).then(res => {
        console.log('提交结果', res.data)
        if (res.data.code == 200) {
          wx.showToast({
            icon:'none',
            title: '报名成功！',
          })
          wx.navigateBack({
            delta: 1,
          })
        } else {
          wx.showToast({
            icon:'none',
            title: res.data.message,
          })
        }
      })
    }
  },
   //下拉框
  bindPickerChange(e){
    let that = this
    let index = e.target.dataset.key //属性index
    let key = e.detail.value //选项index
    let defaultValue =  e.target.dataset.name  //字段名
    console.log('携带值为', e,index,key,defaultValue)
    let formItem = that.data.formItem
    let form = that.data.form
    formItem[index].defaultName = formItem[index].attributes[key].enumName //修改默认值
    formItem[index].defaultValue = formItem[index].attributes[key].enumValue //修改默认值
    form[defaultValue] = formItem[index].attributes[key].enumName
    console.log(that.data.formItem,form);
    that.setData({
      show: false,
      formItem,
      form
    })
  },
  mulSelect(e){
    console.log('点击checkbox', e)
    let item = e.target.dataset.item
    let index = e.target.dataset.key //属性index
    let selected = item.defaultValue&&item.defaultValue.split(',') || []
    item.attributes.map((list)=>{
      list['select'] = true
      selected.map((item)=>{
        if(item == list.enumValue){
          list['select'] = false
        }
      })
    })
    this.setData({
      showSelect: true,
      mulIndex:index,
      mulProperty:item.propertyCode,
      mulPropertyName:item.propertyName,
      selectedArry:item.defaultValue,//字段名
      selectedCode:item.defaultName,
      optionArry:item.attributes
    })
  },
  //多选框
  sureData(e){
    console.log('多选框', e)
    let index = this.data.mulIndex //属性index
    let propertyCode =  this.data.mulProperty
    let propertyName =  this.data.mulPropertyName
    console.log(e.detail, propertyCode)
    let selectName = e.detail.value
    let selectCode = e.detail.data
    let formItem = this.data.formItem
    let form = this.data.form
    formItem[index].defaultName = selectName //修改默认值
    formItem[index].defaultValue = selectCode //修改默认值code
    form[propertyName] = selectName
    console.log(this.data.formItem,form);
    this.setData({
      showSelect: false,
      formItem,
      form
    })
  },
  //单选按钮
  onChange(e) {
    let index = e.target.dataset.key //属性index
    let value = e.detail //选项值
    let defaultValue =  e.target.dataset.name  //字段名
    console.log('携带值为',value,defaultValue)
    let formItem = this.data.formItem
    let form = this.data.form
    formItem[index].defaultValue = value //修改默认值
    form[defaultValue] = value
    this.setData({
      formItem,
      form
    });
    console.log(form,formItem);
  },
  //是否设为默认报名人
  changeSwitch({detail }){
    this.setData({
      defaultSwitch:detail,
      noSwitch:false
    })
  },
  //是否使用默认地址
  changeAddress({detail}){
    var that=this
    this.setData({
      defaultAddress:detail,
    })
    if(detail == false){
      that.setData({
        addressId:'',
        province1:'',
        city1:'',
        area1:'',
        address1:'',
        consignee:'',
        receivePhone:''
      })
      return
    }
    let data = {
      current:that.data.page,
      size:10,
      isDefault:1,
    }
    API.getMyAddress(data).then(res => {
      if (res.data.code == 200) {
        let addressList = res.data.data.records
        if(addressList.length !=0){
          // debugger
          that.setData({
            addressId:addressList[0].id,
            province1:addressList[0].province,
            city1:addressList[0].city,
            area1:addressList[0].area,
            address1:addressList[0].address,
            consignee:addressList[0].name,
            receivePhone:addressList[0].phone
          })
        }else{
          wx.showToast({
            title: "当前用户没有设置默认地址，请勾选其他地址！",
            icon: 'none',
            duration: 2000
          })
          return
        }
        console.log(addressList,'地址')
       
      } else {
        wx.showToast({
          title: "网络繁忙，请重试！",
          icon: 'none',
          duration: 2000
        })
      }
    })
  },
  openPop(){
    this.setData({  
      shoAddress:true
    })
    // if(this.data.name==''||this.data.phone == ''){
    //   wx.showToast({
    //     title: "请输入姓名或者手机号进行地址查找！",
    //     icon: 'none',
    //     duration: 2000
    //   })
    // }
    this.defaultaddress(1,this.data.name,this.data.phone)
  },
  // 值
  onClick(event) {
    console.log(event)
    const addressObj = event.currentTarget.dataset;
    this.setData({
      radio: addressObj.id,
      addressObj
    });
  },
  //确定地址弹窗
  closePop(){
    let {addressid:addressId,province1,city1,area1,address1,consignee,receivephone:receivePhone} = this.data.addressObj
    this.setData({
      addressId,
      province1,
      city1,
      area1,
      address1,
      consignee,
      receivePhone,
      shoAddress:false,
      defaultAddress:false
    })
  },
  //取消地址弹窗
  cancel(){
    this.setData({
      shoAddress:false
    })
  },
   // 埋点事件
   onBuriedPoint(name, title){
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let time = new Date().toLocaleString()
    app.sensors.track(name,{
      name: title,
      personScan:nickName,
      scanTime:time,
      userId:userId
    });
  },
  // 选择地址
  adresstop(){
    var type="get"
    console.log(type)
    wx.navigateTo({
      url: '/pages/subpages/myaddress/myaddress?type='+type,
    })
  },
  // 编辑地址
  toedite(e) { 
    var that = this
    var addressid = that.data.addressId
    var type = e.currentTarget.dataset.type

    wx.navigateTo({
      url: '/pages/subpages/addaddress/addaddress?addressid='+addressid+"&type="+type,
    })
  },
//查询所有地址
defaultaddress(page,name,phone){
    var that=this
    const userId = wx.getStorageSync("userId")
    let data = {
      current:page,
      size:10,
      name:name,
      phone:CryptoJS.Encrypt(phone)
    }
    API.getMyAddress(data).then(res => {
      if (res.data.code == 200) {
        console.log('所有地址', res.data.data)
          let addressList = res.data.data.records
          if(addressList.length == 10){
            that.setData({
              hasMore:true
            })
          }
          console.log(addressList,'地址')
          that.setData({
            addressList,
          })
      } else {
        wx.showToast({
          title: "网络繁忙",
          icon: 'none',
          duration: 2000
        })

      }

    })
},
getMoreAddress(){
  let that = this
  if(!that.data.hasMore){
    return
  }
  let page = this.data.page+1
  let data = {
    current:page,
    size:10,
  }
  API.getMyAddress(data)
    .then(res => {
      if (res.data.code == 200) {
          console.log(res.data)
          if(res.data.data.records.length < 10 || res.data.data.records.length == 0){
            that.setData({
              hasMore:false,
              page
            })
          }else{
            that.setData({
              hasMore:true,
              page
            })
          }
          if(res.data.data.records.length == 0){
            return
          }
          let list = that.data.addressList
          let newList = list.concat(res.data.data.records)
          that.setData({
            addressList: newList,
          })
      } else {
        wx.showToast({
          title: "网络繁忙",
          icon: 'none',
          duration: 2000
        })
      }
    })
},

})