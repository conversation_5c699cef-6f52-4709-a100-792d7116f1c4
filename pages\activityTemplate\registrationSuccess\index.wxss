 .successImg{
   display: block;
   width:130rpx;
   height: 112rpx;
   border-radius: 50%;
   margin: 88rpx auto 36rpx;
 }
 .qrCodeImg{
  display: block;
  width: 450rpx;
  height: 450rpx;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  border: 6rpx solid #E0F2FF;
  padding: 38rpx;
  box-sizing: border-box;
  margin: 0 auto;
  margin-top:30rpx;
}
.qrCodeImg image{
  width: 376rpx;
  height: 376rpx;
}
 .tip{
   font-size: 36rpx;
   font-family: 'PingFang SC-Medium';
   color: #17204D;
   margin-bottom: 24rpx;
   text-align: center;
 }
 .modal-mask {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.7;
  overflow: hidden;
  z-index: 999;
  color: #fff;

}
.choujiang_modal{
  width: 590rpx;
  /* height: 600rpx; */
  position: fixed;
  top: 22%;
  z-index: 99999;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center
}
.choujiang_modal1{
  width: 100%;
  border-radius: 16rpx;
  background-color: #fff;
  position: relative;

}
.choujiang_modal_bottom{
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  text-align: center;
  margin-top: 44rpx
}
.choujiang_modal_closeImg{
width: 100%;
height: 100%;
}
.choujiang_modal1 .success{
    top: -66rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 132rpx;
    height: 132rpx;
    background-color: #41cc8b;
    text-align: center;
    position: relative;
    border-radius: 50%;
}
.success van-icon{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.success_title{
    margin: -28rpx 0 16rpx;
  font-size: 36rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #12172E;
  line-height: 50rpx;
  text-align: center;
}
.label_items{
  margin: 0 0 40rpx 40rpx;
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 52rpx;
}
.label_items text{
  display: inline-block;
  width: 150rpx;
  color: #666666;
}
.desc{
font-size: 28rpx;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #333333;
line-height: 46rpx;
/* text-align: center; */
padding: 0 40rpx;
}
.line{
  /* width: 100%; */
  height: 2rpx;
  background: #F5F5F5;
  margin: 40rpx 40rpx 0;
}
.footer{
    text-align: center;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 40rpx;
    text-align: center;
    margin-top: 40rpx;
}
.share_btn{
   display: flex;
   justify-content: center;
}
.share_btn .btn{
  width: 510rpx;
  height: 88rpx;
  background: linear-gradient(90deg, #F82420 0%, #F82420 0%, #FE6244 100%);
  border-radius: 16rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
  margin: 20rpx 0 40rpx;
}
.oprationBtn{
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  width: calc(100%-0rpx);
  padding: 16rpx 32rpx 84rpx;
  box-shadow: 0px -2px 10px #ccc;
 
}
.oprationBtn view{
  width: 328rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  font-size: 32rpx;
  font-family: 'PingFang SC-Bold';
  color: #025CEA ;
  border-radius: 48rpx;
  border: 1px solid #025CEA;
  box-sizing: border-box;
}
.oprationBtn .myOrder{
  border: none;
  margin-left: 30rpx;
  background: linear-gradient(to right,#025CEA,#4492FC);
  color: #fff;
}
.title{
  font-size: 28rpx;
  color: #7E849F;
  font-family: 'PingFang SC-Medium';
  text-align: center;
}
.arrow{
  width: 50rpx;
  height: 40rpx;
  color: #7E849F;
  margin: 52rpx auto 40rpx;
  transform:rotate(90deg);
-ms-transform:rotate(90deg); /* Internet Explorer */
-moz-transform:rotate(90deg); /* Firefox */
-webkit-transform:rotate(90deg); /* Safari 和 Chrome */
-o-transform:rotate(90deg); /* Opera */
}
.activityTheme{
  display: flex;
  width: 686rpx;
  height: 160rpx;
  border-radius: 16rpx;
  box-shadow: 1px 1px 4px rgba(204, 204, 204, 0.363);
  padding: 24rpx 30rpx 32rpx;
  box-sizing: border-box;
  align-items: center;
  
}
.activityTheme image{
  width: 100rpx;
  height: 100rpx;
  margin-right: 24rpx;
}
.receive{
  /* margin-right: 146rpx; */
  width: 400rpx;
}
.receive view{
 font-size:32rpx ;
 color: #17204D;
 font-family: 'PingFang SC-Medium';
 margin-bottom: 16rpx;
}
.receive text{
  font-size: 24rpx;
  color: #7E849F;
}
.get{
  width: 160rpx;
  height: 52rpx;
  line-height: 52rpx;
  background: linear-gradient(to right,#025CEA,#4492FC);
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  border-radius: 26rpx;
}
.lockOrder{
  font-size: 28rpx;
  color: #7E849F ;
  margin-top: 48rpx;
  line-height: 40rpx;
  padding: 0 32rpx;
  font-family: 'PingFang SC-Medium';
}
.kegeTip{
  margin-top: 32rpx;
  font-size: 28rpx;
  color: #7E849F;
  line-height: 44rpx;
  text-align: center;
  font-family: 'PingFang SC-Medium';
}
.downArrow{
  width: 60rpx;
  height: 60rpx;
  margin: 30rpx auto 0;
}
.blueColor{
 color: #025CEA;
}