.fade-in {
  animation: fadeIn 1s;
  animation-fill-mode: forwards;
}
@keyframes fadeIn {
  0% {
    opacity: .5;
  }
  100% {
    opacity: 1;
  }
}

.fade-out {
  animation: fadeOut 1s;
  animation-fill-mode: forwards;
}
@keyframes fadeOut {
  from {
    opacity: .5;
  }
  to {
    opacity: 0;
  }
}

.dialog-container{
  position: relative;
  height: 100%;
  width: 622rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 40rpx;
  box-sizing: border-box;
}
.dialog-container .img-icon{
  position: absolute;
  height: 172rpx;
  width: 172rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  top: -40rpx;
  left: 0;
  right: 0;
  margin: 0 auto;
}
.dialog-container .sign-success-text{
  font-size: 60rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  color: #A95C0B;
  position: relative;
  top: 216rpx;
  text-align: center;
}
.dialog-container .sign-prize-img{
  width: 128rpx;
  height: 128rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 16rpx;
  border: 2rpx solid #EFE1B4;
  overflow: hidden;
  position: absolute;
  top: 326rpx;
  left: 32rpx;
}
.dialog-container .sign-prize-title{
  position: absolute;
  top: 320rpx;
  left: 184rpx;
  font-size: 40rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: 400;
  color: #555C80;
}
.dialog-container .sign-prize-desc{
  position: absolute;
  top: 384rpx;
  left: 184rpx;
  width: 406rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 400;
  color: #7E849F;
  line-height: 40rpx;
}
.dialog-container .sign-prize-btn{
  position: absolute;
  top: 502rpx;
  left: 32rpx;
  width: 558rpx;
  height: 96rpx;
  /* background: linear-gradient(85deg, #FD1405 0%, #FF9734 100%); */
  border-radius: 936rpx 936rpx 936rpx 936rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 32rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 36rpx;
  letter-spacing: 2px;
}

.repair-container{
  height: 548rpx;
}
.repair-container .img-icon{
  background-image: url("https://txqmp.cpic.com.cn/uploads/img/signInCardReplacementImg.png");
}
.repair-container .repair-title{
  position: absolute;
  top: 216rpx;
  width: 100%;
  font-size: 60rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  color: #A95C0B;
  text-align: center;
}
.repair-container .repair-desc{
  position: absolute;
  top: 304rpx;
  width: 100%;
  font-size: 32rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  color: rgba(169,92,11,0.8);
  line-height: 38rpx;
  text-align: center;
}

.repair-container .repair-btn{
  position: absolute;
  top: 404rpx;
  border-radius: 936rpx 936rpx 936rpx 936rpx;
  width: 264rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  
  letter-spacing: 2px;
}
.repair-left-btn{
  left: 32rpx;
  color: #FD270B;
  border: 2rpx solid #FE300F;
}
.repair-right-btn{
  left: 326rpx;
  color: #FD270B;
  /* background: linear-gradient(85deg, #FD1405 0%, #FF9734 100%); */
  color: #FFFFFF;
}

.awarding-dialog-container{
  height: 572rpx;
}
.awarding-dialog-container .img-icon{
  width: 200rpx;
  height: 200rpx;
  background: #FFFDF5;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}
.awarding-dialog-container .awarding-prize-desc{
  position: absolute;
  top: 304rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  color: rgba(169,92,11,0.8);
  line-height: 48rpx;
  text-align: center;
  width: 416rpx;
  padding: 0 104rpx;
}
.awarding-dialog-container .sign-prize-btn{
  position: absolute;
  top: 428rpx;
}

.mask{
  position:absolute;
  z-index:50;
  width:750rpx;
  height:100vh;
  background-color:rgba(131, 120, 120, 0.3);
  border:none;
  border-radius:0;
  margin: 0;
  top: 0;
  left: 0;
}

.modalDlg{
  width:80%;
  height: 300rpx;
  position: fixed;
  top: 60%;
  left: -1%;
  z-index: 1000;
  margin: -370rpx 85rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: space-between; */
  overflow: hidden;

}
.modalDlg>text{
font-size:30rpx;
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
height:180rpx;
width:100%;
font-weight: bold;
}
.modalDlg ::-webkit-validation-bubble-text-block{
  height:100rpx;
  margin:0 40rpx;
}
.modalDlg>view{
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
}

.modalDlg>view>button{
width:500rpx;
height:80rpx;
font-size: 30rpx;
border-radius: 40rpx;
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
/* border-top: 1rpx solid #CCC; */
border: 1rpx solid#FF564AFF;
background-color:#FF564AFF;
margin-bottom: 20rpx;
color: #fff;
}

.mymask {
  position:fixed;
  z-index:49;
  width:750rpx;
  height:100%;
  /* height: calc( 100% - 80rpx ); */
  /* background:rgb(233, 220, 220); */
  background-color:rgba(131, 120, 120, 0.4);
  /* background:transparent; */
  border:none;
  border-radius:0;
}
.mask_content{
  margin-top: 15vh;
}
.reflash_img{
  width: 590rpx;
  height: 880rpx;
  display: block;
  margin: 0 auto;
}
.reflash_text_wrap{
  width: 100%;
  position: absolute;
  top: 20%;
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 46rpx;
  text-align: center;
}
.reflash_btn_wrap{
  width: 100%;
  height: 88rpx;
  position: absolute;
  bottom: 20%;
  text-align: center;
}
.reflash_btn{
  width: 512rpx;
  height: 100%;
  background: #FFAE54;
  border-radius: 16px;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
}
.mask2{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000000;
  opacity: 0.6;
  /* background: #000; */
  z-index: 48;
  /* opacity: 0.7; */
}


.laoding_wrap{
    width: 100%;
    height: 100vh;
    background: #F5F5F5;
    /* position: relative; */
    text-align: center;
    padding-top: 164rpx;
}
.loading_img{
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 28rpx;
  /* position: absolute;
  left: 50%;
  transform: translateX(-50%); */
}
.loading_text{
  
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #676B81;
  line-height: 46rpx;
}
.loading_nav_wrap{
    background:#fff;
    position: fixed;
    width: 100%;
    top: 0;
    /* background: #fff; */
    /* opacity: 0.7; */
    color: #fff;
    z-index: 9999;
    height: 45px;
    left: 0;
}
.loading_nav_content{
  width: 100%;
  position: absolute;
  left: 0;
  /* top: 51rpx; */
  bottom: 9px;
  font-size: 37rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 45rpx;
  text-align: center;
}



.equity-home-nav {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 10;
}
.equity-home-nav .home_nav_menu {
  position: absolute;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.equity-home-nav .home_nav_menu .home_nav_menu_icon {
  position: absolute;
  left: 0;
}

.signin-wrap{
  height: 2258rpx;
  /* background-color: burlywood; */
  /* padding: 32rpx 32rpx; */
}

.page_main {
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 100%;
}

.signin-btn{
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: PingFang SC-Bold, PingFang SC;
  position: absolute;
}
.awarding-btn{
  box-sizing: border-box;
  padding-top: 62rpx;
  font-family: PingFang SC-Medium, PingFang SC;
}
.signin-btn-sign{
  justify-content: flex-start;
  box-sizing: border-box;
  padding-left: 48rpx;
  letter-spacing: 4rpx;
}
.rule-btn{
  position: fixed;
  writing-mode: vertical-rl;
}
.repair-num{
  position: absolute;
  line-height: 40rpx;
  font-family: PingFang SC-Medium, PingFang SC;
}

.calendar-wrap{
  position: absolute;
  left: 0;
  top: 816rpx;
  /* padding: 0 32rpx; */
  /* box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(230,140,19,0.3); */
  border-radius: 24rpx 24rpx 0 0;
}

.calendar-bottom-tip{
  position: absolute;
  left: 32rpx;
  top: 816rpx;
  height: 74rpx;
  width: 686rpx;
  background-color: #fff;
	/* border-top: 2rpx solid rgba(169,92,11,0.2); */
	line-height: 72rpx;
	color: #A95C0B;
	font-size: 24rpx;
	padding: 0 32rpx;
  box-sizing: border-box;
  border-radius: 0 0 24rpx 24rpx;
}

.calendar-bottom-tip-day{
	color: #FD1D08;
}

.prize-table-wrap{
  position: absolute;
  left: 0;
  top: 1598rpx;
  margin: 0 32rpx;
  width: 686rpx;
  height: 550rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(230,140,19,0.3);
  border-radius: 24rpx;
}

.prize-img{
  width: 320rpx;
  height: 60rpx;
  margin: 0 auto;
  text-align: center;
  background-image: url("https://txqmp.cpic.com.cn/uploads/img/signInPrizeImg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  
  font-size: 36rpx;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 60rpx;
  letter-spacing: 2px;

}

.audit-space{
  height: 128rpx;
}