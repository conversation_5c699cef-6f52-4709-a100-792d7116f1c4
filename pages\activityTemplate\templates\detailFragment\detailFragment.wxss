.activity_detail_content{
  position: absolute;
  background-color: #fff;
  padding-bottom: 32rpx;
  border-radius: 16rpx;
}
.activity_detail_contents{
  background-color: #fff;
  padding:32rpx;
  box-sizing: border-box;
  padding-bottom: 32rpx;
  border-radius: 16rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.jj{
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32rpx;
}
.jj view image{
  width: 24rpx;
  height: 22rpx;
}
.jj view:nth-child(2){
  margin: 0 12rpx;
  font-weight: 400;
  font-size: 32rpx;
  font-family: PingFang SC-Bold, PingFang SC;
}
.activity_detail_timeOptions {
  padding: 20rpx 0;
}
.activity_detail_timeOptions_item {
  display: flex;
  align-items: center;
  justify-content: center;
}
.activity_detail_timeOptions_item_time {
  font-size: 26rpx;
  padding-bottom: 10rpx;
}
.activity_detail_timeOptions_item_text {
  font-size: 24rpx;
  text-align: center;
}
.activity_detail_editor{
  width: calc(100% - 64rpx);
  padding: 0rpx 32rpx 0;
}
.goods_list{
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 574rpx;
  /* height: 420rpx; */
  margin:0rpx auto;
}
.ql-editor{
  padding: 0;
  height: auto;
}
#myVideo{
  width: 574rpx;
  height: 420rpx;
  margin: 0rpx auto 0;
  display: flex;
  justify-content: center;
  align-items: center;
}