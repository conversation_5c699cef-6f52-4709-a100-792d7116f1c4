<view class="mask" hidden="{{loadAnimation==false}}">
  <view class="spinner">
    <view class="bounce1"></view>
    <view class="bounce2"></view>
    <view class="bounce3"></view>
  </view>
</view>
<!-- <form bindsubmit="formSubmit"> -->
<view class="contentBg" style="background-image:url({{backgroundImg}})">
  <view class="content" >
  <view class="topTitle"><span></span>报名详情</view>
    <van-cell title="作品名称" class="inputCell" border="{{false}}">
      <textarea value='{{title}}' name="title" placeholder="请输入" class="inputCellBox" auto-height bindinput="titleChange"></textarea>
    </van-cell>
    <van-cell title="拉票宣言" class="inputCell" border="{{false}}">
      <textarea value='{{tip}}' name="tip" class="inputCellBox1 inputTextArea"
      bindinput="tipChange"  bindblur="tipBlur"></textarea>
    </van-cell>
    <van-cell title="{{voteConfig.mediaType == 'IMG' ? '作品图片' : '作品视频'}}" border="{{false}}" class="uploadCell">
        <com-uploader bind:uploadFile="uploadFile" accept="{{voteConfig.mediaType == 'IMG' ? 'image' : 'video'}}" maxSize="1048576" count="{{voteConfig.mediaType == 'VIDEO' ? '1' : voteConfig.mediaLimit}}" uploadUrl="{{uploadUrl}}"></com-uploader>
    </van-cell>
  </view>
  <view class="content contentBottom">
    <view class="topTitle"><span></span>联系信息<text class="titleTip">（信息不会公开）</text></view>
    <block  wx:for="{{formItem}}" wx:key="id">
      <block wx:if="{{item.type == 'input'}}">
        <van-cell title="{{item.propertyName}}" class="inputCell">
          <input type="{{item.propertyType}}" placeholder='{{item.prompt}}'  data-name="{{item.propertyName}}" value="{{item.defaultValue}}" data-key="{{index}}" bindblur="inputBlur" name="{{item.propertyCode}}" placeholder-style="color:#999999;" class="inputCellBox"/>
        </van-cell>
      </block>
      <block  wx:if="{{item.type == 'radio'}}">
        <van-cell title="{{item.propertyName}}" required="{{item.required == 1}}">
          <van-radio-group value="{{item.defaultValue}}" bindchange="onChange"  data-key="{{index}}" name="{{item.propertyCode}}"  data-name="{{item.propertyCode}}" style="display:flex;justify-content: flex-end;">
            <block wx:for-items="{{item.value}}"   wx:for-item="radios"  wx:key="enumValue">
              <van-radio name="{{radios.enumValue}}" >{{radios.enumName}}</van-radio>
            </block>
          </van-radio-group>
        </van-cell>
      </block>
      <block  wx:if="{{item.type == 'select'}}">
        <van-cell title="{{item.propertyName}}"  required="{{item.required == 1}}" is-link>
          <picker bindchange="bindPickerChange" value="{{index}}" range="{{item.attributes}}"  wx:for-item="values" range-key="enumName" data-name="{{item.propertyName}}" data-key="{{index}}">
            <input class="picker" type="text" placeholder="{{item.prompt}}" value="{{item.defaultName}}" name="{{item.propertyCode}}" data-key="{{index}}" disabled />
          </picker>
        </van-cell>
      </block>
      <block  wx:if="{{item.type == 'checkbox'}}">
        <van-cell title="{{item.propertyName}}" is-link>
          <text bindtap="mulSelect" wx:if="{{item.defaultName}}" data-item="{{item}}" data-key="{{index}}" name="{{item.propertyCode}}">{{item.defaultName}}</text>
          <text bindtap="mulSelect" wx:else data-item="{{item}}" data-key="{{index}}" class="placeHolderText">请选择</text>
        </van-cell>
      </block>
    </block>
  </view>
  <block wx:for="{{componentData}}" wx:key="index">
    <view class="footerBox" wx:if="{{item.type === 'button'}}" style="{{item.styleStr}}">
      <view wx:if="{{item.btEvent === 'cancel'}}" class='btn0' bindtap="onclose">{{item.propValue}}</view>
      <view wx:if="{{item.btEvent === 'confirm'}}" class='btn0 btn0Bg' bindtap="formSubmit">{{item.propValue}}</view>
    </view>
  </block>
</view>
     
<!-- </form> -->
  <!-- 多选弹窗 -->
  <multi-select show="{{showSelect}}" bind:multipleData="sureData" selectedCode="{{selectedCode}}" selected="{{selectedArry}}" options="{{optionArry}}"></multi-select>
  <!-- 地址弹窗 -->
  <van-popup
  show="{{ shoAddress }}"
  round
  position="bottom"
  close-icon-color="#000"
  custom-style="height: 70%"
  
>
  <!-- <view class="titleAddr">请选择收货地址</view> -->
  <view class="topBtn">
    <view bindtap="cancel">取消</view>
    <view class="surePop" bindtap="closePop">确认</view>
  </view>
  <view style="height:80rpx"></view>
  <van-radio-group value="{{ radio }}" bind:change="selectAddr">
    <van-cell-group>
      <van-cell wx:for="{{addressList}}" wx:key="id" clickable  data-addressId="{{item.id}}" data-consignee="{{item.name}}" data-receivePhone="{{item.phone}}" data-province1="{{item.province}}" data-city1="{{item.city}}" data-area1="{{item.area}}" data-address1="{{item.address}}" bindtap="onClick">
        <view slot="title">
          <view class="van-cell-text"><view class="nameAddr">{{item.name}}</view> <view class="phoneNum">手机号: {{item.phone}}</view></view>
          <view style="color:#999">地址：{{item.province+item.city+item.area+item.address}}</view>
        </view>
        <van-radio checked-color="#FF564AFF" icon-size="48rpx" slot="right-icon" name="{{item.id}}" />
      </van-cell>
    </van-cell-group>
  </van-radio-group>
  <view wx:if="{{addressList.length == 0}}" style="text-align: center;margin: 40rpx;">当前客户还没有历史地址</view>
  <view class="bottomText">
    <text wx:if="{{hasMore}}" bindtap="getMoreAddress">点击加载更多...</text>
    <text wx:else>没有更多数据了~</text>
  </view>
</van-popup>
<van-dialog id="van-dialog" />