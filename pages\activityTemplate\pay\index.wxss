page{
  background: #f2f3f5;
}
.content{
  font-family:PingFang SC-Medium;
  font-size: 28rpx;
  color: #fff;
}
.content--back{
  height: 556rpx;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.nav_menu{
  color:#17204D;
}
.status{
  font-size: 36rpx;
  font-family: PingFang SC-Bold;
  margin-bottom: 24rpx;
}
.orderInfo{
  margin-top: 32rpx;
  background: #fff;
  border-radius:16rpx ;
  padding: 48rpx 32rpx 24rpx;
  color: #555C80;
}
.title{
  color: #17204D;
  font-size: 32rpx;
  font-family: PingFang SC-Bold;
  margin-bottom: 24rpx;
}
.info_item{
  display: flex;
  justify-content: space-between;
  height: 88rpx;
  line-height: 88rpx;
  border-bottom: 1px solid #EBEDF0 ;
}
.item_value{
  color: #17204D;

}
.no_border{
  border-bottom: none;
}
.footer{
  position:fixed;
  bottom:0;
  display:flex;
  height:128rpx;
  background-color:#ffffff;
  border-top:1rpx #efefef solid;
  width:calc(100% - 64rpx); 
  align-items: center;
  padding:0 32rpx 84rpx;
  /* box-sizing: border-box; */
  justify-content: space-between;
}
.appointment{
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  border-radius: 50rpx;
  background: linear-gradient(to right,#76A3FD,#527AF9 );
  color: #fff;
  font-size: 32rpx;
  
}