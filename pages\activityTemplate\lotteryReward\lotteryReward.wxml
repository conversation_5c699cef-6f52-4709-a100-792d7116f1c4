<view class="activityFixed">
  <view class="reward" style="height: {{scrollHeight}}px;">
    <scroll-view scroll-y="true" class="myActivity_scroll" >
      <view class="reward_list">
        <block wx:if="{{prizeRecordList.length === 0}}">
          <view class="reward_no">
            <image src="https://txqmp.cpic.com.cn/uploads/img/noneActivity.png" />
            <view>啊哦~您还没有中奖奖品 赶快去参与活动吧~</view>
          </view>
        </block>
        <block wx:if="{{prizeRecordList.length !== 0}}">
          <view class="reward_item" wx:for="{{prizeRecordList}}" wx:key="index">
          <block wx:if="{{item.resultType == 1}}">
            <view class="reward_item_left">
              <image src="{{item.url}}"></image>
              <view class="reward_item_title">
                <view>{{item.prizeName}}</view>
                <view>{{item.createTime || ''}}</view>
              </view>
            </view>
            <view class="reward_btn {{item.receiverStatus === 1?'noBackground':''}}" bindtap="showDialog" data-index="{{index}}" data-item="{{item}}">{{item.receiverStatus === 0 ? '去领取' : '已领取'}}</view>
          </block>
          </view>
        </block>
        
      </view>
    </scroll-view>
    <view class="content_bottom" wx:if="{{prizeRecordList.length && prizeRecordList.length >= total}}">
      到底了哦
    </view>
  </view>
</view>
<dialog-box id='dialog'  dialogData="{{dialogData}}" dialogImg="{{dialogImg}}" acceptDialogData="{{acceptDialogData}}" bind:cancelEvent="_cancelEvent" activityTemplateType="{{activityTemplateType}}" mode="{{drawMode}}" bind:saveEvent="_saveEvent" attributes="{{attributes}}" activityId="{{recordId}}"
  bind:confirmEvent="_confirmEvent" isInfo="{{false}}"  prizeObj='{{prizeObj}}' receiveType="{{receiveType}}" isFirst="{{isFirst}}" getUrlRes="{{getUrlRes}}"></dialog-box>

<!-- 领奖信息弹窗 -->
<van-popup show="{{isInfo}}" closeable="{{false}}" close-on-click-overlay="{{true}}" round overlay-style="z-index: 1" bind:close="onClose"  custom-style="background-color: {{acceptAwardImg&&acceptAwardImg.propValue ? 'transparent' : '#fff'}}" >
  <edit-infor activityTemplateType="{{activityTemplateType}}" acceptAwardImg="{{acceptAwardImg}}" acceptAwardBtn="{{acceptAwardBtn}}" dialogType="receiveInfo" attributes="{{attributes}}" recordId="{{recordId}}" topTitle="领奖信息" bindsaveSuccess="_saveEvent" msg="{{msg}}"></edit-infor>
</van-popup>
<!-- 领奖信息弹窗 -->