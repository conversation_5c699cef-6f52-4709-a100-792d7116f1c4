const wxtimer = require('../../../../utils/time.js');
const http = require('../../../../utils/util.js');
const app = getApp();
const API = require('../../../../api/request.js');
const CryptoJS = require('../../../../utils/CryptoJs.js');
const { showTab, handleUserStorage } = require('../../../../utils/aboutLogin')
const { setSalesCode } = require('../../../../utils/querySalesCode');
const { identity, identityEnum } = require('../../../../utils/phone-idcard')
import HTAPI from '../../../../api/haitong'
let totalTime = 5
let clock = null
Page({
  data: {
    isShowClose:true,
    isShowCancel:true,
    isShow:true,
    isShare:0,
    pageUrl:'pages/activityTemplate/group/home/<USER>',
    audit: 0,
    showReject: false,
    rejectReason: '',
    codeUrl: '',
    peopleNumber: 3,
    showSwitchSale: false,
    shareGroup: false,
    isAttentionAgainFlag: false,
    btnText: {
      text: '',
      cssStr: ''
    },
    timeObj: {},
    disagree: false,
    groupList: [],

    showPoster: false,
    warnInfo: '', // 提醒我/已预约
    disagree: false,
    isClose: true,
    personLogin: false,
    checkDetail:0,
    InfoShow: false,
    reflashToast: false,
    isReflash: false,
    userInfoSuccess: false,
    detailSucess: false,
    currentIndex: 1,
    VSwiperImgList: [],
    chooseArea: false,
    trueName: '',
    idCard: '',
    currentId: '',//当前业务员姓名+工号
    acceptId: '',//换绑后业务员姓名➕工号
    identityType: identityEnum,
    identity: '身份证',
    identityIndex: 0,
    cardType: 'ID_CARD',
    financialManagerData: '',
    financialManagerShareData: '',
    distributionChannel: '',
    message: '',
    isHt: false,
    isShowOverlay: false,
  },
  onLoad (options) {
    console.log(options, '===========');
    // debugger
    let scene = options.scene
    // console.log(options.scene, decodeURIComponent(options.scene).split(","))
    let that = this
    let userId = wx.getStorageSync('userId')
    let userType = wx.getStorageSync('userType')
    let groupNo = options.groupNo || ''
    let saleName = options.saleName || ''
    let salesCode = options.salesCode || ''
    let nickName = options.nickName || ''
    let avatarUrl = options.avatarUrl || ''
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      var id = option[0]
      that.setData({
        heightTop: app.globalData.height,
        ids: id,
        userType: userType,
        userId,
        scene: scene
      })

    } else {
      var id = options.id
      that.setData({
        heightTop: app.globalData.height,
        ids: id,
        audit: options.audit*1||0,
        checkDetail: options.checkDetail*1||0,
        isShare: options.isShare || 0,
        shareUserId: options.shareUserId || '',
        userType: userType,
        userId,
        shareUserType: options.userType || '',
        groupNo,
        changeSalesCode: salesCode,
        changeSaleName: saleName,
        nickName,
        shareHeadImg: avatarUrl,
        financialManagerShareData: {
          financialManagerNo: options.financialManagerNo,
          financialManager: options.financialManager
        }
      })
    }

    if(options.refer == 'kege'){
      this.setData({
        refer: options.refer,
        kegeName: options.name,
        kegePhone: options.phone,
        kegeCardType: options.cardType,
        kegeCardNo: options.cardNo,
      })
    }

  },
  async createKegeUser(){
    if(!this.data.kegePhone) return false;
    let data = {
      phone: this.data.kegePhone,
      name: this.data.kegeName,
      idcard: this.data.kegeCardNo,
      fromKjgx: 1,
      defaultFlag: 0,
      cardType: this.data.kegeCardType

    }
    const res = await API.saveApplyer({encryptData:CryptoJS.Encrypt3(JSON.stringify(data))})
    if(res.data.code == 200){
      wx.setStorageSync('selectId', [res.data.data.id + ""]) //保存勾选的id
      let name = CryptoJS.Decrypt(res.data.data.name)
      let phone = CryptoJS.Decrypt(res.data.data.phone)
      let idcard = res.data.data.idcard ? CryptoJS.Decrypt(res.data.data.idcard) : ""
      res.data.data.name = name
      res.data.data.phone = phone
      res.data.data.idcard = idcard
      res.data.data.cardType = res.data.data.cardType
      res.data.data.otherInfo = {
        "name": name,
        "cardType": res.data.data.cardType,
        "cardNo": idcard,
        "phone": phone
      }
      wx.setStorageSync('selectPeople',JSON.parse(JSON.stringify([res.data.data]))) //保存勾选的用户信息
    }
  },
  async onShow () {
    let that = this
    if (!http.userAre()) {
      // 用户id判断用户是否授权
      that.setData({
        InfoShow: true
      })
    } else {
      that.changeAuth()
      // if (wx.getStorageSync('refreshUserInfo')) {
      //   that.getUserInfo();
      //   console.log('获取新用户信息');
      // } else {
      //   console.log('获取缓存用户信息');
      //   if (wx.getStorageSync('salesCode') || wx.getStorageSync('pcode')) {
      //     const cardType = wx.getStorageSync('cardType')
      //     const identityValue = that.data.identityType.filter((item) => item.value === cardType)
      //     console.log(identityValue)
      //     that.setData({
      //       type: wx.getStorageSync('userType'),
      //       salesCode: wx.getStorageSync('salesCode'),
      //       cardType: wx.getStorageSync('cardType'),
      //       identity: identityValue[0]?.name || '',
      //       idCard: wx.getStorageSync('idCard') ? wx.getStorageSync('idCard') : null,
      //       userType: wx.getStorageSync('userType'),
      //       credits: wx.getStorageSync('credits'),
      //       userInfoSuccess: true
      //     })
      //   }
      //   that.getIsRegister() // 判断是否登录 
      //   const flag = await that.getIsRegister()
      // }
    }


  },

  handleTime (m_seconds) {
    function addEge (a) {
      return a < 10 ? a = "0" + a : a = a
    }

    let seconds = Math.floor(m_seconds / 1);
    let day = Math.floor(seconds / (3600 * 24));
    let hour = Math.floor((seconds - (day * 86400)) / 3600);
    let points = Math.floor((seconds - (hour * 3600 + day * 86400)) / 60)
    let s = Math.floor(seconds % 60);
    return addEge(hour) + ":" + addEge(points) + ":" + addEge(s) 
  },

  //活动详情
  async getDetail (id) {
    this.setData({
      myLoadAnimation: true,
    })
    let that = this
    let data = {
      id: that.data.ids
    }
    let res = null
    if (this.data.audit) {
      res = await API.auditDetail(data)
      this.setData({
        isShow:false
      })
    } else {
      if(this.data.checkDetail == 1){
        data.containsDeleted = 1
      }
      res = await API.getActivityDetail(data)
    }
    console.log('详情数据：', res)
    return new Promise((resolve, reject) => {
      if (res.header['Content-Type'] === 'text/html' && !that.data.reflashToast) {
        console.log('响应头是text/html')
        that.setData({
          myLoadAnimation: false,
          detailSucess: false
        })
        this.handleReflashToast()
        resolve(false)
        console.log(that.data.detailSucess)
      } else {
        if (res.data.code == 200) {
          this.getHyacinthPerm()
          resolve(true)
          let response = res.data.data
          const distributionChannel = response.distributionChannel
          this.setData({ distributionChannel })
          if (distributionChannel === 'HTZQ') this.getHyacinthPerm()
          that.setData({
            validTime: response.activityConfig ? response.activityConfig.validTime : 0
          })
          let groupList = res.data.data.groupList || []
          if (groupList.length == 0) {
            that.setData({
              needPeople: 0,
              sub_time: -1,
              groupTime: {
                day: '00',
                hour: '00',
                points: '00',
                seconds: '00'
              }
            })
            // console.log('@@@@@groupList为空')
          } else {
            groupList.map((item, key) => {
              let nowtime = new Date().getTime() //目前时间
              let endTime = new Date(response.applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒
              let groupEndTime = new Date(item.createTime.replace(/-/g, '/')).getTime() + that.data.validTime * 3600 * 1000// 活动结束时间时间戳毫秒
              let sub_time = parseInt((groupEndTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 已结束
              if (endTime < groupEndTime) {
                sub_time = parseInt((endTime - nowtime) / 1000)
              }
              item._lastTime = sub_time
              // let timers = new wxtimer({
              //   complete: function () {
              //     if (sub_time < 0) {
              //       clearInterval(timers)
              //     }else{
              //     }
              //   }
              // });
              //  计时器
              // timers.start(that, sub_time, false,'groupList',key);
              item['createTimes'] = item.createTime.slice(5, 16).replace(/-/g, '/')
            })
            this.timer = setInterval(() => {
              for (let index = 0; index < groupList.length; index++) {
                const sub_time = groupList[index]._lastTime 
                let text = '已结束'
                if(sub_time > 0) text = this.handleTime(sub_time)
                this.setData({ [`groupList[${index}]._lastTime`]: sub_time - 1 })
                this.setData({ [`groupList[${index}]._text`]: text })
              }
              const flag = groupList.every(item => item._lastTime < 0)
              if (flag) clearInterval(this.timer)
            }, 1000)

            const groupListFilter = groupList.filter((item) => that.data.isShare == 2 && that.data.groupNo == item.groupNo)
            console.log(groupListFilter)
            if (groupListFilter.length !== 0) {
              let nowtime = new Date().getTime() //目前时间
              let endTime = new Date(response.applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒
              let groupEndTime = new Date(groupListFilter[0].createTime.replace(/-/g, '/')).getTime() + that.data.validTime * 3600 * 1000// 活动结束时间时间戳毫秒
              let sub_time = parseInt((groupEndTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 已结束
              if (endTime < groupEndTime) {
                sub_time = parseInt((endTime - nowtime) / 1000)
              }
              let timers = new wxtimer({
                complete: function () {
                  if (sub_time < 0) { 
                    clearInterval(timers)
                  }
                }
              });
              //  计时器
              timers.start(that, sub_time, false,'groupPop');
              that.setData({
                needPeople: groupListFilter[0].canJoinPersons,
                // sub_time
              })
            } else {
              that.setData({
                needPeople: 0,
                sub_time: -1,
                groupTime: {
                  day: '00',
                  hour: '00',
                  points: '00',
                  seconds: '00'
                }
              })
              // console.log('@@@@@没有该团的信息')
            }
          }
          that.setData({
            detailSucess: true,
            myLoadAnimation: false,
            isAttention: res.data.data.isAttention,
            shareText: response.shareText,
            groupList,
          })

          if (that.data.detailSucess && that.data.userInfoSuccess && wx.getStorageSync('key')) {
            that.setData({
              reflashToast: false
            })
          }
          if (res.data.data.isAttention == 1) { // 点击过我想参加
            that.setData({
              isAttentionAgainFlag: true
            })
          } else {
            that.setData({
              isAttentionAgainFlag: false
            })
          }

          wx.setStorageSync('attributes', JSON.parse(JSON.stringify(res.data.data.attributes)))
          // 埋点
          let nickName = wx.getStorageSync('nickName')
          let userId = wx.getStorageSync('userId')
          let activityName = res.data.data.title
          let activityId = res.data.data.id
          let time = new Date().toLocaleString()
          app.sensors.track('activityDetail', {
            name: '活动详情页',
            personScan: nickName,
            activityName: activityName,
            activityId: activityId,
            scanTime: time,
            userId: userId
            // personScanPhone:phonebtn,
          });
          var create_time = res.data.data.startTime // 活动开始时间
          var applyStartTime = res.data.data.applyStartTime // 活动买票开始时间
          var end_time = res.data.data.endTime
          var applyEndTime = res.data.data.applyEndTime
          if (res.data.data.detailImgs) {
            res.data.data.detailImgs = res.data.data.detailImgs.split(",")
          }
          // 免费活动的时候显示freeLimit字段，默认是限时免费
          if (!res.data.data.freeLimit) {
            res.data.data.freeLimit = "限时免费"
          }
          // totalplace: 活动总票数
          // place: 活动票数剩余库存
          // shiwutype: 活动类型 0:虚拟 1:实物活动(有收货地址)
          // if (res.data.data.category == 3) {
          //   that.setData({
          //     minNum: that.data.userType == 3 ? res.data.data.salesMinNum : res.data.data.minNum,
          //     maxNum: that.data.userType == 3 ? res.data.data.salesMaxNum : res.data.data.maxNum,
          //   })
          // }
          var reg=new RegExp('TXQImgPath/','ig');
          let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
          let activityData  = res.data.data
          let activityPageData = activityData.activityPageConfig&&activityData.activityPageConfig.replace(reg,dynamicDomainName)
          const activityPageConfig = activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '拼团首页') : null
          let backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
          that.setData({
            enterTime: new Date().getTime(),
            lockDate: res.data.data.lockDate,
            activity: res.data.data,
            backgroundImg,
            place: res.data.data.limitBuy,
            applyEndTime: res.data.data.applyEndTime,
            applyStartTime: res.data.data.applyStartTime,
            create_time: create_time,
            end_time: end_time,
            prdsen: parseInt((1 - res.data.data.place / res.data.data.totalplace) * 100),
            shiwutype: res.data.data.type,
            isProductionTalk: res.data.data.isProductionTalk, // 是否是产说会
            category: res.data.data.category,
            videoUrl: res.data.data.video,
            batchFlag: res.data.data.batchFlag,
            loadAnimation: false,
            warnInfo: res.data.data.subscribed ? '已预约' : '提醒我',
            chatLst: res.data.data.chatLst,  //活动可以参与的业务员渠道
            myLoadAnimation: false,
            isReflash: false,
            activityPageConfig
          })

          that.handlePageStyle(activityPageData)
          //拼团活动category为 6
          let nowtime = new Date().getTime() //目前时间
          let createTime = new Date(applyStartTime.replace(/-/g, '/')).getTime()  // 活动开始时间时间戳
          let endTime = new Date(applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒

          let sub_time1 = parseInt((nowtime - createTime) / 1000) //正数： 活动开始  负数：活动开始倒计时
          let sub_time2 = parseInt((endTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束

          that.setData({
            sub_time1,
            sub_time2
          })
          var subtime = ""
          if (sub_time1 < 0) {
            subtime = parseInt((createTime - nowtime) / 1000)  //活动开始倒计时
          }
          if (sub_time2 < 0) {
            if (this.data.audit||this.data.checkDetail) return
            that.setData({
              isFinish: true,
            })
            wx.showToast({
              title: "活动已结束！",
              icon: "none",
              duration: 2500,
            })
          }
          if (sub_time1 > 0 && sub_time2 > 0) {
            subtime = parseInt((endTime - nowtime) / 1000) //进行中  结束倒计时
          }


          that.createKegeUser()
          let timer = new wxtimer({
            complete: function () {
              if (sub_time1 < 0) { // 活动开始倒计时
                that.getDetail(that.data.ids)
                clearInterval(timer)
              } else if (sub_time1 > 0 && sub_time2 > 0) {
                that.setData({
                  isFinish: true,
                })
                wx.showModal({
                  title: '提示',
                  content: '活动已结束',
                  showCancel: false,
                  success: function (res) {
                    if (res.confirm) {
                      wx.navigateBack({
                        delta: 1
                      })
                    }
                  }
                })
              }
            }
          });
          //  计时器
          timer.start(that, subtime, false);
        } else {
          that.setData({
            myLoadAnimation: false,
          })
          if (res.data.code === 500 && res.data.message === '活动火爆，小主请稍后再试' && !that.data.reflashToast) {
            that.setData({
              detailSucess: false
            })
            that.handleReflashToast()
          } else {
            wx.showToast({
              title: "网络繁忙，请稍后重试",
              icon: "none",
              duration: 2000,
            })
          }
        }
      }
    })
  },
  // 立即购买
  async tobuy (e) {
    console.log(e.target.dataset);
    if(this.data.checkDetail||this.data.audit) return
    if (e.target.dataset.type == 'join' && this.data.sub_time < 0) return
    if (e.target.dataset.type == 'join' && this.data.needPeople == 0) return
    const flag = await this.changeCheck()
    if (!flag) return
    const flags = await this.groupActivityCheck(e.target.dataset.type)
    if (!flags) return
    
    
    if (e.target.dataset.type == 'wantJoin') {
      this.setData({
        groupNo: e.target.dataset.item.groupNo
      })
    }
    let groupsNumber = ''
    if (e.target.dataset.type == 'open') {
      // 开团，团号清空
      groupsNumber = ''
    }else if(e.target.dataset.type == 'join'){
      groupsNumber = this.data.groupNo
    }
    var that = this
    var id = that.data.ids // 活动id
    var category = that.data.category
    var shiwutype = that.data.shiwutype
    console.log('category类型', category);
    const { activity } = that.data
    const isRequire = activity.attributes.filter((item) => item.propertyCode === 'cardNo')[0]?.required
    var selBatchId = (that.data.batchFlag == '1' || that.data.batchFlag == 2) ? that.data.selBatchId : ''
    if (wx.getStorageSync('token') && that.data.userType == 0) {// && !that.data.isShare不管是否分享都要判断是否注册
      if (wx.getStorageSync('userId') !== '') {
        // 判断是否注册过
        that.getIsRegister()
      }
      return false;
    } else if ([1, 3, 4].includes(category) && that.data.activity.place === 0) {
      wx.showToast({
        title: '活动太火爆，已被抢光啦',
        icon: 'none',
        duration: 1500,
        mask: false,
      });
      return
    } else if (that.data.activity.secKill && that.data.chatLst.findIndex(item => ['root', wx.getStorageSync('chatType')].includes(item)) === -1) {

      wx.showToast({
        title: '您的客户经理渠道不在该活动的允许范围内',
        icon: 'none',
        duration: 2500,
        mask: false,
      });
      return false
      // }
    } else if (that.data.userType != 3 && ((that.data.category == 5 && that.data.activeMode === 'PLUS') || that.data.category == 4) && that.data.activity.salesPay == 1) {
      wx.showToast({
        title: '请联系您的客户经理进行活动报名',
        icon: 'none',
        duration: 1500,
      });
      return false
    } else if (that.data.sub_time1 < 0) {
      wx.showToast({
        title: "活动暂未开始！",
        icon: "none",
        duration: 2500,
      })
      return false;
    } else if (that.data.batchFlag == '1' && !that.data.selBatch) {
      wx.showToast({
        title: "请选择批次！",
        icon: "none",
        duration: 2500,
      })
      return false
    } else if (that.data.batchFlag == '2' && !that.data.selBatch) {
      wx.showToast({
        title: "请选择机构！",
        icon: "none",
        duration: 2500,
      })
      return false
    } else if ((that.data.selBatch && that.data.batchFlag == '1') && (new Date(that.data.selBatch['appointTimes']['endTime']).getTime() < new Date().getTime())) {
      wx.showToast({
        title: "该批次的报名已截止,请选择其他批次预约！",
        icon: "none",
        duration: 2500,
      })
      return false
    } else if (that.data.sub_time2 < 0) {
      that.setData({
        isFinish: true
      })
      wx.showToast({
        title: "活动已结束！",
        icon: "none",
        duration: 2500,
      })
    } else if (!that.data.activity.secKill && wx.getStorageSync('token') && (wx.getStorageSync('idCard') == null || wx.getStorageSync('idCard') == '') && isRequire === 1) {

      that.setData({
        chooseArea: !that.data.chooseArea,
        trueName: wx.getStorageSync('trueName')
      })

    } else if (that.data.userType == 3) {
      that.checkYwyLabel()
      return false;
    } else {

      if (http.userAre() && wx.getStorageSync('agree') == '0') {//已登录没有同意隐私项
        that.setData({
          disagree: true,
          jumpType: 'tobuy'
        })
        return
      } else {
        let nickName = wx.getStorageSync('nickName')
        // 埋点
        let activityName = that.data.activity.title
        let activityId = that.data.activity.id
        let activity = that.data.activity
        let userId = wx.getStorageSync('userId')
        let time = new Date().toLocaleString()
        app.sensors.track('activityBuy', {
          name: '立即购买',
          activityId: activityId,
          activityName: activityName,
          personScan: nickName,
          time: time,
          userId: userId
        });
        let url = '/pages/activityready/activityready?cate=' + that.data.category + '&id=' + id + '&shiwutype=' + shiwutype + '&batchId=' + selBatchId + '&activeMode=' + that.data.activeMode + '&groupNo=' + groupsNumber + '&minNum=' + activity.minNum + '&maxNum=' + activity.maxNum + '&salesMinNum=' + activity.salesMinNum + '&salesMaxNum=' + activity.salesMaxNum + '&customerPrice=' + activity.price + '&salesPrice=' + activity.salesPrice + '&salesPay=' + activity.salesPay + '&lockDate=' + activity.lockDate + '&secKill=' + activity.secKill + '&isQtn=' + that.data.activity.isQtn + '&endTime=' + that.data.validTime + "&creditsSource=" + that.data.activity.creditsSource + '&usableCredits=' + that.data.activity.usableCredits + '&refer=' + that.data.refer+'&shareUserId='+that.data.shareUserId
        if (activity.secKill || that.data.category == 6) {
          url += '&title=' + activity.title + '&coverImg=' + activity.coverImg
        }
    
        wx.navigateTo({
          url,
        })
      

      }

    }
  },
  async groupActivityCheck(type){
    let data = {
      activityId:this.data.ids,
      groupNo:type=='join'?this.data.groupNo:''
    }
    const res = await API.groupActivityCheck(data)
    console.log(res)
    return new Promise((resolve, reject) => {
      if (res.data.code == 200) {
        if(res.data.data){
          resolve(true)
        }else{
          wx.showToast({
            title: res.data.message,
            icon: 'none',
            duration: 2000
          })
          resolve(false)
        }
       
      }else{
        wx.showToast({
          title: res.data.message,
          icon: 'none',
          duration: 2000
        })
        resolve(false)
      
      }
    })
  },
  //页面元素处理
  handlePageStyle (activityPageData) {
    const that = this
    console.log(JSON.parse(activityPageData));
    if (this.data.isShare == 2) {
      const tipConfig = activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '参团提示') : null
      if (tipConfig !== undefined && tipConfig !== null) {
        const tipBackgroundImg = tipConfig.componentData.find((item) => item.type === 'backgroundImg')
        const collageTip = tipConfig.componentData.find((item) => item.type === 'collageTip')
        const buttonTip = tipConfig.componentData.find((item) => item.type === 'button'&&item.btEvent=='立即参团')
        const buttonTips = tipConfig.componentData.find((item) => item.type === 'button'&&item.btEvent=='了解详情')
        // collageTip.options.progressTips = collageTip.options.progressTips.replace('X',this.data.peopleNumber)
        let showTitle = false
        let showTime = false
        let showNum = false
        collageTip.checkList.map((item) => {
          if (item == '提示语') {
            showTitle = true
          } else if (item == '剩余时间') {
            showTime = true
          } else if (item == '当前进度') {
            showNum = true
          }

        })
        that.setData({
          tipConfig,
          collageTip,
          tipBackgroundImg,
          buttonTip,
          buttonTips,
          showTitle,
          showNum,
          showTime

        })
      }
    }
    const postersConfig = activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '专属海报') : null
    if (postersConfig !== undefined && postersConfig !== null) {
      const posterBackgroundImg = postersConfig.componentData.find((item) => item.type === 'posterPic')
      const posterText = postersConfig.componentData.find((item) => item.type === 'text')

      that.setData({
        posterBackgroundImg,
        postersConfig,
        posterText,
      })
    }

  },
  openChangeScole () {
    const that = this
    let salesCode = wx.getStorageSync('salesCode')//加密
    let saleName = wx.getStorageSync('saleName')
    // Number(this.data.shareUserType) === 3 && 
    if ((Number(this.data.userType) === 1 || Number(this.data.userType) === 2) && (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName)) {
      this.setData({
        showSwitchSale: true,
        currentId: saleName + salesCode,
        acceptId: this.data.changeSaleName + this.data.changeSalesCode
      })
      if(this.data.activity.distributionChannel==='PSA'){
        this.setData({
          isShowClose:false,
          isShowCancel:false,
        })

      }
    }
  },

  closeChangeTieModal () {
    this.setData({
      showSwitchSale: false
    })
  },
  confirmChangeTieModal () {
    const that = this
    let avatarUrl = wx.getStorageSync('headImgUrl')
    let phone = wx.getStorageSync('phonebtn')//加密1
    let gender = wx.getStorageSync('gender')//1
    let name = wx.getStorageSync('trueName');//加密1
    let nikename = wx.getStorageSync('nickName')//1
    let idCard = wx.getStorageSync('idCard') //加密1,
    let salesCode = this.data.changeSalesCode //加密
    console.log(salesCode)

    if (idCard == '' || idCard == null) {
      // 身份证为空时保存
      var data = {
        name: CryptoJS.Encrypt(name),
        phone: CryptoJS.Encrypt(phone),
        gender: gender,
        nikename: nikename,
        avatarUrl: avatarUrl,
        serialCode: CryptoJS.Encrypt(salesCode),
        idCard: '',
      }

    } else {
      var data = {
        name: CryptoJS.Encrypt(name),
        phone: CryptoJS.Encrypt(phone),
        gender: gender,
        nikename: nikename,
        avatarUrl: avatarUrl,
        serialCode: CryptoJS.Encrypt(salesCode),
        idCard: CryptoJS.Encrypt(idCard),

      }
    }
    API.updateBinding(data).then(res => {
      console.log(res)
      if (res.data.code == 200) {
        console.log(that.data.showSwitchSale)
        that.upDataInfoById()
        that.setData({
          showSwitchSale: !that.data.showSwitchSale,
        })
      }
    })

  },
  closeChooseAreaModal () {
    this.setData({
      chooseArea: !this.data.chooseArea
    })
  },
  idCardValue (e) {
    const value = e.detail.value
    this.setData({ idCard: value })
  },
  confrimChooseArea () {
    const idCard = this.data.idCard
    const cardType = this.data.cardType
    if ( idCard == '' || idCard === null) {
      wx.showToast({
        title: "证件号输入格式有误",
        icon: 'none',
        duration: 2000
      })
      return false;
    }
    this.updateUserInfo()
  },
  async updateUserInfo () {
    const that = this
    const params = {
      idcard: CryptoJS.Encrypt(that.data.idCard),
      cardType: that.data.cardType
    }
    const res = await API.updateUserInfo(params)
    if (res.data.code === 200) {
      wx.showToast({
        title: "保存成功",
        icon: 'none',
        duration: 2000
      })
      wx.setStorageSync('idCard', that.data.idCard);
      wx.setStorageSync('cardType', that.data.cardType);
      that.closeChooseAreaModal()
    } else {
      wx.showToast({
        title: res.data.message,
        icon: 'none',
        duration: 2000
      })
    }
    console.log(res)
  },
  // 业务员标签校验
  async checkYwyLabel () {
    let that = this
    var selBatchId = (that.data.batchFlag == '1' || that.data.batchFlag == 2) ? that.data.selBatchId : ''
    let data = {
      activityId: that.data.ids
    }
    API.checkYwyLabel(data)
      .then(res => {
        if (res.data.code == 200) {
          if (!res.data.data) {
            wx.showToast({
              title: '当前为限定业务员标签活动，您无法参与！',
              icon: 'none',
              duration: 2000,
            });
          } else {
            //校验是否同意隐私项
            if (http.userAre() && wx.getStorageSync('agree') == '0') {//已登录没有同意隐私项
              that.setData({
                disagree: true,
                jumpType: 'tobuy'
              })
              return
            } else {
              let nickName = wx.getStorageSync('nickName')
              let userId = wx.getStorageSync('userId')
              // 埋点
              let activityName = that.data.activity.title
              let activityId = that.data.activity.id
              let activity = that.data.activity
              let time = new Date().toLocaleString()
              app.sensors.track('activityBuy', {
                name: '立即购买',
                activityId: activityId,
                activityName: activityName,
                personScan: nickName,
                time: time,
                userId: userId
              });
              console.log('标签校验');
              let url = '/pages/activityready/activityready?cate=' + that.data.category + '&id=' + that.data.ids + '&groupNo=' + that.data.groupNo + '&shiwutype=' + that.data.shiwutype + '&batchId=' + selBatchId + '&activeMode=' + that.data.activeMode + '&minNum=' + activity.minNum + '&maxNum=' + activity.maxNum + '&salesMinNum=' + activity.salesMinNum + '&salesMaxNum=' + activity.salesMaxNum + '&customerPrice=' + activity.price + '&salesPrice=' + activity.salesPrice + '&salesPay=' + activity.salesPay + '&lockDate=' + activity.lockDate + '&secKill=' + activity.secKill + '&isQtn=' + that.data.activity.isQtn + '&endTime=' + that.data.validTime + "&creditsSource=" + activity.creditsSource + '&usableCredits=' + that.data.activity.usableCredits + '&refer=' + that.data.refer
              if (activity.secKill) {
                url += '&title=' + activity.title + '&coverImg=' + activity.coverImg
              }
             
              wx.navigateTo({
                url,
              })
            }

          }
        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none',
            duration: 1500,
          });

        }
      })
  },

  wantToJoin () {
    console.log('提醒我')
    let that = this
    let data = {
      activityId: Number(that.data.ids)
    }
    API.wantAttention(data).then(res => {
      console.log('async我想参加', res)
      if (res.data.code == 200) {
        that.setData({
          isAttentionAgainFlag: true
        })
      } else {
        wx.showToast({
          title: res.data.message,
          icon: 'none'
        })
      }
    })
  },
  close (e) {
    const tip = '请选择身份登录'
    this.showTips(tip)
  },
  goback () {
    if (this.data.isShare == 1 || this.data.scene || this.data.groupNo) {
      wx.switchTab({
        url: '/pages/home/<USER>',
      })
    } else {
      wx.navigateBack({
        delta: 1
      })
    }
  },

  onClickHide () {
    this.setData({
      showPoster: false
    })
  },
  // 下载海报
  downPoster () {
    this.setData({
      showPoster: true
    })
  },
  async userInfoHandler (e) {
    console.log('执行了用户微信登录')
    const that = this
    if (e.detail.errMsg == "getUserInfo:fail auth deny") { } else {
      wx.removeStorageSync('defaultPerson');
      wx.removeStorageSync('refreshUserInfo')
      app.getUserInfo(async (userInfo) => {
        wx.setStorageSync('userId', userInfo.id);
        wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
        wx.setStorageSync('nickName', userInfo.nikename);
        wx.setStorageSync('openId', userInfo.openId);
        wx.showToast({
          title: '授权成功',
          icon: "none",
          duration: 1500,
        })
        that.setData({
          userId: userInfo.id,
          isAuthorize: true,
          InfoShow: false
        })
        if (wx.getStorageSync('userId') !== '') {
          that.changeAuth()
        }
      })
    }

  },
  // 判断是否登录
  async getIsRegister () {
    let that = this
    if (!that.data.activity) {
      that.getDetail(that.data.ids)
    }
    return new Promise((resolve, reject) => {

      const res = http.unRegister()
      console.log(res)
      if (res) {
        resolve(false)
        that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode,that.data.changeSaleName)
        that.setData({ personLogin: true })
      } else {
        that.data.isAuthorize && showTab()
        that.setData({ InfoShow: false })
        if (that.data.isShare == 1 || that.data.isShare == 2) {
          that.openChangeScole()
        }
        if (that.data.isShare == 2 && (that.data.shareUserId != that.data.userId)) {
          that.setData({ shareGroup: true })
        }
        resolve(true)
      }
    })
  },
  getCurrentIndex (e) {
    this.setData({
      currentIndex: e.detail.current + 1
    })
  },
  onHide () {
    let that = this
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let activityName = that.data.activity.title
    let activityId = that.data.activity.id
    let time = new Date().toLocaleString()
    let hour = (new Date().getTime() - that.data.enterTime) / 1000
    app.sensors.track('activityDetailStayTime', {
      name: '活动详情页停留时长',
      personScan: nickName,
      activityName: activityName,
      activityId: activityId,
      leaveTime: time,
      userId: userId,
      stayTime: hour + 's'

    });

  },
  // 获取用户信息
  async getUserInfo () {
    // 获取用户所属门店信息
    let that = this
    this.setData({
      myLoadAnimation: true,
    })
    const res = await API.getInfoById()
    console.log('--获取用户信息✨✨', res)
    const { data, code, message } = res.data
    if (res.header['Content-Type'] === 'text/html' && !that.data.reflashToast) {
      that.setData({
        myLoadAnimation: false,
      })
      that.handleReflashToast()
    } else {
      if (code == 200) {
        const { cardType } = res.data.data
        const identityValue = that.data.identityType.filter((item) => item.value === cardType)
        console.log(identityValue)
        that.setData({
          type: res.data.data.type,
          salesCode: res.data.data.salesCode,
          idCard: CryptoJS.Decrypt(res.data.data.idcard),
          userType: res.data.data.type,
          credits: res.data.data.credits,
          userInfoSuccess: true,
          myLoadAnimation: false,
          cardType: cardType,
          identity: identityValue[0]?.name || ''
        })

        if (that.data.detailSucess && that.data.userInfoSuccess && wx.getStorageSync('key')) {
          that.setData({
            reflashToast: false
          })
        }
        handleUserStorage(data)

        if (wx.getStorageSync('userId') !== '') {
          // 判断是否注册过
          that.getIsRegister()
        }
      } else {
        that.setData({
          myLoadAnimation: false,
        })
        if (res.data.code === 500 && res.data.message === '活动火爆，小主请稍后再试' && !that.data.reflashToast) {
          that.handleReflashToast()
        } else {
          that.showTips(message)
        }
      }
      return new Promise((resolve, reject) => {
        if (code === 200) {
          resolve(true)
        } else {
          resolve(false)
        }
      })
    }
  },
  async upDataInfoById(){
    let res = await API.getInfoById()
    if (res.data.code == 200) {
      console.log('获取用户信息', res);
      handleUserStorage(res.data.data)  
    }
  },
  // 刷新获取活动数据
  reGetActivity () {
    let that = this
    this.handleReflashToast()
    if (!that.data.userInfoSuccess) {
      this.getUserInfo()
    }
    if (!wx.getStorageSync('key')) {
      CryptoJS.getKey()
    }
    if (that.data.userInfoSuccess && !that.data.detailSucess && !that.data.activity) {
      console.log('加载活动');
      this.getDetail(this.data.ids)
    }
  },
  handleReflashToast () {
    let that = this
    that.setData({
      reflashToast: true,
      reflashBtnContent: `(${totalTime}s)`,
      isReflash: false
    })
    clearInterval(clock);
    clock = setInterval(() => {
      totalTime--;
      that.setData({
        reflashBtnContent: `(${totalTime}s)`
      })
      if (totalTime < 1) {
        clearInterval(clock);
        totalTime = 5
        that.setData({
          reflashBtnContent: '',
          isReflash: true
        })
      }
    }, 1000);
  },
  showTips (title, duration = 3000) {
    wx.showToast({
      title,
      duration,
      icon: "none",
      mask: true
    })
  },
  closeGroup () {
    this.setData({
      shareGroup: false
    })
  },
  reject () {
    this.setData({
      showReject: true,
      rejectReason: ''
    })
  },
  // 提醒我
  handleWarnMe () {
    let { warnInfo, activity: { id } } = this.data
    if (warnInfo === '已预约') {
      API.cardCollectNewWarn({ activityId: id, status: 0 }).then(res => {
        if (res.data.code === 200) {
          this.setData({
            warnInfo: '提醒我'
          })
        }
      })
      return
    }
    let message = 'Y7lBG9cAPzsB1QzpK9MASEp8Xv2YFH4YnkCmILWTlCA'
    wx.getSetting({
      withSubscriptions: true,//是否同时获取用户订阅消息的订阅状态，默认不获取
      success: (res) => {
        if (res.subscriptionsSetting.mainSwitch) { //用户是否打开了接收消息的总开关
          if (res.subscriptionsSetting.itemSettings != null && res.subscriptionsSetting.itemSettings[message]) { // 用户同意总是保持是否推送消息的选择, 这里表示以后不会再拉起推送消息的授权
            const status = res.subscriptionsSetting.itemSettings[message]
            if (status == 'accept') { // accept：接收，reject：拒绝，ban：已被后台禁止
              wx.requestSubscribeMessage({
                tmplIds: [message],
                success: (item) => {
                  if (item[message] == 'accept') {
                    API.cardCollectNewWarn({ activityId: id, status: 1 }).then(res => {
                      if (res.data.code === 200) {
                        this.setData({
                          warnInfo: '已预约'
                        })
                      }
                    })
                  }
                },
                fail: (res) => { console.log('2', res) },
              })
            } else {
              wx.openSetting({
                withSubscriptions: true,
                success: (rej) => {
                  if (rej.subscriptionsSetting.itemSettings != null && rej.subscriptionsSetting.itemSettings[message] == 'accept') {
                    wx.showToast({
                      title: '权限修改成功，请点击提醒我',
                      icon: 'none',
                      duration: 3000
                    })
                  }
                }
              })
            }
          } else {
            wx.requestSubscribeMessage({
              tmplIds: [message],
              success: (res) => {
                if (res[message] == 'accept') {
                  API.cardCollectNewWarn({ activityId: id, status: 1 }).then(res => {
                    if (res.data.code === 200) {
                      this.setData({
                        warnInfo: '已预约'
                      })
                    }
                  })
                }
              },
              fail: (res) => { console.log('2', res) },
            })
          }
        }
      }
    })
  },
  //校验是否同意隐私项
  checkAgree () {
    if (http.userAre() && wx.getStorageSync('agree') == '0') {//已登录没有同意隐私项
      that.setData({
        disagree: true
      })
    } else {
      that.setData({
        disagree: false
      })
    }

  },
  // 弹出框蒙层截断touchmove事件
  preventTouchMove: function () {
    return
  },
  // 分享活动
  onShareAppMessage () {
    if (this.data.audit||this.data.checkDetail) return
    var that = this
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let userType = wx.getStorageSync('userType')
    let salesCode = wx.getStorageSync('salesCode')
    let saleName = wx.getStorageSync('saleName')
    if(that.data.activity.distributionChannel==='PSA' && this.data.changeSalesCode&&userType==3){
      salesCode = this.data.changeSalesCode
      saleName = this.data.changeSaleName
    }
    // 埋点
    let activityName = that.data.activity.title
    let activityId = that.data.activity.id
    let time = new Date().toLocaleString()
    app.sensors.track('activityShare', {
      name: '分享活动',
      activityId: activityId,
      activityName: activityName,
      personScan: nickName,
      shareTime: time,
      userId: userId
    });
    let result = this.data.shareText.replace('#微信昵称#', nickName);
    let titles = result.replace('#活动名称#', activityName);
    const { distributionChannel, financialManagerData } = this.data
    // 客户是理财经理分享带出
    let HTPATH = ''
    if ((userType === 1) && distributionChannel === 'HTZQ') {
      const { financialManager, financialManagerNo } = financialManagerData
      HTPATH = `&financialManager=${financialManager}&financialManagerNo=${financialManagerNo}`
    }
    console.log(`/pages/activityTemplate/group/home/<USER>'userId')}&salesCode=${salesCode}&saleName=${saleName}&userType=${userType}&changeType=${1}&nickName=${nickName}${HTPATH}`);
    return {
      title: titles,
      path: `/pages/activityTemplate/group/home/<USER>'userId')}&salesCode=${salesCode}&saleName=${saleName}&userType=${userType}&changeType=${1}&nickName=${nickName}${HTPATH}`
    }
  },
  // 更改证件类型
  identityChange (e) {
    const that = this
    const identityType = that.data.identityType
    console.log('picker发送选择改变，携带值为', e.detail.value);//index为数组点击确定后选择的item索引
    this.setData({
      identityIndex: e.detail.value,
      identity: identityType[e.detail.value].name,
      cardType: identityType[e.detail.value].value
    })
  },
  // 海通专区判断权限
  changeCheck () {
    return new Promise((resolve, reject) => {
      const userType = wx.getStorageSync('userType')
      const { isHt, distributionChannel, financialManagerShareData, changeSalesCode } = this.data
      const { financialManagerNo, financialManager } = financialManagerShareData

      // 针对海通活动
      if (distributionChannel === 'HTZQ') {
        if (userType === 2) {
          this.setData({ isShowOverlay: true, message: '此活动仅限海通专区用户参与~' })
          resolve(false)
          return
        }
        if (userType === 3 && !isHt) {
          this.setData({ isShowOverlay: true, message: '仅海通专区营销员可参与活动~' })
          resolve(false)
          return
        }

        if (userType === 1 && !isHt) {
          const url = !financialManagerNo ?  `/pages/fuseSpecial/haitong/login/index?salesCode=${changeSalesCode}` : `/pages/fuseSpecial/haitong/login/index?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}`
          wx.navigateTo({ url: url})
          resolve(false)
          return
        }

        resolve(true)
      } else {
        resolve(true)
      }
    })
  },
  async getHyacinthPerm () {
    return new Promise((resolve, reject) => {
      let userType = wx.getStorageSync('userType')
      const distributionChannel = this.data.distributionChannel
      console.log(distributionChannel === 'HTZQ' && (userType === 1 || userType === 3))
      // 获取理财经理信息
      if (distributionChannel === 'HTZQ' && userType === 1) {
        this.getQueryFinancial()
      }
      HTAPI.getHyacinthPerm().then(res => {
        const { data, code, message } = res.data
        if (code === 200) {
          resolve()
          this.setData({ isHt: data.htUser === 1 })
        }
      }).catch(err => {
        resolve()
      })

    })
  },
  async getQueryFinancial () {
    const salesCode = wx.getStorageSync('salesCode')
    const getInfoByIdData = wx.getStorageSync('getInfoByIdData')
    const { financialManagerNo } = getInfoByIdData
    console.log(financialManagerNo)
    if (financialManagerNo === undefined || financialManagerNo === null) return
    const res = await HTAPI.getQueryFinancial({ salesCode, managerCode: financialManagerNo })
    console.log('--✨🍎', res)
    const { code, message, data } = res.data
    if (code !== 200) return
    this.setData({ financialManagerData: data })
  },
  closeOverTip () {
    this.setData({ isShowOverlay: false }, () => {
      wx.switchTab({ url: '/pages/home/<USER>' })
    })
  },
  //权限校验
  async changeAuth () {
    const res = await this.getDetail(this.data.ids)
    const ret = await this.getUserInfo()

    const userType = wx.getStorageSync('userType')
    const { isHt, distributionChannel, financialManagerShareData, changeSalesCode } = this.data

    const { financialManagerNo, financialManager } = financialManagerShareData

    // 针对海通活动
    if (distributionChannel === 'HTZQ') {
      const params = `?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}`
      if (userType === 0 && !isHt && financialManagerNo) return wx.navigateTo({ url: `/pages/fuseSpecial/haitong/login/index${params}` })
    }

    // 判断是否注册过
    this.getIsRegister()
  },
})