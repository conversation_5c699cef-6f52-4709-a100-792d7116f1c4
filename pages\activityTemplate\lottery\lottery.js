// pages/activityTemplate/lottery/lottery.js
const API = require('../../../api/request')
const CryptoJS = require('../../../utils/CryptoJs.js');
import { unRegister, formatTime, splitText, debounce, sensorsData } from '../../../utils/util';
import { getQwUser, getQwInfo } from '../../../api/api'
import { getSaleInfoByCode } from '../../../api/request'
const { setSalesCode } = require('../../../utils/querySalesCode');
import HTAPI from '../../../api/haitong'
const { showTab, handleUserStorage } = require('../../../utils/aboutLogin')
const app = getApp()
let bulletChatList = [];
let id = 0;
let cycle = null;  //计时器
let topArray = [0, 15, 30, 45, 60, 75, 90, 105, 120, 135, 150];//用来做随机top值
let usedTop = []
Page({

  /**
   * 页面的初始数据
   */
  data: {
    businessData:{},
    encryptMsg: '',//企微过来的业务员加密msg信息
    showGlobalPrompt: false,
    globalPromptMessage: '',
    isHt: false,
    isShowOverlay: false,
    message: '',
    financialManagerData: '',
    financialManagerShareData: '',
    distributionChannel: '',
    isShowSubmitIncident: true,
    pageUrl: 'pages/activityTemplate/lottery/lottery',
    activityTemplateType: 14,
    drawConfig: null,
    acceptAwardImg: {},
    isShowLucky: true,
    rejectReason: '',
    prizeIndex: 0,
    isInfo: false,//弹框是否显示录入联系方式
    isShow: false,
    drawNumber: 99,
    dialogImg: '',
    dialogData: {},
    rewardsObj: {},//中奖配置数据
    nothing: {},//未中奖配置数据
    acceptAward: {},//领奖配置数据
    acceptAwardBtn: null,//领奖配置数据
    activityId: '',
    userId: '',
    showModalDlg: false,
    personLogin: false,
    blocks: [],
    prizes: [],
    awardList: [],
    buttons: [],
    height: '',
    backgroundImg: '',
    pageData: [],
    swiperList: [],
    drawPrizes: {},
    recordId: null,
    numberDay: 0,
    bulletChatData: [],
    format: '', //是否为弹幕
    startTurn: true,
    drawMode: 'DRAW',
    eggImg: [],
    showIndex: -1,
    openEggImg: '',
    hammer: '',
    eggInitImg: '',
    showCancel: true,

    _isShare: false,
    shareUserType: '',
    showSwitchSale: false,
    changeSalesCode: '',
    currentId: '',
    acceptId: '',
    showPoster: false,
    qtnIdType: false,
    audit: '', //活动审核判断
    showReject: false,
    twistingEggsList: [
      {
        url: 'https://txqmp.cpic.com.cn/uploads/img/ball_1.png'
      },
      {
        url: 'https://txqmp.cpic.com.cn/uploads/img/ball_2.png'
      },
      {
        url: 'https://txqmp.cpic.com.cn/uploads/img/ball_3.png'
      },
      {
        url: 'https://txqmp.cpic.com.cn/uploads/img/ball_4.png'
      },
      {
        url: 'https://txqmp.cpic.com.cn/uploads/img/ball_5.png'
      },
      {
        url: 'https://txqmp.cpic.com.cn/uploads/img/ball_6.png'
      }
    ],
    twistingEggsStart: false,
    twistingEggsListBG: '',
    twistingEggsListBGList: [],
    twistingEggsEnd: false,
    twistingEggsChooseImage: '',
    selectedId: null,
    resultImg: '',
    cardNo: '',
    encryptMsg: '',
    optionsData: {},
    taskId: ''
  },
  onLoad (options) {
    console.log('options:', options);
    this.setData({ optionsData: options })
    if (options.taskId) this.setData({ taskId: options.taskId })

    let that = this
    let scene = options.scene
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      var id = option[0]
      that.setData({
        activityId: id * 1,
        qtnId: '',//问卷id
        productId: '', //投票活动 作品id
      })
    } else {
      that.setData({
        activityId: options.activityId * 1,//抽奖活动id
        qtnId: options.qtnId || '',//问卷id,集卡id
        productId: options.productId || '', //投票活动 作品id
        drawMode: options.mode || '',
        shareUserId: options.shareUserId || null,
        qtnIdType: options.qtnId ? true : false,
        audit: options.audit || ''
      })
    }

    if (options.isShare) {
      this.setData({
        _isShare: true,
        shareUserType: options.userType,
        changeSalesCode: options.salesCode,
        changeSaleName: options.saleName,
        financialManagerShareData: {
          financialManagerNo: options.financialManagerNo,
          financialManager: options.financialManager,
          distributionChannel: options.distributionChannel
        }
      })
    }
    if (options.mode === 'FACTORY') {
      // 企微跳转过来的砸金蛋活动
      that.setData({
        _isShare: false,
        activityId: options.activityId * 1,//抽奖活动id
        drawMode: options.mode || '',
        encryptMsg: options.msg,
        showCancel: false
      })

    }

  },
  async isAddQiWx () {
    if (!app.isWxwork && this.data.drawMode === 'FACTORY') {
      // 个微环境
      const res = await getQwUser({ params: { type: 'FACTORY', activityId: this.data.activityId } }) // 判断是否加微
      console.log(169, res)
      if (res.code == 200) {
        if (res.data) {
          // 已加微
          return true
        } else {
          // 未加微 跳转业务员企微二维码页面
          let success = await this.decryptLink()
          if (!success) return
          wx.navigateTo({ url: `/pages/fuseSpecial/takePictures/qrCodePrompt/index?source=lottery&salesCode=${this.data.changeSalesCode}` })
          return false
        }
      } else {
        console.log(182, res)
        wx.showToast({ title: res.error || res.message, icon: 'none', duration: 2000 })
        return false
      }
    } else {
      // 企微环境 
      return true
    }
  },
  // 获取企微信息
  async getQwInfo (params) {
    const res = await getQwInfo({ params })
    const { data, success } = res
    if (!success) return
    const { userId } = data
    wx.setStorageSync('userType', '3')
    // wx.setStorageSync('userId', userId)
    // this.setData({ qwSalesCode: userId })
    // const infoData = wx.getStorageSync('getInfoByIdData') ? wx.getStorageSync('getInfoByIdData') : {}
    // const headimg = infoData.avatarUrl ? encodeURIComponent(infoData.avatarUrl) : ''
    // const apc = infoData.unionId ? infoData.unionId : ''
    // const urlParams = { apc, nickname: '', channel: 'taixiangqu', headimg, activityNo: 'GX-ZMR-001', agentCode: userId }
    // const src = urlJoinParams(app.psaPhoto, urlParams)
    // this.setData({ src })
    // this.getSalesName(userId)
  },


  async getSalesName (val) {
    wx.showLoading({ title: '加载中...', mask: true })
    const response = await getSaleInfoByCode({ code: CryptoJS.Encrypt(val) })
    wx.hideLoading()
    const { data, code, message } = response.data
    if (code !== 200) return wx.showToast({ title: message, icon: 'none', duration: 2000 })
    const qwSalesName = CryptoJS.Decrypt(data.name)
    this.setData({ qwSalesName })
  },
  async onShow () {

    this.setData({ onShowStartTime: new Date().getTime() })

    let that = this
    console.log(this.data.drawMode);
    if (this.data.drawMode === 'FACTORY') {
      wx.hideHomeButton();
      const res = await API.getInfoById()
      if (res.data.code != 200) {
        // 用户已授权
        that.setData({ showModalDlg: true })
        return
      }
      // 从内容工厂进入 并且小程序的运行环境，为个微环境
      // 未加微，则跳转营销员的企微二维码弹窗
      const isAddQiWxFlag = await this.isAddQiWx()
      console.log(201, isAddQiWxFlag)
      if (!isAddQiWxFlag) return false;
    }
    const userId = wx.getStorageSync('userId')
    if (!userId) {
      that.setData({ showModalDlg: true })
    } else {
      const ret = await that.getUserInfo()
      console.log(ret)
      // this.getRecordsList()
      if (!ret) return
      if (this.data.drawMode === 'FACTORY') {
        let success = await that.decryptLink()
        if (!success) return
      }
      const flag = await that.getIsRegister()
      console.log(flag)
      if (flag) {
        that.getBasicInfo()
        that.getRecordsList()


      }
    }
    this.findByCode()
    if (this.data.audit != 1) {
      wx.showShareMenu({
        withShareTicket: false,
      })
    } else {
      wx.hideShareMenu({})
    }
    // this.getDommStart()
  },

  // 埋点方法
  async onBuriedPoint (options = {},eventName = 'eventPageView') {
    const {
      title,
      applyStartTime,
      applyEndTime,
      startTime,
      endTime,
      id,
      distributionChannel,
    } = this.data.businessData;
    const userData = wx.getStorageSync("getInfoByIdData");
    const onShowEndTime = new Date().getTime();
    const obj = await sensorsData(userData, "太享趣");
    const data = {
      ...obj,
      name: "活动详情浏览",
      $title: "抽奖活动详情页",
      task_id: this.data.taskId,
      activityId: id,
      activityName: title,
      activity_type: "抽奖活动",
      apply_start_time: applyStartTime,
      apply_end_time: applyEndTime,
      start_time: startTime,
      end_time: endTime,
      customer_name: userData.name ? CryptoJS.Decrypt(userData.name) : "",
      agent_id: userData.salesCode ? CryptoJS.Decrypt(userData.salesCode) : "",
      agent_name: userData.salesName ? CryptoJS.Decrypt(userData.salesName) : "",
      stayTime: onShowEndTime - this.data.onShowStartTime,
      ...options
    }

    if (distributionChannel === 'TX') {
      data.channel = '团险'
    } else {
      if (['SPDB', 'YB_SCE'].includes(distributionChannel)) {
        data.channel = '银保'
      } else {
        data.channel = '个险'
      }
    }

    console.log('sss--✨🍎', data)
    app.sensors.track(eventName,data)
  },


  onHide () {
    this.onBuriedPoint()
    clearInterval(cycle)
    id = 0;
    bulletChatList = []
  },
  onReady: function () {
    this.dialog = this.selectComponent("#dialog");
  },
  onPageScroll ({ scrollTop }) {
    if (this.data.activityTemplateType != 14) {
      if (scrollTop <= 400 && !this.data.isShowLucky) {
        this.setData({ isShowLucky: true })
      }
      if (scrollTop > 400 && this.data.isShowLucky) {
        this.setData({ isShowLucky: false })
      }
    }
  },
  async decryptLink () {
    let that = this
    let data = { msg: that.data.encryptMsg }
    const res = await API.decryptLink(data)

    return new Promise((resolve, reject) => {
      if (res.data.code === 200) {
        let { salesName, empno } = res.data.data
        that.setData({
          changeSalesCode: empno,
          changeSaleName: salesName
        })
        resolve(true)
      } else {
        this.showTips(res.data.message)
        resolve(false)
      }
    })

  },
  cradClick: debounce(async function (e) {
    if (this.data.audit == 1) return

    const flag = await this.changeCheck()
    if (!flag) return

    console.log(353, e)
    const index = e.target.dataset.index
    this.cardNo = this.data.drawConfig.flipsJson[index].cardNo

    if (!this.data.canDraw) {
      this.showTips('您不能参与此次活动~!')
      return
    }
    if (this.data.endTime && new Date().getTime().toString() > new Date(this.data.endTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动已结束!')
      return
    }

    if (this.data.startTime && new Date().getTime().toString() < new Date(this.data.startTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动未开始!')
      return
    }
    if (this.data.numberDay <= 0) {
      this.showTips('您的抽奖次数已用完!')
      return
    }

    this.getDraw("card")
  }, 500),
  onClose () {
    this.setData({ isInfo: false })
  },
  async findByCode () {
    const res = await API.getfindByCode({ code: 'open:plat:h5:code' })
    this.h5Code = res.data.data
  },
  async taskHandle (e) {
    console.log(e);
    const item = e.target.dataset.item
    const index = e.target.dataset.index
    if (item.taskState == 1) return false;

    const custId = wx.getStorageSync('userId')
    const taskSync = item.monitorResult == 1 ? 'Y' : 'N'
    const url = `${item.targetUrl}&taskId=${item.id}&custId=${custId}&sync=${taskSync}&appId=${item.mpAppid}&h5Code=${this.h5Code || ''}`
    if (item.taskType == 'H5') {
      this.asyncTackHandle(item)
      const src = encodeURIComponent(url)
      wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${this.data.activityId}&src=${src}` })
    }
    if (item.taskType == 'EX_MP') {
      wx.navigateToMiniProgram({
        appId: item.mpAppid,
        path: url,
        success: () => {
          this.asyncTackHandle(item)
        }
      })
    }
    if (item.taskType == 'TXQ') {
      // this.asyncTackHandle(item)
      console.log(url);
      wx.reLaunch({
        url: url
      })
    }
    if (item.taskType == "SHARING") {
      this.asyncTackHandle(item)
    }
  },
  async asyncTackHandle (item) {
    // monitorResult	是否需要监控结果 1:是 0:否
    // taskType	任务类型,枚举值【H5跳转-H5】、【太享趣-TXQ】、【外部小程序-EX_MP】、【分享-SHARING】(仅任务类型为跳转时展示)
    // if(item.taskState == 1) return false;
    const custId = wx.getStorageSync('userId')
    // 不需要监控结果时 直接调用接口
    if (item.monitorResult == 0 || item.taskType == 'SHARING' || item.taskType == 'TXQ') {
      wx.showLoading({ title: '加载中...', mask: true })
      const res = await API.bizResultSync({
        commitTime: formatTime(),
        custId: custId, //客户id
        state: 'success',
        taskId: item.id + "",
        userId: null	// 银保虚拟id
      })
      wx.hideLoading()
      if (res.data.code == 200) {
        setTimeout(() => {
          this.getBasicInfo()
        }, 2000)
      } else {
        wx.showToast({ title: res.data.message, duration: 2000, icon: "none" })
      }
    }
  },

  async getQueryFinancial () {
    const salesCode = wx.getStorageSync('salesCode')
    const getInfoByIdData = wx.getStorageSync('getInfoByIdData')
    const { financialManagerNo } = getInfoByIdData
    if (financialManagerNo === undefined || financialManagerNo === null) return
    const res = await HTAPI.getQueryFinancial({ salesCode, managerCode: financialManagerNo })
    console.log('--✨🍎', res)
    const { code, message, data } = res.data
    if (code !== 200) return
    this.setData({ financialManagerData: data })
  },

  async getHyacinthPerm () {
    return new Promise((resolve, reject) => {
      let userType = wx.getStorageSync('userType')
      const distributionChannel = this.data.distributionChannel

      // 获取理财经理信息
      if (distributionChannel === 'HTZQ' && userType === 1) {
        this.getQueryFinancial()
      }
      HTAPI.getHyacinthPerm().then(res => {
        const { data, code, message } = res.data
        if (code === 200) {
          resolve()
          this.setData({ isHt: data.htUser === 1 })
        }
      }).catch(err => {
        resolve()
      })

    })
  },


  //获取基本信息
  async getBasicInfo () {
    let that = this
    let params = {
      id: this.data.activityId,
      drawMode: this.data.drawMode,//'DRAW'
      productId: this.data.productId,
      qtnId: this.data.qtnId
    }
    let response = that.data.audit == 1 ? await API.auditDetail({ id: this.data.activityId }) : await API.getNewDrawInfo(params)
    if (that.data.audit == 1) {
      this.setData({
        isShowSubmitIncident: false
      })
    }
    console.log(response.data)

    if (this.data.drawMode === 'ARTICLE' || this.data.drawMode === 'FACTORY') {
      app.sensors.track('eventPageView', {
        name: '抽奖页浏览',
        time: new Date().toLocaleString(),
        userId: wx.getStorageSync('userId'),
        distinct_id: wx.getStorageSync('openId'),
        activityId: this.data.activityId,
        activityName: response.data.data.title
      })
    }

    if (response.data.code === 200) {
      if (response.data.data.activityTemplateType == 16) {
        const optionsData = this.data.optionsData
        const params = Object.keys(optionsData).map(key => key + '=' + optionsData[key]).join('&');
        wx.redirectTo({
          url: `/pages/activityTemplate/springOuting/home/<USER>
        })
        return
      }

      const distributionChannel = response.data.data.distributionChannel
      this.setData({ distributionChannel })
      if (distributionChannel === 'HTZQ') this.getHyacinthPerm()
      if (distributionChannel === 'WORK_WX') wx.hideShareMenu({})
      var reg = new RegExp('TXQImgPath/', 'ig');
      let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
      let activityData = response.data.data
      let activityPageData = activityData.activityPageConfig && activityData.activityPageConfig.replace(reg, dynamicDomainName)
      let activityPageConfig = JSON.parse(activityPageData)
      console.log(activityPageConfig, '-----基本信息----')
      let prizeConfig = activityPageConfig.find((item) => item.title === '抽奖首页')
      let rule = activityPageConfig.find((item) => item.title === '规则介绍')
      let nothing = activityPageConfig.find((item) => item.title === '未中奖弹窗')
      let rewardsObj = activityPageConfig.find((item) => item.title === '中奖弹窗')
      let acceptAward = activityPageConfig.find((item) => item.title === '领奖弹窗')
      // let acceptSuccess = activityPageConfig.find((item) => item.title === '领奖成功提示')
      const activityPosterConfig = activityPageConfig ? activityPageConfig.find((item) => item.title === '专属海报') : null
      if (activityPosterConfig !== undefined && activityPosterConfig !== null) {
        (activityPosterConfig.componentData ? activityPosterConfig.componentData : []).map((item) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
          if (item.type === 'button') {//处理圆角
            let radius = typeof (item.style.borderRadius)
            if (radius === 'string') {
              const newRadius = item.style.borderRadius.split(' ').map(item => {
                return item * 2 + 'rpx'
              })
              item['styleStr'] = item['styleStr'] + `border-radius:${newRadius.join(' ')}`
            }
          }
        })
        const posterBackgroundImg = activityPosterConfig.componentData.find((item) => item.type === 'posterPic')
        const posterText = activityPosterConfig.componentData.find((item) => item.type === 'text')
        that.setData({
          posterBackgroundImg,
          activityPosterConfig,
          posterText
        })
      }
      if (prizeConfig !== undefined && prizeConfig !== null) {
        (prizeConfig.componentData ? prizeConfig.componentData : []).map((item) => {
          if (item.type === 'list') {
            if (item.bgStatus == 1) {
              item.style = {
                ...item.style,
                wrapBackgroundColor: `url(${item.imgOptions.url})`
              }
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
          if (item.type === 'poster') {
            let radius = typeof (item.style.borderRadius)
            if (radius === 'string') {
              const newRadius = item.style.borderRadius.split(' ').map(item => {
                return item * 2 + 'rpx'
              })
              item['styleStr'] = item['styleStr'] + `border-radius:${newRadius.join(' ')}`
            }
            if (item.style.borderColor || item.style.borderWidth) {
              item['styleStr'] = `${item['styleStr']};border-style:solid;`
            }
          }
          if (item.type === 'button') {//处理圆角
            let radius = typeof (item.style.borderRadius)
            if (radius === 'string') {
              const newRadius = item.style.borderRadius.split(' ').map(item => {
                return item * 2 + 'rpx'
              })
              item['styleStr'] = item['styleStr'] + `border-radius:${newRadius.join(' ')}`
            }
          }
        })
      }
      wx.setStorageSync('lotteryRule', rule.componentData)

      let acceptAwardImg = acceptAward.componentData.find((item) => item.type === 'backgroundImg')
      let acceptAwardBtn = acceptAward.componentData.find(item => item.type == 'button')
      this.setData({
        acceptAwardImg: acceptAwardImg,
        acceptAwardBtn: acceptAwardBtn
      })
      wx.setStorageSync('lotteryRule', rule.componentData)
      wx.setStorageSync('acceptAwardImg', acceptAwardImg)
      wx.setStorageSync('activityTemplateType', response.data.data.activityTemplateType)

      let backgroundImg = null
      if (response.data.data.taskConfigEnable == 1) {
        backgroundImg = prizeConfig.componentData.find((item) => item.type === 'YBbackgroundImg')
      } else {
        backgroundImg = prizeConfig.componentData.find((item) => item.type === 'backgroundImg')
      }
      // let backgroundImg = prizeConfig.componentData.find((item) => item.type === 'backgroundImg')
      let format = prizeConfig.componentData.find((item) => item.type === 'list') && prizeConfig.componentData.find((item) => item.type === 'list').format

      // console.log(prizeConfig.componentData)
      let wheel = prizeConfig.componentData.find((item) => item.type === 'luck')
      let luckImg = {}
      let buttons = []

      if (wheel) {
        luckImg = { src: wheel.propValue.url, width: '100%', height: '100%', rotate: true }
        //抽奖按钮配置
        if (wheel.type === 'luck') {
          // 抽奖形式为转盘
          let luckBtn = { imgs: [], radius: '45%' }
          luckBtn.imgs.push({ src: wheel.propValue.btUrl, width: '170rpx', top: '-100%', })
          buttons.push(luckBtn)
        } else {
          // 抽奖形式为九宫格
          let luckGridBtn = { x: 1, y: 1, imgs: [] }
          luckGridBtn.imgs.push({ src: wheel.propValue.btUrl, width: '100%', top: '100%', })
          buttons.push(luckGridBtn)
        }
      }
      //转盘配置
      let blocks = { padding: '20rpx', imgs: [] }
      blocks.imgs.push(luckImg)
      let arr = []
      arr.push(blocks)

      //转盘奖品展示配置
      let drawPrizesData = JSON.stringify(response.data.data.drawConfig.drawPrizes)
      let prizeData = JSON.parse(drawPrizesData.replace(reg, dynamicDomainName))
      let prizes = []
      if (wheel) {
        if (wheel.type === 'luck') {
          for (var i = 0; i < prizeData.length; i++) {
            let prizeList = { fonts: [], imgs: [] }
            let prizeText = { text: '', top: '30', fontSize: '24rpx', fontColor: '#000' }
            let prizeImage = { src: '', width: '50rpx', height: '50rpx', top: '60' }
            prizeText.text = prizeData[i].prizeName
            if (prizeData[i].url) {
              prizeImage.src = prizeData[i].url
              prizeList.imgs.push(prizeImage)
            }
            prizeList.fonts.push(prizeText)
            prizes.push(prizeList)
          }
        } else {
          const indexs = [
            [0, 0],
            [1, 0],
            [2, 0],
            [2, 1],
            [2, 2],
            [1, 2],
            [0, 2],
            [0, 1],
          ]
          prizeData.forEach((item, index) => {
            prizes.push({
              x: indexs[index][0],
              y: indexs[index][1],
              fonts: [
                {
                  text: item.prizeName,
                  top: '65px',
                  fontColor: '#FF5A0C',
                  fontSize: '12px',
                },
              ],
              background: '#fff',
              imgs: [
                {
                  top: 8,
                  src: item.url,
                  width: '100rpx',
                  height: '100rpx',
                },
              ],
            })
          })
        }
      }
      //老虎机配置
      let tiger = prizeConfig.componentData.find((item) => item.type === 'prize')
      console.log(prizeData, '奖品列表');
      let tigerBlock = []
      let slots = []
      let defaultConfig = {}
      let defaultStyle = {}
      if (tiger) {

        tigerBlock = [
          { padding: '10px', background: tiger.style.backgroundColor, borderRadius: tiger.style.borderRadius + 'px' },
          { padding: '10px', background: ' #FF7437', borderRadius: '10px' }
        ]

        slots = [
          { speed: 15, direction: 1 },
          { speed: 20, direction: -1 },
          { speed: 10, direction: 1 },
        ]
        for (var i = 0; i < prizeData.length; i++) {
          let prizeList = { fonts: [], imgs: [] }
          let prizeText = { text: '', top: '40%', fontColor: tiger.style.color, fontSize: '12px', }
          let prizeImage = { src: '', width: '50%', top: '20%', }
          // prizeText.text = prizeData[i].prizeName
          if (prizeData[i].url) {
            prizeImage.src = prizeData[i].url
            prizeList.imgs.push(prizeImage)
          }
          prizeList.fonts.push(prizeText)
          prizes.push(prizeList)
        }

        defaultConfig = {
          rowSpacing: '0px',
          colSpacing: '10px',

        }
        defaultStyle = {
          background: '#fff',
          // borderRadius: '5px',

        }
      }
      let flipDetails = prizeConfig.componentData.find((item) => item.type === 'flipDetails')
      console.log(prizeConfig.componentData)
      that.setData({
        businessData: response.data.data,
        slots,
        defaultConfig,
        defaultStyle,
        flagPoster: response.data.data.flagPoster,
        title: response.data.data.title,
        qrcode: response.data.data.qrcode,
        startTime: response.data.data.startTime,
        endTime: response.data.data.endTime,
        shareText: response.data.data.shareText,
        shareUrl: response.data.data.shareUrl,
        height: backgroundImg.style.height,
        // height: prizeConfig.canvasStyleData.height * 1,
        backgroundImg: backgroundImg.propValue.url,
        pageData: prizeConfig.componentData,
        flipDetails,
        blocks: arr,
        tigerBlock,
        buttons: buttons,
        prizes,
        awardList: prizeData,
        rewardsObj,
        nothing,
        acceptAward,
        attributes: response.data.data.attributes,
        numberDay: response.data.data.drawInfoVo ? response.data.data.drawInfoVo.restDrawNum : 0,//抽奖次数
        canDraw: response.data.data.drawInfoVo.canDraw,//是否可以抽奖
        drawConfig: JSON.parse(response.data.data.drawConfig && JSON.stringify(response.data.data.drawConfig).replace(reg, dynamicDomainName)),
        shareLimitDaily: response.data.data.drawConfig.shareLimitDaily,//分享次数限制
        format,
        activityTemplateType: response.data.data.activityTemplateType,
        financeStatus: response.data.data.financeStatus || '',
        auditStatus: response.data.data.auditStatus || '',
        receiveType: response.data.data.drawConfig.receiveType,
        activityTaskVos: response.data.data.activityTaskVos,
        taskConfigEnable: response.data.data.taskConfigEnable
      })
      // console.log(436, this.data.attributes)

      // 砸金蛋
      let smashGoldenEggs = prizeConfig.componentData.find((item) => item.type === 'smashGoldenEggs')

      let eggImgList = []
      if (smashGoldenEggs) {
        for (let index = 0; index < 3; index++) {
          eggImgList.push({
            index: index,
            url: smashGoldenEggs.propValue[0]?.url || ''
          })
        }
        that.setData({
          eggImg: eggImgList,
          eggInitImg: smashGoldenEggs.propValue[0]?.url || '',
          openEggImg: smashGoldenEggs.propValue[1]?.url || '',
          hammer: smashGoldenEggs.propValue[2]?.url || '',
          eggBgImg: smashGoldenEggs.propValue[3]?.url || ''
        })
      }
      if (this.data._isShare || (this.data.drawMode === 'FACTORY' && wx.getStorageSync('userType') != 3 && !app.isWxwork)) {
        let salesCode = wx.getStorageSync('salesCode')
        let saleName = wx.getStorageSync('saleName')
        if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
          this.setData({ showSwitchSale: true, currentId: saleName + salesCode, acceptId: this.data.changeSaleName + this.data.changeSalesCode })
        }
      }

      // 扭蛋机
      let twistingEggs = prizeConfig.componentData.find((item) => item.type === 'twistEgg')
      if (twistingEggs) {
        console.log('扭蛋机',)
        this.setData({
          twistingEggsListBGList: twistingEggs.propValue.slice(0, 3),
          twistingEggsListBG: twistingEggs.propValue.slice(3, 4),
          // twistingEggsChooseImage:twistingEggs.propValue.slice(0,3)[0].url
        })
      }

      // 抽奖活动如有配置【奖励领取条件】，则所有用户每日第一次中奖时，则点击领奖弹窗中的“去领取”按钮，直接跳转进入第三方对应ID的文章/问卷页面；同时自动做奖励领取操作（记录领取奖励名称、领取时间）
      // const receiveType = this.data.receiveType
      // console.log(699, receiveType)
      // if (receiveType) {
      //   const activityId = this.data.activityId
      //   const backUrl = encodeURIComponent(`#/results?activityType=DRAW&activityId=${activityId}&drawMode=${this.data.drawMode}&qtnId=${this.data.qtnId}`)
      //   const res = await API.signInGetReceiveUrl({ activityId, backUrl, receiveType, type: "DRAW" })
      //   // console.log(380, res.data)
      //   if (res.data.code == 200) {
      //     this.getUrlRes = res.data.data
      //     // . 如正常返回ID，并未领奖
      //     this.setData({ isFirst: this.getUrlRes.isNot === 'n', getUrlRes: res.data.data })
      //   } else {
      //     wx.showToast({
      //       title: res.data.message,
      //       duration: 2000,
      //       icon: "none",
      //     })
      //   }
      // }
    } else {
      wx.showToast({
        title: response.data.message,
        icon: 'none',
        duration: 2000
      })
    }
  },
  closeChangeTieModal () {
    this.setData({ showSwitchSale: false })
  },

  closeOverTip () {
    this.setData({ isShowOverlay: false }, () => {
      wx.switchTab({ url: '/pages/home/<USER>' })
    })
  },

  /**
   * 
   * @returns {Promise<void>}
   */
  changeCheck () {
    return new Promise((resolve, reject) => {
      const userType = wx.getStorageSync('userType')
      const { isHt, distributionChannel, financialManagerShareData, changeSalesCode } = this.data
      const { financialManagerNo, financialManager } = financialManagerShareData

      // 针对海通活动
      if (distributionChannel === 'HTZQ') {
        if (userType === 2) {
          this.setData({ isShowOverlay: true, message: '此活动仅限海通专区用户参与~' })
          resolve(false)
          return
        }
        if (userType === 3 && !isHt) {
          this.setData({ isShowOverlay: true, message: '仅海通专区营销员可参与活动~' })
          resolve(false)
          return
        }

        if (userType === 1 && !isHt && !financialManagerNo) {
          this.setData({ isShowOverlay: true, message: '此活动仅限海通专区用户参与~' })
          resolve(false)
          return
        }
        if (userType === 1 && !isHt && financialManagerNo) {
          wx.navigateTo({ url: `/pages/fuseSpecial/haitong/login/index?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}` })
          resolve(false)
          return
        }

        resolve(true)
      } else {
        resolve(true)
      }
    })
  },


  async start () {
    if (this.data.drawMode === 'FACTORY') {
      app.sensors.track('drawClick', {
        name: '点击抽奖按钮',
        time: new Date().toLocaleString(),
        userId: wx.getStorageSync('userId'),
        distinct_id: wx.getStorageSync('openId'),
        activityId: this.data.activityId,
        activityName: this.data.title
      })
    }
    const { businessData: { drawInfoVo } } = this.data
    if (this.data.audit == 1) return
    const userType = wx.getStorageSync('userType')
    // if (this.data.drawMode =='ARTICLE'&&userType===3) return this.setData({ showGlobalPrompt: true, globalPromptMessage: '仅客户才能参加本次活动~' })
    const flag = await this.changeCheck()
    if (!flag) return
    if (this.data.startTurn == false) return

    if (drawInfoVo.authMsg) {
      const globalPromptMessage = splitText(drawInfoVo.authMsg)
      this.setData({ showGlobalPrompt: true, globalPromptMessage })
      return
    }
    if (!this.data.canDraw) return this.setData({ showGlobalPrompt: true, globalPromptMessage: '您不能参与此次活动~!' })
    if (this.data.endTime && new Date().getTime().toString() > new Date(this.data.endTime.replace(/-/g, '/')).getTime().toString()) {
      return this.setData({ showGlobalPrompt: true, globalPromptMessage: '抽奖活动已结束!' })
    }
    if (this.data.startTime && new Date().getTime().toString() < new Date(this.data.startTime.replace(/-/g, '/')).getTime().toString()) {
      return this.setData({ showGlobalPrompt: true, globalPromptMessage: '抽奖活动未开始!' })
    }
    if (this.data.numberDay <= 0) return this.setData({ showGlobalPrompt: true, globalPromptMessage: '您的抽奖次数已用完!' })
    this.setData({
      startTurn: false
    })
    // 获取抽奖组件实例
    const child = this.selectComponent('#myLucky')
    child.lucky.play()
    let params = {
      id: this.data.activityId,
      drawMode: this.data.drawMode,//'DRAW'
      qtnId: this.data.qtnId,
      productId: this.data.productId,
      msg: this.data.encryptMsg
    }
    const response = await API.getNewPrize(params)
    // console.log(response,'-----------')
    // 用定时器模拟请求接口
    if (response.data.code === 200) {
      //找出中奖的索引值
      var reg = new RegExp('TXQImgPath/', 'ig');
      let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
      let drawPrizesData = JSON.stringify(response.data.data.drawPrizes)
      let prizeData = JSON.parse(drawPrizesData.replace(reg, dynamicDomainName))
      console.log(prizeData);
      let index = this.data.awardList.findIndex((item) => item.id === prizeData.id)
      // index = 2
      // console.log(index)
      let styleObj = {}
      let isSuccess = true
      if (prizeData.rightsSrc == 'NOTHING') {
        // 未中奖
        styleObj = this.data.nothing
        isSuccess = false
      } else {
        // 中奖
        styleObj = this.data.rewardsObj
      }

      this.onBuriedPoint({
        name: '抽奖',
        button_name: '抽奖',
        prize_name: prizeData.prizeName
      },'lotteryDraw')

      console.log(styleObj)
      // debugger
      let backgroundImg = null
      if (this.data.taskConfigEnable == 1) {
        backgroundImg = styleObj.componentData.find((item) => item.type === 'YBbackgroundImg')
      } else {
        backgroundImg = styleObj.componentData.find((item) => item.type === 'backgroundImg')
      }


      setTimeout(() => {
        child.lucky.stop(index)
        this.setData({
          drawPrizes: prizeData,//中奖数据
          rewardImg: prizeData.url,
          recordId: response.data.data.recordId,
          numberDay: response.data.data.restDrawNum,
          prizeObj: prizeData,
          dialogData: styleObj,
          dialogImg: backgroundImg,
          prizeIndex: index,
          isSuccess
        })

      }, 500)

    } else {
      setTimeout(() => {
        child.lucky.stop()
      }, 500)

      setTimeout(() => {
        wx.showToast({
          title: response.data.message,
          icon: 'none',
          duration: 3000
        })
        this.setData({
          startTurn: true
        })
      }, 1000)
    }
  },
  end (event) {
    console.log(event)
    if (JSON.stringify(event.detail) != '{}') {
      this.setData({
        isShow: true
      })
      this.getRecordsList()
    }
    this.setData({
      startTurn: true
    })
  },
  changeCard: debounce(async function (e) {
    // console.log(e.currentTarget.dataset.key)
    const key = e.currentTarget.dataset.key
    this.setData({
      selectedId: key,
      resultImg: this.data.flipDetails.filpSettings.imgListOptions[key].url

    })
    wx.nextTick(() => {
      wx.pageScrollTo({ selector: '.flipDetails', scrollTop: 444 })
    })

  }, 300),
  jumpPage (e, set) {
    if (this.data.audit == 1) return
    if (e.currentTarget.dataset.btevent == 'rule') {
      wx.navigateTo({
        url: `/pages/activityTemplate/lotteryRule/lotteryRule?id=${this.data.activityId}&qtnIdType=${this.data.qtnIdType}&mode=${this.data.drawMode}`,
      })
    } else if (e.currentTarget.dataset.btevent == 'prize') {
      console.log('去我的奖品页')

      //跳转 我的订单奖品列表
      if (this.data.distributionChannel === 'WORK_WX') {
        wx.navigateTo({ url: '/pages/myactivity/myactivity?status=4' })
      } else {
        wx.navigateTo({
          url: `/pages/activityTemplate/lotteryReward/lotteryReward?id=${this.data.activityId}&mode=${this.data.drawMode}&qtnId=${this.data.qtnId}&productId=${this.data.productId}&msg=${encodeURIComponent(this.data.encryptMsg)}`,
        })
      }
    } else if (e.currentTarget.dataset.btevent == '立即抽奖') {
      console.log('立即抽奖')
      this.data.activityTemplateType === 12 ? this.eggPlay() : this.playGame()
    } else if (e.currentTarget.dataset.btevent == 'draw') {
      // 翻牌抽奖
      this.flipDraw()
    } else if (e.currentTarget.dataset.btevent == 'other') {
      // console.log(e.currentTarget.dataset)
      // 翻牌跳转
      const set = e.currentTarget.dataset.set
      const src = encodeURIComponent(set.jumpUrl)
      wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${this.data.activityId}&src=${src}` })

    }
  },
  //老虎机开始抽奖
  async playGame () {
    if (this.data.drawMode === 'FACTORY') {
      app.sensors.track('drawClick', {
        name: '点击抽奖按钮',
        time: new Date().toLocaleString(),
        userId: wx.getStorageSync('userId'),
        distinct_id: wx.getStorageSync('openId'),
        activityId: this.data.activityId,
        activityName: this.data.title
      })
    }
    if (this.data.audit == 1) return

    const flag = await this.changeCheck()
    if (!flag) return

    if (this.data.startTurn == false) {
      return
    }
    if (!this.data.canDraw) {
      this.showTips('您不能参与此次活动~!')
      return
    }
    if (this.data.endTime && new Date().getTime().toString() > new Date(this.data.endTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动已结束!')
      return
    }

    if (this.data.startTime && new Date().getTime().toString() < new Date(this.data.startTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动未开始!')
      return
    }
    if (this.data.numberDay <= 0) {
      this.showTips('您的抽奖次数已用完!')
      return
    }
    const myLucky = this.selectComponent('#myLucky')
    myLucky.play()
    this.setData({
      startTurn: false
    })
    let params = {
      id: this.data.activityId,
      drawMode: this.data.drawMode,//'DRAW'
      qtnId: this.data.qtnId,
      productId: this.data.productId,
      msg: this.data.encryptMsg
    }
    const response = await API.getNewPrize(params)
    console.log(response, '-----------')
    if (response.data.code === 200) {
      //找出中奖的索引值
      var reg = new RegExp('TXQImgPath/', 'ig');
      let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
      let drawPrizesData = JSON.stringify(response.data.data.drawPrizes)
      let prizeData = JSON.parse(drawPrizesData.replace(reg, dynamicDomainName))
      console.log(prizeData);
      let index = this.data.awardList.findIndex((item) => item.id === prizeData.id)
      let prizeIndex = [] //奖品下标
      console.log(index)
      let styleObj = {}
      let isSuccess = true
      if (prizeData.rightsSrc == 'NOTHING') {
        // 未中奖
        styleObj = this.data.nothing
        isSuccess = false
        let index2 = Math.floor(Math.random() * this.data.awardList.length);
        let index3 = Math.floor(Math.random() * this.data.awardList.length);
        prizeIndex = [index, index2, index3]
      } else {
        // 中奖
        styleObj = this.data.rewardsObj
        prizeIndex = [index, index, index]
      }

      this.onBuriedPoint({
        name: '抽奖',
        button_name: '抽奖',
        prize_name: prizeData.prizeName
      },'lotteryDraw')

      console.log(styleObj, prizeIndex)
      let backgroundImg = null
      if (this.data.taskConfigEnable == 1) {
        backgroundImg = styleObj.componentData.find((item) => item.type === 'YBbackgroundImg')
      } else {
        backgroundImg = styleObj.componentData.find((item) => item.type === 'backgroundImg')
      }
      // let backgroundImg = styleObj.componentData.find((item) => item.type === 'backgroundImg')
      setTimeout(() => {
        myLucky.stop(prizeIndex)
        this.setData({
          drawPrizes: prizeData,//中奖数据
          recordId: response.data.data.recordId,
          numberDay: response.data.data.restDrawNum,
          prizeObj: prizeData,
          dialogData: styleObj,
          dialogImg: backgroundImg,
          prizeIndex: index,
          isSuccess
        })

      }, 500)


    } else {
      setTimeout(() => {
        myLucky.stop()
      }, 500)

      setTimeout(() => {
        wx.showToast({
          title: response.data.message,
          icon: 'none',
          duration: 3000
        })
        this.setData({
          startTurn: true
        })
      }, 1000)
    }

  },
  // 关闭专属海报弹框
  onClickHide () {
    this.setData({
      showPoster: false
    })
  },
  // 下载海报
  downPoster () {
    this.setData({
      showPoster: true
    })
  },
  showSalesPop () {
    this.setData({
      showPoster: true
    })
  },
  //取消事件
  _cancelEvent () {
    console.log('你点击了关闭');
    this.setData({
      isInfo: false,
      isShow: false
    })

  },
  //确认事件
  async _confirmEvent (e) {
    if (this.data.isSuccess) {
      // console.log('你点击了领取');
      console.log(this.data.awardList[this.data.prizeIndex].flagReceiveInfo);
      if (this.data.awardList[this.data.prizeIndex].flagReceiveInfo == 1) {
        this.setData({
          isShow: false,
          isInfo: true
        })
        this.setData({
          acceptDialogData: this.data.acceptAward,
        })
      } else {
        let json = {
          id: this.data.recordId,
        }
        let res = await API.saveRecord(json)
        if (res.data.code == 200) {
          if (e && e.detail && e.detail.url && this.data.activityTemplateType === 7) {
            // 砸金蛋
            const src = encodeURIComponent(e.detail.url)
            wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?src=${src}&mode=${this.data.drawMode}` })
          }
          if (this.data.isFirst) {
            // 今日未跳转第三方
            const src = encodeURIComponent(this.getUrlRes.returnUrl)
            wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${this.data.activityId}&src=${src}` })
          } else {
            // 跳转第三方则不提示用户领取成功
            wx.showToast({ title: "领取成功", icon: "none", duration: 1000 })
          }
          this.setData({
            isFirst: false,
            isShow: false,
          })
          this.getRecordsList()
        } else {
          wx.showToast({
            title: `${res.data.message}!`,
            icon: 'none',
            duration: 3000
          })
        }
      }
    } else {
      //未中奖点击再抽一次
      this.setData({
        isShow: false,
      })
      if (e && e.detail && e.detail.url && this.data.activityTemplateType === 7) {
        const src = encodeURIComponent(e.detail.url)
        wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?src=${src}&mode=${this.data.drawMode}` })
      }
      // this.start()
    }
  },
  //确认保存事件
  _saveEvent (e) {
    // console.log('收货信息保存成功', e.detail);
    let that = this
    this.setData({
      isInfo: false,
      isFirst: false
    })
    wx.showToast({
      title: `领取成功!`,
      icon: 'none',
      duration: 1000
    })
    this.getRecordsList()
    console.log(this.data.acceptAward);
    setTimeout(function () {
      console.log(that.data.acceptAward);
      let flag = that.data.acceptAward && that.data.acceptAward.componentData.find((item) => item.type == 'button')
      console.log(flag);
      if (flag && flag.setting.jumpUrl && that.data.activityTemplateType === 7) {
        let src = encodeURIComponent(flag.setting.jumpUrl)
        wx.navigateTo({ url: `/pages/activityTemplate/lottery/webview/index?id=${that.data.activityId}&src=${src}&mode=${that.data.drawMode}` })
      }
    }, 300)

  },
  goback () {
    if (this.data.isShare == 1) {
      wx.switchTab({
        url: '/pages/home/<USER>',
      })
    } else {
      wx.navigateBack({
        delta: 1
      })
    }
  },
  //获取中奖名单
  async getRecordsList () {
    let params = {
      id: this.data.activityId,
      type: 2,
      pageSize: 100,
      current: 1
    }
    const response = await API.getPageRecordsList(params)
    console.log('中奖名单', response)
    if (response.data.code == 200) {
      this.setData({
        swiperList: response.data.data.records
      })
      setTimeout(() => {
        if (this.data.format == 'barrage') {
          this.getDommStart()
        }
      }, 1500)
    } else {
      wx.showToast({
        title: response.data.message,
        icon: 'none',
        duration: 3000
      })

    }



  },
  // 获取用户信息
  async getUserInfo (token) {
    const res = await API.getInfoById()
    // console.log('--获取用户信息✨✨', res)
    const { data, code, message } = res.data
    if (code == 200) {
      handleUserStorage(data)
    } else {
      this.showTips(message)
    }

    return new Promise((resolve, reject) => {
      if (code === 200) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  },
  handlerStyle (style) {
    // console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  // 判断是否注册过
  async getIsRegister () {
    let that = this
    return new Promise(async (resolve, reject) => {
      const res = !unRegister()
      if (!res) {
        resolve(false)
        that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode, that.data.changeSaleName)

        if (app.isWxwork && this.data.drawMode === 'FACTORY') {
          // 从内容工厂进入 企微环境下小程序做业务员免登
          await that.wxWorkLogin()
        }

        // 从内容工厂进入 并且小程序的运行环境，为个微环境
        // 未加微，则先跳转营销员的企微二维码弹窗 然后绑定用户
        const isAddQiWxFlag = await this.isAddQiWx()
        console.log(201, isAddQiWxFlag)
        if (!isAddQiWxFlag) return false;

        if (this.data.drawMode === 'FACTORY') return wx.navigateTo({
          url: '/pages/subpages/clientLogin/clientLogin',
        })
        this.setData({ personLogin: true })
      } else {
        this.data.isAuthorize && showTab()
        resolve(true)
      }
    })
  },
  wxWorkLogin () {
    let that = this
    return new Promise((resolve, reject) => {
      wx.qy.login({
        success: async (res) => {
          await that.getQwInfo({ code: res.code })
          resolve()
        }
      })
    })
  },
  //用户微信登录授权
  async userInfoHandler () {
    app.getUserInfo(async (userInfo) => {
      wx.setStorageSync('userId', userInfo.id);
      wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
      wx.setStorageSync('nickName', userInfo.nikename);
      wx.setStorageSync('openId', userInfo.openId)
      this.setData({ userId: userInfo.id, isAuthorize: true })
      let that = this
      if (userInfo.id) {
        this.setData({ showModalDlg: false })
        const ret = await this.getUserInfo()
        if (!ret) return

        if (app.isWxwork && this.data.drawMode === 'FACTORY') {
          // 企微环境下小程序做业务员免登
          await that.wxWorkLogin()
        }

        // 从内容工厂进入 并且小程序的运行环境，为个微环境
        // 未加微，则跳转营销员的企微二维码弹窗
        const isAddQiWxFlag = await this.isAddQiWx()
        console.log(201, isAddQiWxFlag)
        if (!isAddQiWxFlag) return false;


        if (this.data.drawMode === 'FACTORY') {
          let success = await that.decryptLink()
          if (!success) return
        }
        await this.getHyacinthPerm()
        const userType = wx.getStorageSync('userType')
        const { isHt, financialManagerShareData, changeSalesCode } = this.data
        const { financialManagerNo, financialManager, distributionChannel } = financialManagerShareData

        // 针对海通活动
        if (distributionChannel === 'HTZQ') {
          const params = `?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}`
          if (userType === 0 && !isHt && financialManagerNo) return wx.navigateTo({ url: `/pages/fuseSpecial/haitong/login/index${params}` })
        }
        const res = await this.getIsRegister() // 判断是否注册过 
        if (res) {
          that.getBasicInfo()
          that.getRecordsList()

        }
      }
    })
  },

  showTips (title, duration = 2000) {
    wx.showToast({
      title,
      duration,
      icon: "none",
      mask: true
    })
  },

  close () {
    this.showTips('请选择身份登录')
  },

  /**
   * 用户点击右上角分享
   */
  async onShareAppMessage () {
    let that = this
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let salesCode = wx.getStorageSync('salesCode')
    let userType = wx.getStorageSync('userType')
    let saleName = wx.getStorageSync('saleName')
    // 埋点
    let time = new Date().toLocaleString()

    this.onBuriedPoint({
      name: '活动分享',
      button_name: '分享客户',
      share_form: '微信好友'
    }, 'eventShare')

    app.sensors.track('activityShare', {
      name: '分享抽奖',
      activityId: that.data.activityId,
      // activityName: activityName,
      personScan: nickName,
      shareTime: time,
      userId: userId
    });
    // 主动分享不增加抽奖次数 如果任务有配置微信分享 则完成改任务增加抽奖次数
    // let data = {
    //   id: that.data.activityId
    // }
    // let res = await API.shareRecord(data)
    var result = this.data.shareText.replace('#微信昵称#', nickName);
    var title = result.replace('#活动名称#', this.data.title);
    // console.log('result:', res);

    const { distributionChannel, financialManagerData } = this.data

    // 客户是理财经理分享带出
    let HTPATH = ''
    if (userType === 1 && distributionChannel === 'HTZQ') {
      const { financialManager, financialManagerNo } = financialManagerData
      HTPATH = `&financialManager=${financialManager}&financialManagerNo=${financialManagerNo}&distributionChannel=${distributionChannel}`
    }
    var reg = new RegExp('TXQImgPath/', 'ig');
    let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
    let imageUrl = this.data.shareUrl.replace(reg, dynamicDomainName)

    return {
      title: title,
      imageUrl: imageUrl,
      path: `/pages/activityTemplate/lottery/lottery?activityId=${that.data.activityId}&isShare=1&shareUserId=${userId}&salesCode=${salesCode}&saleName=${saleName}&mode=${that.data.drawMode}&qtnId=${that.data.qtnId}&userType=${userType}${HTPATH}`
    }


  },
  //设置弹幕的开始
  getDommStart () {
    const _this = this;
    let messageList = []
    _this.data.swiperList.forEach((item) => {
      let message = `恭喜${item.consumerName || '***'}获得${item.prizeName}`
      messageList.push(message)
    })
    cycle = setInterval(function () {
      let arr = messageList
      if (arr[id] == undefined) {
        id = 0;
        // 2.无限循环弹幕
        let obj = {};
        obj.text = arr[id];
        let num = Math.floor(Math.random() * topArray.length);
        obj.top = topArray[num];//拿到随机值 Math.ceil向上取整
        // 被使用了，从原数组删掉并添加到已使用的数组里
        usedTop.push(topArray[num]);
        topArray.splice(num, 1);
        // console.log(obj)
        bulletChatList.push(obj);
        _this.setData({
          bulletChatData: bulletChatList
        })
        id++;
      } else {
        let obj = {};
        obj.text = arr[id];
        let num = Math.floor(Math.random() * topArray.length);
        obj.top = topArray[num];//拿到随机值 
        // 被使用了，从原数组删掉并添加到已使用的数组里
        usedTop.push(topArray[num]);
        topArray.splice(num, 1);
        bulletChatList.push(obj);
        _this.setData({
          bulletChatData: bulletChatList
        })
        id++;
      }
      if (usedTop.length > 5) {
        // 从已使用的数组删掉并添加到原数组里
        topArray.push(usedTop.shift());
      }
    }, 2000)
  },
  //金蛋砸中效果
  smashingGoldenEggs: debounce(async function (e) {
    if (this.data.drawMode === 'FACTORY') {
      app.sensors.track('drawClick', {
        name: '点击抽奖按钮',
        time: new Date().toLocaleString(),
        userId: wx.getStorageSync('userId'),
        distinct_id: wx.getStorageSync('openId'),
        activityId: this.data.activityId,
        activityName: this.data.title
      })
    }
    const that = this
    if (that.data.audit == 1) return
    // const userType = wx.getStorageSync('userType')
    // if (this.data.drawMode =='ARTICLE'&&userType===3) return this.setData({ showGlobalPrompt: true, globalPromptMessage: '仅客户才能参加本次活动~' })

    const flag = await this.changeCheck()
    if (!flag) return

    if (!this.data.canDraw) {
      this.showTips('您不能参与此次活动~!')
      return
    }
    if (this.data.endTime && new Date().getTime().toString() > new Date(this.data.endTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动已结束!')
      return
    }
    if (this.data.startTime && new Date().getTime().toString() < new Date(this.data.startTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动未开始!')
      return
    }
    if (this.data.numberDay <= 0) {
      this.showTips('您的抽奖次数已用完!')
      return
    }
    const data = e.currentTarget.dataset.index
    let imgList = this.data.eggImg
    imgList.forEach((item) => {
      if (item.index === data.index) {
        item.url = that.data.openEggImg
      } else {
        item.url = that.data.eggInitImg
      }
    })
    this.setData({
      showIndex: data.index,
    })
    setTimeout(() => {
      that.setData({
        eggImg: imgList
      })
      that.getDraw()
    }, 2000)
  }, 800),
  async getDraw (val) {
    let params = {
      id: this.data.activityId,
      drawMode: this.data.drawMode,//'DRAW'
      qtnId: this.data.qtnId,
      productId: this.data.productId,
      cardNo: this.data.cardNo,
      msg: this.data.encryptMsg
    }
    // if (val == "card") {
    wx.showLoading({ title: '加载中...', mask: true })
    // }
    const response = await API.getNewPrize(params)
    wx.hideLoading()
    // console.log(response,'-----------')
    // 用定时器模拟请求接口
    if (response.data.code === 200) {
      //找出中奖的索引值
      var reg = new RegExp('TXQImgPath/', 'ig');
      let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
      let drawPrizesData = JSON.stringify(response.data.data.drawPrizes)
      let prizeData = JSON.parse(drawPrizesData.replace(reg, dynamicDomainName))
      let index = this.data.awardList.findIndex((item) => item.id === prizeData.id)
      // index = 2
      console.log(index)
      let styleObj = {}
      let isSuccess = true
      if (prizeData.rightsSrc == 'NOTHING') {
        // 未中奖
        styleObj = this.data.nothing
        isSuccess = false
      } else {
        // 中奖
        styleObj = this.data.rewardsObj
      }

      this.onBuriedPoint({
        name: '抽奖',
        button_name: '抽奖',
        prize_name: prizeData.prizeName
      },'lotteryDraw')

      console.log(styleObj)
      // debugger
      let backgroundImg = null
      if (this.data.taskConfigEnable == 1) {
        backgroundImg = styleObj.componentData.find((item) => item.type === 'YBbackgroundImg')
      } else {
        backgroundImg = styleObj.componentData.find((item) => item.type === 'backgroundImg')
      }
      // let backgroundImg = styleObj.componentData.find((item) => item.type === 'backgroundImg')
      setTimeout(() => {
        this.setData({
          drawPrizes: prizeData,//中奖数据
          recordId: response.data.data.recordId,
          numberDay: response.data.data.restDrawNum,
          prizeObj: prizeData,
          dialogData: styleObj,
          dialogImg: backgroundImg,
          prizeIndex: index,
          isSuccess,
          isShow: true,
        })
        if (this.data.activityTemplateType !== 12) {
          let imgList = this.data.eggImg
          imgList.forEach((item) => {
            item.url = this.data.eggInitImg
          })
          this.setData({
            eggImg: imgList,
            showIndex: -1
          })
        }
      }, 300)
    } else {
      setTimeout(() => {
        wx.showToast({
          title: response.data.message,
          icon: 'none',
          duration: 3000
        })
        if (this.data.activityTemplateType !== 12 && !this.data.flipDetails) {
          this.setData({
            eggImg: imgList,
            showIndex: -1
          })
        }
      }, 1000)
    }
  },
  reject () {
    // console.log('触发了')
    this.setData({
      showReject: true,
      rejectReason: ''
    })
  },
  async eggPlay () {
    let _this = this;
    if (_this.data.audit == 1) return

    const flag = await this.changeCheck()
    if (!flag) return
    if (!this.data.canDraw) {
      this.showTips('您不能参与此次活动~!')
      return
    }
    if (this.data.endTime && new Date().getTime().toString() > new Date(this.data.endTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动已结束!')
      return
    }
    if (this.data.startTime && new Date().getTime().toString() < new Date(this.data.startTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动未开始!')
      return
    }
    if (this.data.numberDay <= 0) {
      this.showTips('您的抽奖次数已用完!')
      return
    }
    _this.setData({
      twistingEggsStart: true
    })
    setTimeout(() => {
      const randomItem = items => items[Math.random() * items.length | 0]
      const randomElement = randomItem(_this.data.twistingEggsListBGList)
      console.log(randomElement)
      _this.setData({
        twistingEggsChooseImage: randomElement.url,
        twistingEggsStart: false,
        twistingEggsEnd: false
      })
      var animation = wx.createAnimation({
        duration: 1500,
        timingFunction: 'ease'
      })
      animation.opacity(1).step()
      this.setData({
        ani: animation.export()
      })
    }, 3000)
    _this.setData({
      twistingEggsEnd: false
    })
    var animation = wx.createAnimation({
      duration: 1500,
      timingFunction: 'ease'
    })
    animation.opacity(0).step()
    this.setData({
      ani: animation.export()
    })
    setTimeout(() => {
      this.getDraw()
    }, 3500)
  },
  async flipDraw () {
    const that = this
    if (that.data.audit == 1) return
    const flag = await this.changeCheck()
    if (!flag) return

    this.cardNo = this.data.drawConfig.flipsJson[this.data.selectedId].cardNo

    if (!this.data.canDraw) {
      this.showTips('您不能参与此次活动~!')
      return
    }
    if (this.data.endTime && new Date().getTime().toString() > new Date(this.data.endTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动已结束!')
      return
    }

    if (this.data.startTime && new Date().getTime().toString() < new Date(this.data.startTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('抽奖活动未开始!')
      return
    }
    if (this.data.numberDay <= 0) {
      this.showTips('您的抽奖次数已用完!')
      return
    }
    that.getDraw("card")

  }
})