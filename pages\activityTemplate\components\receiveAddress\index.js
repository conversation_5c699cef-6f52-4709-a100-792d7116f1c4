const API = require('../../../../api/request')
const CryptoJS = require('../../../../utils/CryptoJs.js');
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showAddress:{
      type:Boolean,
      value:false
    },
    saleName:{
      type: String,
      value: '',
    },
    hotId:{
      type: String,
      value: '0',
    },
    userType:{
      type: String,
      value: '0',
    },
    consumerName:{
      type: String,
      value: '',
    }

  },

  /**
   * 组件的初始数据
   */
  data: {
    radio:'2',
    institutionDefaultAddress:false,//送货至机构是否有默认地址，true有默认地址
    homeAddress:false,//送货上门地址是否有值，true有默认地址
    defaultModelInstitution:{},
    defaultModelHome:{},
    currentSelectDefault:{}
  },
  observers:{
    hotId:function(val){
      console.log(val,'hotId');
      if(val==='1'){
        this.defaultaddress()
      }
    },
    radio:function(val){
      console.log(val,'radio');
      
    },
    currentSelectDefault:function(val){
      console.log(val,'对象');

    }
  },
  lifetimes: {
    created: function () {
      console.log('进入地址弹窗',this.data.hotId)
      if(this.data.hotId==='1'&&this.data.userType==='3'){
        // this.getEmallAddress()
        this.defaultaddress()
      }
     
    },
    detached: function () {
      // 在组件实例被从页面节点树移除时执行
    },
},

  /**
   * 组件的方法列表
   */
  methods: {
    onChange(e){
      console.log(e);
      this.setData({
        radio:e.detail
      })
      if(this.data.radio==='2'){
        // 送货上门
        this.setData({
          currentSelectDefault:this.data.defaultModelHome
        })
      }else{
        this.setData({
          currentSelectDefault:this.data.defaultModelInstitution
        })
      }
      this.triggerEvent('receiveType',{sendModel:e.detail})
    },

     //   查询默认地址
    async defaultaddress () {
      var that = this
      let res = await API.getMyAddress()
      if (res.data.code == 200) {
        const resaddress = res.data.data.records&&res.data.data.records.filter(element => element.isDefault.toString() == 'true')
        console.log(resaddress,'默认地址',this.data.radio);
        if(resaddress.length!=0){
          that.setData({
            homeAddress:true,
            addressId: resaddress[0].id,
            defaultModelHome:resaddress[0],
            currentSelectDefault:resaddress[0]
          })
          this.triggerEvent('defaultAddress',resaddress[0].id)
        }else{
          that.setData({
            homeAddress:false,
            defaultModelHome:null,
            addressId:null,
            currentSelectDefault:this.data.defaultModelInstitution
          })
          this.triggerEvent('defaultAddress',null)
        }
        // debugger
        // if(resaddress.length!=0){
        //   this.setData({
        //     currentSelectDefault:resaddress[0]
        //   })
        // }else{
        //   this.setData({
        //     currentSelectDefault:this.data.defaultModelInstitution
        //   })
        // }
      } else {
        wx.showToast({
          title:res.data.message,
          icon: 'none',
          duration: 2000
        })
      }
    },
    async getEmallAddress () {
      let that = this
      let res = await API.getEmallAddress()
      console.log('getEmallAddress');
      
      if (res.data.code == 200) {
        console.log(res.data)
        if(res.data.data){
          that.setData({
            institutionDefaultAddress:true,
            addressId: res.data.data.id,
            defaultModelInstitution:res.data.data
          })
         
        }else{
          that.setData({
            institutionDefaultAddress:false,
            defaultModelInstitution:null,
            currentSelectDefault:null
          })
        }
        this.triggerEvent('receiveType',{sendModel:'1'})
      } else {
        that.setData({
          institutionDefaultAddress:false,
          defaultModelInstitution:null,
          currentSelectDefault:null
        })
        wx.showToast({
          title:res.data.message,
          icon: 'none',
          duration: 2000
        })
      }
    },
    goAdd(){
      wx.navigateTo({
        url: '/pages/subpages/myaddress/myaddress',
      })
    },
    onClickHide(){
      this.triggerEvent("onClickHide", {showAddress:false})
    },

  }
})
