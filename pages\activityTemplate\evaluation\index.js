
const app = getApp();
import { unRegister } from '../../../utils/util.js'
const API = require('../../../api/request.js')
const { handleUserStorage } = require('../../../utils/aboutLogin')
import Dialog from '../../../assets/vant/dialog/dialog';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showArr1Disable:false,
    showGlobalPrompt:false,
    globalPromptMessage:'',
    showModalDlg:false,
    personLogin:false,
    showArr1: [
      // {
      //   title: '专家课程对于业务实操落地帮助度',
      //   value: 0,
      //   type: 'select'
      // },
      // {
      //   title: '专家课程的通俗易懂性',
      //   value: 0,
      //   type: 'select'
      // },
      // {
      //   title: '课程对于参训学员自身专业提升的帮助度',
      //   value: 0,
      //   type: 'select'
      // },
      // {
      //   title: '课程对于参训学员自身专业提升的帮助度',
      //   value: 0,
      //   type: 'special'
      // },
      // {
      //   title: '请用至少一句话评价或推荐此课程',
      //   value: '',
      //   type: 'input'
      // }
    ],
    complaintValue:0,
    startTime:null,
    activityId:null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // decodeURIComponent(options.q)//
    let params = decodeURIComponent(options.q)//'https://txqmp-sit.cpic.com.cn/verification?activityId=4110153&startTime=43592347193'
    this.getQueryVariable(params)
    let nowTime = new Date().getTime()
    if(this.data.startTime > nowTime) return this.setData({ showGlobalPrompt: true,globalPromptMessage:"活动尚未开始，无法进行评价" })
    this.init ()
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
  },
  getQueryVariable (url) {
    var query = url.substring(1);
    var vars = query.split("?");
    var params = vars[1].split("&");
    // console.log(params);
    let activity = params[0].split("=")
    let time = params[1].split("=")
    console.log(activity,time);
    this.setData({
      activityId:activity[1],
      startTime:time[1]
    })
  },
  async init () {
    if (!wx.getStorageSync("userId")) return this.setData({ showModalDlg: true })
    const ret = await this.getUserInfo()
    if (!ret) return
    const flag = await this.getIsRegister()
    if (!flag) return
    this.evaluatePermCheck()
    
  },
  // 获取题目
   async getQuestion(){
    let res = await API.getAnswer({activityId:this.data.activityId})
    console.log(res.data);
    if(res.data.code===200){
      this.setData({
        showArr1:res.data.data
      })
    }else{
      this.setData({ showGlobalPrompt: true,globalPromptMessage:res.data.message})
    }
  },
  // 校验是否有评价权限
  async evaluatePermCheck(){
    let res = await API.evaluatePermCheck({activityId:this.data.activityId})

    if(res.data.code === 200){
      this.existsEvaluate()
    }else{
      this.setData({ showGlobalPrompt: true,globalPromptMessage:res.data.message })
    }
  },
  // 校验是否评价
  async existsEvaluate(){
    let res = await API.existsEvaluate({activityId:this.data.activityId})
    if(res.data.code == 200){
      if(!res.data.data){
        this.getQuestion()
      }else{
        // 已经评价了
        this.setData({ showGlobalPrompt: true,globalPromptMessage:'您已完成活动评价，请勿重复评价' })
      }
    }else{
      this.setData({ showGlobalPrompt: true,globalPromptMessage:res.data.message })
    }
  },
  userInfoHandler () {
    app.getUserInfo(async (userInfo) => {
      this.setData({ userId: userInfo.id })
      if (userInfo.id) {
        this.setData({ showModalDlg: false })
        const ret = await this.getUserInfo()
        if (!ret) return
        const res = await this.getIsRegister()
        if (!res) return
        this.evaluatePermCheck()
       
      }
    })
  },
  // 获取用户信息
  async getUserInfo () {
    return new Promise((resolve, reject) => {
      API.getInfoById().then(res => {
        const { data, code, message } = res.data
        if (code === 200) {
          handleUserStorage(data)
          resolve(true)
        } else {
          this.showTips(message)
          resolve(false)
        }
      }).catch(err => {
        resolve(false)
      })
    })
  },
  // 判断是否注册过
  async getIsRegister () {
    let that = this
    return new Promise((resolve, reject) => {
      const res = !unRegister()
      if (!res) {
        // 没有注册
        that.setData({
          personLogin: true,
          showModalDlg: false,
        })
        resolve(false)
        
      } else {
        resolve(true)
      }
    })
  },
  // 选择星星
  onChangeStar (e) {
    let { index, status } = e.currentTarget.dataset
    this.setData({
      [`${status}[${index}].value`]: e.detail,
    })
  },
  onChangeSpecialStar(e){
    let { index, status } = e.currentTarget.dataset
    this.setData({
      [`${status}[${index}].value`]:e.detail,
      complaintValue:e.detail?0:1
    })

  },
  selectComplaint(e){
    let { index, status } = e.currentTarget.dataset
    this.setData({
      complaintValue:!this.data.complaintValue,
      [`${status}[${index}].value`]:!this.data.complaintValue?-1:0
    })

  },
  // 选择单选
  onChangeRadio (e) {
    let { index, status } = e.currentTarget.dataset
    this.setData({
      [`${status}[${index}].value`]: e.detail,
    })
  },
  // 主题是否吻合
  onChangeTime (e) {
    this.setData({ isTimeTheSame: e.detail })
  },
  // 输入评价
  writeValue (e) {
    let { index, status } = e.currentTarget.dataset
    this.setData({
      [`${status}[${index}].value`]: e.detail.value.trim(),
    })
  },
  showTips (title, duration = 2000) {
    wx.showToast({
      title,
      duration,
      icon: "none",
    })
  },
  close (e) {
    wx.showToast({
      title: '请选择身份登录',
      duration: 2000,
      icon: "none",
    })
    this.setData({
      personLogin: true
    })
  },
  submit(){
    const feedbackData = this.data.showArr1
    console.log(feedbackData,this.data.complaintValue);
    const info = []
    for (let index = 0; index < feedbackData.length; index++) {
      const n = feedbackData[index];
      let obj = {}
      obj['title'] = n.title
      if (n.type == 'select') {
        if(n.value==0){
          app.showToast('必填项未填写完整，请确认')
          return false
        }
        obj['value'] = n.value
        obj.type = 'select'
      } else if (n.type == 'special') {
        if((!n.value&&!this.data.complaintValue)){
          app.showToast('必填项未填写完整，请确认')
          return false
        }
        obj['value'] = n.value==-1?0:n.value
        obj.type = 'special'
      } else if (n.type == 'INPUT'||n.type == 'input') {
        if (n.value.length < 11) {
          app.showToast('必填项未填写完整，请确认')
          return false
        }
        obj['value'] = n.value
        obj.type = 'input'
      }
      info.push(obj)
    }
    console.log(info);

    Dialog.confirm({
      context: this,
      message: `评价提交后不可更改，请确认`,
    }).then(() => {
      this.saveEvaluation(JSON.stringify(info))

    }).catch(() => {
      app.showToast('已取消')
    });
    
  },
  async saveEvaluation(value){
    let data = {
      activityId:this.data.activityId,
      complaint:'',
      content:value
    }
    let res = await API.saveEvaluate(data)
    console.log(res);
    if(res.data.code != 200) return app.showToast(res.data.message)
    app.showToast(res.data.message)
    wx.switchTab({
      url: '/pages/home/<USER>',
    })
    

  }

})