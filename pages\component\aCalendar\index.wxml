<wxs src="./index.wxs" module="calculate" />
<view class="calendar">
  <view class="calendar_header">
    <view class="calendar_header_arrow">
      <van-icon name="arrow-left" color="#707E9E" bind:click="previousClickEvent" />
    </view>
    <view class="calendar_header_text" bind:tap="changeYear">
      {{selectDay.year+'年'+selectDay.month+"月"}}
    </view>
    <view class="calendar_header_arrow">
      <van-icon name="arrow" color="#707E9E" bind:click="nextClickEvent" />
    </view>
  </view>
  <!--  -->
  <view class="calendar_week">
    <text class="calendar_week_item" wx:for="{{weekDayZh}}" wx:key="index">{{item}}</text>
  </view>
  <!-- <view wx:if="{{open && !(nowDay.year==selectDay.year&&nowDay.month==selectDay.month&&nowDay.day==selectDay.day)}}" class="today" bindtap="switchNowDate">
            今日
          </view> -->
  <!-- 日历头部 -->
  <!-- 日历主体 -->
  <view style="position: relative;">
    <swiper class="calendar_swiper" style="height:{{swiperHeight}}rpx" bindchange="swiperChange" circular="{{true}}" current="{{swiperCurrent}}">
      <swiper-item wx:for="{{[dateList0, dateList1, dateList2]}}" wx:for-index="listIndex" wx:for-item="listItem" wx:key="listIndex">
        <view class="calendar_swiper_item" style="height:{{listItem.length/7*102}}rpx">
          <view wx:for="{{listItem}}" wx:key="dateList" class="calendar_swiper_item_day {{calculate.hasSelect(item,cureentselectDay,oldCurrent,listIndex)}} {{calculate.hasNow(item,nowDay)}} {{calculate.spot(item,selectDay,spotMapList,nowDay)}}">
            <view class="{{calculate.spot(item,selectDay,spotMapList,nowDay)}} {{calculate.hasSelect(item,cureentselectDay,oldCurrent,listIndex)}} {{calculate.hasNow(item,nowDay)}} {{calculate.hasNowMonth(item,selectDay)}}  {{calculate.hasDisable(item,disabledDateList)}}" catchtap="selectChange" data-item="{{item}}">
              {{calculate.hasNow(item,nowDay) ? '今' : item.day  }}
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
    <view wx:if="{{ isShowMask }}" class="calendar_swiper_content"></view>
  </view>
</view>
<!--  -->
<view class="mask" wx:if="{{isShowtip}}">
  <tip year="{{selectDay.year}}" bind:changeM="changeM"></tip>
  <view class="mask_close" bind:tap="closeTips">
    <van-icon name="close" size="48rpx" />
  </view>
</view>