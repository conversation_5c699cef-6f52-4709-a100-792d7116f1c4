// pages/activityTemplate/vote/pk-result/index.js

const API = require('../../../../api/request.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    backgroundImg: null,
    componentData: [],
    leftVote: 0,
    rightVote: 0,
    height: '100vh',
    voteConfig: {},
    voteProductList: [],
    userChooseIdx: 0,

  },

  onLoad: function (options) {
    console.log('onload', options)
    var that = this
    let scene = options.scene
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      var id = option[0]
      that.setData({
        firstInPageTime: new Date().getTime(),
        activityId: id,
      })

    } else {
      that.setData({
        firstInPageTime: new Date().getTime(),
        // activityId: options.activityId,
        activityId: options.activityId,
        audit:options.audit || ''
      })
    }
    this.productType = options.productType

    if (options.isShare) {
      this.setData({
        isShare: true,
        changeSalesCode: options.salesCode,
        changeSaleName: options.saleName,
        shareUserType: options.userType
      })
    }

    // that.onBuriedPoint('votePageScan', '投票首页浏览')
    let userId = wx.getStorageSync('userId')
    let userType = wx.getStorageSync('userType')
    that.setData({
      userId,
      userType,
      shareUserId: options.shareUserId
    })
    that.getHeight()
  },
  getHeight () {
    let systemInfo = wx.getSystemInfoSync()
    let menuButton = wx.getMenuButtonBoundingClientRect()
    let menuHeight = menuButton.height
    let menuRight = systemInfo.screenWidth - menuButton.right
    let menuBotton = menuButton.top - systemInfo.statusBarHeight
    let navBarHeight = (menuButton.top - systemInfo.statusBarHeight) * 2 + menuHeight + systemInfo.statusBarHeight
    const navBarData = {
      navBarHeight,
      menuRight,
      menuBotton,
      menuHeight
    }
    this.setData({
      navBarData
    })
  },

  onShow(){
    this.activityDetail()
  },
  btnClick(e){
    console.log(147,e.currentTarget.dataset)
    const type = e.currentTarget.dataset.type
    if(type == 'shareBtn'){

    }
    if(type == 'money'){
      this.goMore()
    }
  },
  goMore(){
    const temp = this.data.voteProductList[this.data.userChooseIdx]
    console.log(91,temp)
    if(temp.chooseContext == 2){
      // 去抽奖
      wx.redirectTo({
        url: `/pages/activityTemplate/lottery/lottery?activityId=${temp.drawId}&mode=VOTE&qtnId=${this.data.activityId}&productId=${temp.id}`,
      })
    }
    if(temp.chooseContext == 3){
      // h5链接
      const src = encodeURIComponent(temp.link)
      wx.navigateTo({ url: `/pages/activityTemplate/vote/webview/index?src=${src}`})
    }
  },
  // 活动详情
  async activityDetail () {
    let that = this
    let data = {
      activityId: that.data.activityId
    }
    let res = null
    if(that.data.audit == 1){
      res = await API.auditVoteDetail({id:that.data.activityId})
    }else {
      res = await API.votePkDetail(data)
    }
    console.log('活动详情', res)
    if (res.data.code == '200') {

      if(this.productType == 'gift'){
        this.setData({userChooseIdx: 0})
      }else if(this.productType == 'money'){
        this.setData({userChooseIdx: 1})
      }else{
        // 用户无投票次数将直接跳转结果页 根据作品id获取上次投票结果
        this.setData({
          userChooseIdx: res.data.data.voteProductList.findIndex(item => item.id == res.data.data.productId)
        })
      }
      let resultTitle = this.data.userChooseIdx == 0 ? '结果一' : '结果二'

      res.data.data.voteProductList.map(item =>{
        item.mediaList = item.mediaSrc ? item.mediaSrc.split(",") : []
        return item
      })
      
      const activityPageConfig = res.data.data.activityPageConfig ? JSON.parse(res.data.data.activityPageConfig).find((item) => item.title === resultTitle) : null
      if (activityPageConfig !== undefined && activityPageConfig !== null) {
        (activityPageConfig.componentData ? activityPageConfig.componentData : []).map((item) => {
          if (item.type === 'button' && (item.btEvent === 'vote' || item.btEvent === 'canvass')) {
            delete item.style.top
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        console.log('页面组件元素', activityPageConfig.componentData)
        const backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
        const voteDescConfig = activityPageConfig.componentData.find((item) => item.type === 'voteDesc')
        that.setData({
          backgroundImg: backgroundImg.propValue.url,
          height: backgroundImg.style.height,
          componentData: activityPageConfig.componentData,
          activityPageConfig: res.data.data.activityPageConfig,
          voteDescConfig: voteDescConfig || {},
        })
      }
      const activityPosterConfig = res.data.data.activityPageConfig ? JSON.parse(res.data.data.activityPageConfig).find((item) => item.title === '专属海报') : null
      if(activityPosterConfig !== undefined && activityPosterConfig !== null){
        (activityPosterConfig.componentData ? activityPosterConfig.componentData : []).map((item) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        const posterBackgroundImg = activityPosterConfig.componentData.find((item) => item.type === 'posterPic')
        const posterText = activityPosterConfig.componentData.find((item) => item.type === 'text')
        that.setData({
          posterBackgroundImg,
          activityPosterConfig,
          posterText
        })
      }
      console.log(activityPageConfig)
      // 计算倒计时
      let nowtime = new Date().getTime() //目前时间
      let from_time1 = -1
      let from_time2 = -1
      if (res.data.data.voteConfig.mobileLimit === 1) {
        var joinbeginTime = res.data.data.applyStartTime // 报名开始时间
        var joinendTime = res.data.data.applyEndTime // 报名结束时间
        let beginTime1 = new Date(joinbeginTime.replace(/-/g, '/')).getTime()  // 报名开始时间时间戳
        let endTime1 = new Date(joinendTime.replace(/-/g, '/')).getTime() // 报名结束时间时间戳毫秒
        from_time1 = parseInt((nowtime - beginTime1) / 1000) //正数： 活动开始  负数：活动开始倒计时
        from_time2 = parseInt((endTime1 - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束
        console.log('报名倒计时', from_time2)
        console.log(from_time1)
      }
      var applyStartTime = res.data.data.voteConfig.voteBegin // 投票开始时间
      var applyEndTime = res.data.data.voteConfig.voteEnd // 投票结束时间
      let createTime = new Date(applyStartTime.replace(/-/g, '/')).getTime()  // 活动开始时间时间戳
      let endTime = new Date(applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒
      let sub_time1 = parseInt((nowtime - createTime) / 1000) //正数： 活动开始  负数：活动开始倒计时
      let sub_time2 = parseInt((endTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束
      // console.log(sub_time1, sub_time2)
      that.setData({
        sub_time1,
        sub_time2,
        from_time1,
        from_time2,
        voteConfig: res.data.data.voteConfig,
        voteProductList: res.data.data.voteProductList,
        flagConnect: res.data.data.flagConnect,
        activityInfo: res.data.data,
        financeStatus: res.data.data.financeStatus||'',
        auditStatus: res.data.data.auditStatus||''
      })

      let leftVote = res.data.data.voteProductList[0].chooseNum
      let rightVote = res.data.data.voteProductList[1].chooseNum
      // 左右最少 展示30% 的宽度
      let temp = leftVote / (leftVote + rightVote)
      let leftProgressW = 0
      if(temp <= 0.3){
        leftProgressW = 0.3 * 650
      }else if(temp >= 0.7){
        leftProgressW = 0.7 * 650
      } else{
        leftProgressW = temp * 650
      }
      console.log(108, temp, leftProgressW)      

      let leftVotePercent = (leftVote / (leftVote + rightVote) * 100).toFixed(0)
      let rightVotePercent = (rightVote / (leftVote + rightVote) * 100).toFixed(0)
      that.setData({
        leftProgressW,
        leftVotePercent,
        rightVotePercent
      })

      if (sub_time2 < 0 && this.data.audit != 1) {
        that.setData({
          isFinish: true,
        })
        wx.showToast({
          title: "投票已结束！",
          icon: "none",
          duration: 1500,
        })
      }
    } else {
      wx.showToast({
        title: res.data.message,
        icon: 'none'
      })
    }
    that.setData({
      loadAnimation: false
    })

    if (this.data.isShare) {
      let salesCode = wx.getStorageSync('salesCode')
      let saleName = wx.getStorageSync('saleName')
      if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
        this.setData({ showSwitchSale: true, currentId: saleName + salesCode, acceptId: this.data.changeSaleName + this.data.changeSalesCode })
      }
    }
  },
  // 返回上一个页面
  back () {
    wx.navigateBack({ url: -1 })
  },
  handlerStyle (style) {
    // console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage (res) {
    if(this.data.audit) return
    console.log('用户点击分享按钮', res)
    let userId = wx.getStorageSync('userId')
    var that = this
    let path = ``
    let activityId = that.data.activityId
    let title = ''
    let imageUrl = ''
    let salesCode = wx.getStorageSync('salesCode')
    let userType = wx.getStorageSync('userType')
    let saleName = wx.getStorageSync('saleName')

    const { shareText } = this.data.activityInfo
    console.log('shareText--✨🍎', shareText)
    if (shareText) {
      title = shareText.replace('#微信昵称#', wx.getStorageSync('nickName')).replace('#活动名称#', this.data.activityInfo.title)
    }

    // .replace('#作品名称#',wx.getStorageSync('nickName'))
    imageUrl = this.data.activityInfo.shareUrl
    path = `/pages/activityTemplate/vote/pk-result/index?activityId=${activityId}&shareUserId=${wx.getStorageSync('userId')}&clientShareUserId=${userId}`
  
    // that.onBuriedPoint('voteShare', '投票分享')
    return {
      title: title,
      imageUrl: imageUrl,
      path: `${path}&salesCode=${salesCode}&saleName=${saleName}&isShare=1&userType=${userType}`,
    }
  },
})