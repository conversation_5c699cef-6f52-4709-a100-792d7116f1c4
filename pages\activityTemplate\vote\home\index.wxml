<!-- 没有同意协议显示弹窗 -->
<view class="mask" hidden="{{loadAnimation==false}}">
  <view class="spinner">
    <view class="bounce1"></view>
    <view class="bounce2"></view>
    <view class="bounce3"></view>
  </view>
</view>
<!-- 没有同意协议显示弹窗 -->
<!-- 投票内容 -->
<scroll-view>
  <view class="navTitle" style="height: {{navBarData.navBarHeight}}px">
    <view class="nav_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px">
      <van-icon bindtap="backPage" class="nav--arrow" name="arrow-left" />
      <text class="nav_title">开始投票</text>
    </view>
  </view>
  <view class="contain" style="margin-top:{{navBarData.navBarHeight}}px;background-image:url({{backgroundImg}})">
    <view class="content">
      <!-- 投票活动信息 -->
      <scroll-view scroll-y lower-threshold='100' style="margin-bottom: 200rpx;height: 90vh;" bindscrolltolower="scrollTolower">
        <block wx:for="{{componentData}}" wx:key="index" wx:if="{{item.type === 'button' && item.btEvent === 'activity'}}">
          <view class="activityNeeds" style="{{item.styleStr}}" bindtap="toActivityDetail">
            {{item.propValue}}
          </view>
        </block>
        <view class="voteInfo">
          <!-- 倒计时 -->
          <view class="voteCouter" wx:if="{{voteDescConfig}}" style="{{voteDescConfig.styleStr}}">
            <view class="voteCouterTitle" style="color:{{voteDescConfig.style.textColor}}">
              {{voteDescConfig.propValue}}
            </view>
            <view class="voteCouterTime">
              <view class="num" style="color:{{voteDescConfig.style.numberColor}};background: {{voteDescConfig.style.numberBackground}};box-shadow: {{voteDescConfig.style.numberBoxShadow}};">
                {{timeObj.day}}
              </view>
              <view class="numUnit" style="margin:0 8rpx;">天</view>
              <view class="num" style="color:{{voteDescConfig.style.numberColor}};background: {{voteDescConfig.style.numberBackground}};box-shadow: {{voteDescConfig.style.numberBoxShadow}};">
                {{timeObj.hour}}
              </view>
              <view class="numUnit">:</view>
              <view class="num" style="color:{{voteDescConfig.style.numberColor}};background: {{voteDescConfig.style.numberBackground}};box-shadow: {{voteDescConfig.style.numberBoxShadow}};">
                {{timeObj.points}}
              </view>
              <view class="numUnit">:</view>
              <view class="num" style="color:{{voteDescConfig.style.numberColor}};background: {{voteDescConfig.style.numberBackground}};box-shadow: {{voteDescConfig.style.numberBoxShadow}};">
                {{timeObj.seconds}}
              </view>
            </view>
          </view>
          <!-- 报名信息 -->
          <view class="activityDetailBox" style="margin-top:{{voteDescConfig.style.height * 2 + 44}}rpx;">
            <view class="detailTopBox">
              <view class="numInfo" style="background-color: {{voteNumConfig.backgroundColor}};">
                <view class="realNum" style="color:{{voteNumConfig.numberColor}}">
                  {{products}}
                </view>
                <view class="numTitle" style="color:{{voteNumConfig.textColor}}">报名人数</view>
                <image class="img_desc" src="{{imgOptions[0] ? imgOptions[0] : '../../../../image/activity/vote_num.png'}}"></image>
              </view>
              <view class="numInfo" style="background-color: {{voteNumConfig.backgroundColor}};">
                <view class="realNum" style="color:{{voteNumConfig.numberColor}}">
                  {{totalVote}}
                </view>
                <view class="numTitle" style="color:{{voteNumConfig.textColor}}">累积投票</view>
                <image class="img_desc" src="{{imgOptions[1] ? imgOptions[1] :'../../../../image/activity/vote.png'}}"></image>
              </view>
            </view>
            <view class="voteNeedsBox" style="background-color: {{voteNotesConfig.style.backgroundColor}};">
              <span>投票须知：</span>
              <block wx:if="{{voteConfig.voteNotes !== ''}}">
                <rich-text nodes="{{voteConfig.voteNotes}}" class="ql-editor"></rich-text>
              </block>
            </view>
          </view>
          <!-- 搜索框 -->
          <view class="searchOutter">
            <view class="searchBox">
              <input type="text" placeholder='输入参赛名称' value="{{filters}}" bind:blur="changeInput" placeholder-style="color:#D0D2DB;" class="inputCellBox" />
            </view>
            <view class="searchIconBtn">
              <image src="../../../../image/activity/vote_search.png" class="iconBtnSearch" alt="" />
            </view>
          </view>
          <!-- 排名按钮 -->
          <view class="classBtns">
            <view bindtap="popuRank" class="tabCss {{tabActive==1 ? 'tabActive' : ''}}">人气排名</view>
            <view bindtap="newJoin" class="tabCss {{tabActive==2 ? 'tabActive' : ''}}">最新参赛</view>
          </view>
        </view>
        <!-- 投票活动瀑布流 -->
        <view class="voteListBox">
          <view class="left">
            <block wx:for="{{voteList}}" wx:key="id">
              <view class="voteWorkItem" wx:if="{{index%2==0}}">
                <view class="voteWorkItem_img">
                  <view class="classIco" wx:if="{{index < 4&& tabActive==1}}">
                    <view class="classIcoItem">
                      <span></span>
                      <text>排名</text>
                      <text>{{index+1}}</text>
                    </view>
                  </view>
                  <view class="classIcoRight">编号：{{item.id}}</view>
                  <image wx:if="{{item.mediaType == 'IMG'}}" src="{{item.mediaSrc}}" alt="" class="activityItemImg" />
                  <video wx:if="{{item.mediaType == 'VIDEO'}}" src="{{item.mediaSrc}}" object-fit='fill' show-center-play-btn='true' show-play-btn="true" show-fullscreen-btn="{{fullscreen}}" controls class="activityItemImg" style="height: 250rpx;"></video>
                </view>
                <view class="voteWorkItem_title">{{item.infoConfog['参赛名称']}}</view>
                <view class="voteWorkItem_info">
                  <view class="voteWorkItem_num">
                    {{item.votes || 0}}
                    <span>人气</span>
                  </view>
                  <view class="voteWorkItem_vote {{item.votedNum > 0 ? 'voteWorkItem_vote_success' : ''}}" bindtap="toVote" data-title="{{ item.infoConfog['参赛名称'] }}" data-id="{{item.id}}">
                    {{item.votedNum > 0 ? '已投票' : '投票'}}
                  </view>
                </view>
              </view>
            </block>
          </view>
          <view class="right">
            <block wx:for="{{voteList}}" wx:key="id">
              <view class="voteWorkItem" wx:if="{{index%2==1}}">
                <view class="voteWorkItem_img">
                  <view class="classIco" wx:if="{{index < 4&& tabActive==1}}">
                    <view class="classIcoItem">
                      <span></span>
                      <text>排名</text>
                      <text>{{index+1}}</text>
                    </view>
                  </view>
                  <view class="classIcoRight">编号：{{item.id}}</view>
                  <image wx:if="{{item.mediaType == 'IMG'}}" src="{{item.mediaSrc}}" alt="" class="activityItemImg" />
                  <video wx:if="{{item.mediaType == 'VIDEO'}}" src="{{item.mediaSrc}}" object-fit='fill' show-center-play-btn='true' show-play-btn="true" show-fullscreen-btn="{{fullscreen}}" controls class="activityItemImg" style="height: 250rpx;"></video>
                </view>
                <view class="voteWorkItem_title">{{item.infoConfog['参赛名称']}}</view>
                <view class="voteWorkItem_info">
                  <view class="voteWorkItem_num">
                    {{item.votes || 0}}
                    <span>人气</span>
                  </view>
                  <view class="voteWorkItem_vote {{item.votedNum > 0 ? 'voteWorkItem_vote_success' : ''}}" bindtap="toVote"  data-title="{{ item.infoConfog['参赛名称'] }}" data-id="{{item.id}}">
                    {{item.votedNum > 0 ? '已投票' : '投票'}}
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>
        <view class="footInfoWarn">{{isPageFinished ? '--没有更多了--' : '下拉加载更多'}}</view>
      </scroll-view>
      <!-- 投票活动信息 -->
      <!-- 底部显示两个按钮 -->
      <block wx:for="{{componentData}}" wx:key="index">
        <block wx:if="{{voteConfig.mobileLimit != 1 || canApply == false || (voteConfig.applyerType != null && voteConfig.applyerType != userType)}}">
          <!-- <view wx:if="{{item.type === 'button' && item.btEvent === 'canvass'}}" style='{{item.styleStr}}' class="bargain">
            <view bindtap="toCanvass"  wx:if="{{isShowCanvassBtn && !shareDirectly}}" style="width: 100%;" class="buttonName">我要拉票</view>
            <button open-type="share"  wx:if="{{isShowCanvassBtn && shareDirectly}}" style="width: 100%;background: none;color:{{item.style.color}};" class="buttonName">我要拉票</button>
          </view> -->
        </block>
        <block wx:else>
          <view wx:if="{{item.type === 'button' && item.btEvent === 'vote'}}" style='{{item.styleStr}}' class="bargain">
            <view bindtap="toJoin" class="buttonName">{{item.propValue}}</view>
            <!-- <view class="buttonName buttonWidth buttonBgc" bindtap="toCanvass"  wx:if="{{isShowCanvassBtn && !shareDirectly}}">我要拉票</view>
            <button class="buttonName buttonWidth buttonBgc" open-type="share"  wx:if="{{isShowCanvassBtn && shareDirectly}}">我要拉票</button> -->
          </view>
        </block>
        <block wx:if="{{item.type === 'poster' && activityInfo.flagPoster == 1}}">
          <!-- <root-portal wx:if="{{activityInfo.flagPoster == 1}}" enable="{{true}}"> -->
            <!-- 专属海报弹框 -->
            <post-popup show="{{showPoster}}" codeUrl="{{activityInfo.qrcode}}" activityName="{{activityInfo.title}}" btnText="{{item}}" banner="{{posterBackgroundImg.propValue.url}}" title="{{posterText.propValue}}" textStyle="{{posterText}}" bind:onClickHide="onClickHide" bind:clickDownPoster="downPoster" audit="{{audit}}"></post-popup>
            <!-- 专属海报弹框 -->
          <!-- </root-portal> -->
        </block>
      </block>
      <!-- 底部显示两个按钮 -->
    </view>
  </view>
</scroll-view>
<!-- 活动审核显示底部操作按钮 -->
<audit activityId="{{activityId}}" activityType="9" linkType="{{linkType}}" wx:if="{{audit}}" bind:reject="reject"></audit>
<!-- 活动审核显示底部操作按钮 -->
<!-- 投票内容 -->
<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close" allShow='{{isClose}}'></login-box>
<!-- 登陆者身份选择 -->
<!-- 参与活动触发弹框 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler">点击参与</button>
  </view>
</view>
<!-- 参与活动触发弹框 -->
<!-- 拉票活动选择 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showPickDlg}}"></view>
<view class="modalDlg1" wx:if="{{showPickDlg}}">
  <view bindtap="toCloseActivityPick" class="closeX">
    <image class="xCss" src="../../../image/x.png" alt="" />
  </view>
  <view class="pickTitle">我要拉票</view>
  <view class="btnsBox">
    <block wx:for="{{myActivities}}" wx:key="id">
      <button open-type="share" style="{{currentProductIndex==index && currentProductIndex!= '' ? 'background-color: rgba(254, 86, 73, 0.1); color: #FE5649;' : ''}}" bindtap="toShare" data-id="{{item.id}}" data-index="{{index}}" class="btnCss" data-lapiaoTip="{{item.infoConfog['拉票宣言']}}">
        {{item.infoConfog['参赛名称']}}
      </button>
    </block>
  </view>
</view>
<!-- 拉票活动选择 -->
<!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" salesCode="{{changeSalesCode}}" bind:close="closeChangeTieModal"></switch-sale>
<!-- 分享页面是否切换业务员 -->

<!-- 活动审核驳回弹框 -->
<reject-reason showDialog="{{showReject}}" activityId="{{activityId}}" audit="{{auditStatus}}" financeStatus="{{financeStatus}}" rejectReason="{{rejectReason}}" ></reject-reason>
<!-- 活动审核驳回弹框 -->

<contact module="活动参与" pageName="投票首页" pageUrl="{{pageUrl}}" businessData="{{activityInfo}}" isShow="{{isShow}}"></contact>