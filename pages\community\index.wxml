<!-- 新社区 -->
<view>
  <!-- 导航标题 -->
  <view class="navTitle" style="height: {{navBarData.navBarHeight}}px">
    <view class="nav_menu" style="bottom:{{navBarData.menuBotton}}px;height:{{navBarData.menuHeight}}px">
      <van-image class="nav-image" round width="64rpx" height="64rpx" src="{{headImgUrl}}" bindtap="goSelfCommuinty" />
      <view class="redDot" wx:if="{{newPointStatus}}"></view>
      <view class="tabSwitch">
        <block wx:for="{{tabSwitch}}" wx:key="index">
          <view class="box {{tabId === item.id?'activeTab':'box'}}" data-id="{{item.id}}" bindtap='selectactivitytab'>
            <view>{{item.name}}</view>
            <view class="bordrLine" hidden="{{tabId === item.id?false:true}}"></view>
          </view>
        </block>
      </view>
    </view>
  </view>
  <!-- 发现搜索和列表 -->
  <view class="invitationBox__find" style="margin-top: {{navBarData.navBarHeight}}px">
    <!-- 搜索栏 -->
    <view class="invitationBox__head--find">
      <view class="search" style="width:100%;position: relative;">
        <van-search value="{{ keyword }}" shape="round" background="#fff" placeholder="搜索" placeholder-style="color:#D0D2DB;font-size: 28rpx;font-family: PingFangSC-Semibold, PingFang SC;padding-left:8rpx" bind:change="changeInput" left-icon='../../image/searchImg.png' bind:focus="focus" bind:blur="blurClick" bind:clear="clearKeyWord" bind:cancel="clearKeyWord">
          <view slot="action" bindtap="{{!isClearText?'toSearch' : 'clearKeyWord'}}">
            {{!isClearText? '搜索' : '清空'}}
          </view>
        </van-search>
        <image src="../../image/searchImg.png" class="searchIcon" />
      </view>
    </view>
    <!-- 筛选分公司 -->
    <view class="selectCompany" wx:if="{{tabId == 2 && showSelectCompany }}">
      <view>
        <image src="../../image/locus1.png" class="location" />
        <text>{{corpName}}</text>
      </view>
      <view bindtap="showCompanys" class="switchTag">
        <view>点击切换</view>
        <van-icon name="arrow" size="32rpx" color="#025CEA" custom-style="margin-top:0rpx;vertical-align: bottom; " />
      </view>
    </view>
    <!-- 标签分类 -->
    <van-sticky offset-top="{{ navBarData.navBarHeight-1}}" wx:if="{{tabId == 1 && showTab}}">
      <view style="display: flex;justify-content: space-between;padding: 0 28rpx;background: #fff;">
        <view style="width: 85%;" class="nav">
          <view wx:for="{{tabs}}" wx:for-item="list" wx:key="list" class="navItem {{list.name == active?'itemActive':''}}" catch:tap="clickTab" data-name="{{list.name}}">
            <view wx:if="{{list.name == '玩转太享趣'}}">
              <image class="navItem_img" src="https://txqmp.cpic.com.cn/uploads/img/txq.gif" />
            </view>
            <view wx:else>{{ list.name }}</view>
          </view>
          <!-- <van-tabs active="{{ active }}" bind:click="clickTab" swipe-threshold='3' color="#025CEA">
            <van-tab title="{{list.name}}" wx:for="{{tabs}}" name="{{list.id}}" wx:for-item="list" wx:key="list"></van-tab>
          </van-tabs> -->
        </view>
        <view class="sort-style" bindtap="showTabs">
          <view>排序</view>
          <van-icon name="arrow-down" size="32rpx" color="#323233" custom-style="margin-top:-4rpx" />
        </view>
        <!-- 标签分类弹窗 -->
        <view hidden="{{ !show }}" bindtap="hiddenTabs" catchtouchmove="preventTouchMove" class="maskPopup" style="top:{{navBarData.navBarHeight*2}}rpx">
          <van-transition show="{{ show }}" custom-class="block" name="fade-down">
            <view class="popupTabs">
              <view class="selectContent">
                <view wx:for="{{subsume}}" wx:key="index" class="{{subsumeId == index?'currentTab':''}}" bindtap="sieve" data-item="{{item}}">
                  {{item.name}}
                </view>
              </view>
            </view>
          </van-transition>
        </view>
      </view>
    </van-sticky>
    <!-- 筛选 综合 点赞最多 最新发表 -->
    <van-sticky offset-top="{{ navBarData.navBarHeight-1}}" wx:if="{{keyword!=''&&fineList.length!=0}}">
      <view class="selectContent">
        <view wx:for="{{subsume}}" wx:key="index" class="{{subsumeId == index?'currentTab':''}}" bindtap="sieve" data-item="{{item}}">
          {{item.name}}
        </view>
      </view>
    </van-sticky>
    <!-- 发现列表 -->
    <view class="listContent">
      <view class="invitationList backGray">
        <view wx:if="{{fineList.length == 0&&collection.length==0}}" class="noneContent">
          <image src="https://txqmp.cpic.com.cn/uploads/img/noneRedBook.png" />
          <view>暂无内容</view>
        </view>
        <view class="left" wx:if="{{fineList.length != 0||collection.length!=0}}">
          <block wx:if="{{fineList.length == 0&&collection.length!=0}}">
            <view wx:if="{{tabId==0}}" class="invitationItem collectionList" style="background: url(https://txqmp.cpic.com.cn/uploads/img/collectionBg.png) 0 0 no-repeat;background-size: cover;">
              <view class="collectionTitle">
                <image src="../../image/flag.png" />
                <view>热门活动</view>
              </view>
              <block wx:for="{{collection}}" wx:key="list" wx:for-item="list">
                <view style="margin: 0 32rpx 24rpx;" bindtap="goDetail" data-id="{{list.id}}" data-voteEndTime="{{list.voteEndTime}}">
                  #{{list.title}}
                </view>
              </block>
              <view class="activityBtn" bindtap="collectionList">活动广场</view>
            </view>
          </block>
          <block wx:for="{{fineList}}" wx:key="index">
            <view wx:if="{{tabId==0 && collection.length!=0&&index%2==0&&index==0}}" class="invitationItem collectionList" style="background: url(https://txqmp.cpic.com.cn/uploads/img/collectionBg.png) 0 0 no-repeat;background-size: cover;">
              <view class="collectionTitle">
                <image src="../../image/flag.png" />
                <view>热门活动</view>
              </view>
              <block wx:for="{{collection}}" wx:key="list" wx:for-item="list">
                <view style="margin: 0 32rpx 24rpx;" bindtap="goDetail" data-id="{{list.id}}" data-voteEndTime="{{list.voteEndTime}}">
                  #{{list.title}}
                </view>
              </block>
              <view class="activityBtn" bindtap="collectionList">活动广场</view>
            </view>
            <view catch:tap="toInvitationDetail" data-item="{{item}}" class="invitationItem" wx:if="{{tabId==0&&collection.length!=0?index%2==1:index%2==0}}">
              <view wx:if="{{item.ranking}}" class="invitationItem--top">{{item.ranking}}</view>
              <view wx:if="{{ item.isBoutique == '1' }}" class="invitationItem--right" style="{{item.ranking && 'top: 56rpx; border-top-left-radius: 0;'}}">
                <image src="/image/star.png"></image>
                精选
              </view>
              <image class="invitationItem--img" src="{{item.coverImage}}" mode="widthFix" />
              <view class="invitationItem--content">{{item.title}}</view>
              <view class="invitationItem__info">
                <view class="info__left">
                  <!-- salesCommunity  1业务员 0分公司 -->
                  <image wx:if="{{item.category === '玩转太享趣'}}" class="invitationItem--haedImg" src="https://txqmp.cpic.com.cn/uploads/img/qukeTab.png" />
                  <block wx:else>
                    <block wx:if="{{item.salesCommunity == 0}}">
                      <image wx:if="{{item.companyHomePageConfig.headImage}}" data-item="{{item}}" catchtap="toCompanyDetail" class="invitationItem--haedImg" src="{{item.companyHomePageConfig.headImage}}" />
                      <block wx:else>
                        <view data-item="{{item}}" catchtap="toCompanyDetail" class="info--company {{item.nameLength === 2 ? '' : 'company--radius'}}">
                          {{item.companyHomePageConfig.name}}
                        </view>
                      </block>
                    </block>
                    <image wx:if="{{item.salesCommunity == 1}}" data-item="{{item}}" catchtap="toCompanyDetail" class="invitationItem--haedImg" src="{{item.communitySales.headImage}}" />
                  </block>
                  <view class="info--name">
                    {{ item.category === '玩转太享趣' ? '太享趣' : item.createName || ''}}
                  </view>
                </view>
                <view class="info__right">
                  <image catchtap="handleLike" data-like="{{ item.isPoint }}" data-index="{{ index }}" data-id="{{ item.id }}" data-pointNum="{{ item.pointNum }}" data-title="{{ item.title }}" class="invitationItem--likeImg" src="{{item.isPoint ? '../../image/likeActive.png' : '../../image/like.png'}}" />
                  <view class="info--likeNum">{{item.pointNum || 0}}</view>
                </view>
              </view>
            </view>
          </block>
        </view>
        <view class="right" wx:if="{{fineList.length != 0}}">
          <block wx:for="{{fineList}}" wx:key="index">
            <view catch:tap="toInvitationDetail" data-item="{{item}}" class="invitationItem" wx:if="{{tabId==0?collection.length!=0?index%2==0:index%2==1:index%2==1}}">
              <view wx:if="{{item.ranking}}" class="invitationItem--top">{{item.ranking}}</view>
              <view wx:if="{{ item.isBoutique == '1' }}" class="invitationItem--right" style="{{item.ranking && 'top: 56rpx; border-top-left-radius: 0;'}}">
                <image src="/image/star.png"></image>
                精选
              </view>
              <image class="invitationItem--img" src="{{item.coverImage}}" mode="widthFix" />
              <view class="invitationItem--content">{{item.title}}</view>
              <view class="invitationItem__info">
                <view class="info__left">
                  <!-- salesCommunity  1业务员 0分公司 -->
                  <image wx:if="{{item.category === '玩转太享趣'}}" class="invitationItem--haedImg" src="https://txqmp.cpic.com.cn/uploads/img/qukeTab.png" />
                  <block wx:else>
                    <block wx:if="{{item.salesCommunity == 0}}">
                      <image wx:if="{{item.companyHomePageConfig.headImage}}" data-item="{{item}}" catchtap="toCompanyDetail" class="invitationItem--haedImg" src="{{item.companyHomePageConfig.headImage}}" />
                      <block wx:else>
                        <view data-item="{{item}}" catchtap="toCompanyDetail" class="info--company {{item.nameLength === 2 ? '' : 'company--radius'}}">
                          {{item.companyHomePageConfig.name}}
                        </view>
                      </block>
                    </block>
                    <block wx:if="{{item.salesCommunity == 1}}">
                      <image data-item="{{item}}" catchtap="toCompanyDetail" class="invitationItem--haedImg" src="{{item.communitySales.headImage}}" />
                    </block>
                  </block>
                  <view class="info--name">
                    {{ item.category === '玩转太享趣' ? '太享趣' : item.createName || ''}}
                  </view>
                </view>
                <view class="info__right">
                  <image catchtap="handleLike" data-like="{{ item.isPoint }}" data-index="{{ index }}" data-id="{{ item.id }}" data-pointNum="{{ item.pointNum }}" data-title="{{ item.title }}" class="invitationItem--likeImg" src="{{item.isPoint ? '../../image/likeActive.png' : '../../image/like.png'}}" />
                  <view class="info--likeNum">{{item.pointNum || 0}}</view>
                </view>
              </view>
            </view>
          </block>
        </view>
        <view class="footer_text" wx:if="{{fineList.length && fineList.length >= total}}">
          没有更多了
        </view>
      </view>
    </view>
  </view>
</view>
<!-- 发帖权限 -->
<view class="filter_btn" wx:if="{{ tabId!=0 && userType!=1 && (active !='玩转太享趣' || isJurisdiction )   }}">
  <image src="../../image/toEditPost.png" mode="widthFix" bindtap="toEdit" />
</view>
<!-- 以下为废除代码 -->
<!-- 其他option -->
<view hidden="{{!isOther}}">
  <!-- 发现和其他标签 -->
  <view class="invitation_option">
    <view class="invitation_option__item" bindtap="changeOption">
      <view class="item--title">发现</view>
    </view>
    <view class="invitation_option__item">
      <view class="item--title--option">{{currentActivity.shortTitle}}</view>
      <view class="item--line"></view>
    </view>
  </view>
  <!-- 标题&详情 -->
  <view class="invitation">
    <image src="{{currentActivity.backgroundImage}}" class="backImg" />
    <view class="invitation_info">
      <view class="invitation_title">{{currentActivity.title}}</view>
      <view class="invitation_intro">
        <rich-text nodes="{{currentActivity.notes}}" class="ql-editor"></rich-text>
      </view>
    </view>
  </view>
  <!-- 搜索和列表 -->
  <view class="invitationBox">
    <!-- 搜索栏 -->
    <view class="invitationBox__head">
      <view class="invitationBox__head--search" bindtap="toSearch">
        <image class="search--icon" src="../../image/search.png" />
        <view class="search--input"></view>
        <view class="search-placeholder">请输入主题名字</view>
      </view>
      <image bindtap="showFilter" class="invitationBox__head--screen" src="../../image/screen.png" />
    </view>
    <!-- 标签 只有一个以上的标签才展示 -->
    <view class="invitationLabel">
      <view wx:for="{{invitationLabelList}}" wx:key="index" data-item="{{item}}" data-index="{{index}}" class="labelItem {{currentActivity.title === item.title ? 'labelItema--active' : ''}}" bindtap="changeLabelItem">
        {{item.shortTitle || ''}}
      </view>
    </view>
    <!-- 列表 -->
    <view class="invitationList">
      <view bindtap="toInvitationDetail" data-item="{{item}}" class="invitationItem" wx:for="{{optionInvitationList}}" wx:key="index">
        <view wx:if="{{index < 8}}" class="invitationItem--top">TOP{{index + 1}}</view>
        <view wx:if="{{ item.isBoutique == '1' }}" class="invitationItem--right" style="{{item.ranking && 'top: 56rpx; border-top-left-radius: 0;'}}">
          <image src="/image/star.png"></image>
          精选
        </view>
        <image class="invitationItem--img" src="{{item.coverImage}}" mode="widthFix" />
        <view class="invitationItem--content">{{item.title}}</view>
        <view class="invitationItem__info">
          <view class="info__left">
            <image wx:if="{{item.category === '玩转太享趣'}}" class="invitationItem--haedImg" src="https://txqmp.cpic.com.cn/uploads/img/qukeTab.png" />
            <block wx:else>
              <image wx:if="{{item.companyHomePageConfig.headImage}}" data-item="{{item}}" catchtap="toCompanyDetail" class="invitationItem--haedImg" src="{{item.companyHomePageConfig.headImage}}" />
              <block wx:else>
                <view wx:if="{{item.nameLength === 2}}" data-item="{{item}}" class="info--company" catchtap="toCompanyDetail">
                  {{item.companyHomePageConfig.name}}
                </view>
                <view wx:else data-item="{{item}}" catchtap="toCompanyDetail" class="info--company company--radius">
                  {{item.companyHomePageConfig.name}}
                </view>
              </block>
            </block>
            <view class="info--name">
              {{ item.category === '玩转太享趣' ? '太享趣' : item.createName || ''}}
            </view>
          </view>
          <view class="info__right">
            <image class="invitationItem--likeImg" src="{{item.isPoint ? '../../image/likeActive.png' : '../../image/like.png'}}" />
            <view class="info--likeNum">{{item.pointNum || 0}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
<!-- 筛选 -->
<van-popup show="{{ popupShow }}" position="bottom" closeable bind:close="onClose" class="popup" close-on-click-overlay="{{ false }}" custom-style=" width: 100%;height: 800rpx;border-radius: 32rpx 32rpx 0px 0px;">
  <van-dropdown-menu style="width: 100%;margin-top: 50rpx;" overlay="{{false}}" close-on-click-outside="{{ false }}" popup-style="background:#000">
    <van-dropdown-item id="item" title="机构">
      <view class="cropCheck">
        <van-cell-group>
          <van-cell title="全部" bindtap='handleCheckAll'>
            <!-- <text>全部</text> -->
            <van-checkbox slot="right-icon" use-icon-slot value="{{ checkAll }}">
              <van-icon slot="icon" name="{{checkAll? 'success' : ''}}" color="#f82521" class="check_icon" />
            </van-checkbox>
          </van-cell>
          <van-cell wx:for="{{ cropOptions }}" wx:key="id" title="{{ item.name }}" bindtap="handleCheck" data-index="{{index}}">
            <!-- <text> {{ item.name }}</text> -->
            <van-checkbox slot="right-icon" use-icon-slot class="check_item" name="{{ item.id }}" value="{{ item.check }}">
              <van-icon slot="icon" name="{{item.check? 'success' : ''}}" color="#f82521" class="check_icon" />
            </van-checkbox>
          </van-cell>
        </van-cell-group>
      </view>
    </van-dropdown-item>
    <van-dropdown-item id="item" title="标签">
      <view class="cropCheck">
        <van-cell-group>
          <van-cell title="全部" bindtap='handleCheckAllLabel'>
            <!-- <text>全部</text> -->
            <van-checkbox slot="right-icon" use-icon-slot value="{{ checkAllLabel }}">
              <van-icon slot="icon" name="{{checkAllLabel? 'success' : ''}}" color="#f82521" class="check_icon" />
            </van-checkbox>
          </van-cell>
          <van-cell wx:for="{{ labelList }}" wx:key="id" title="{{ item.name }}" bindtap="handleLabelCheck" data-index="{{index}}">
            <!-- <text> {{ item.name }}</text> -->
            <van-checkbox slot="right-icon" use-icon-slot class="check_item" name="{{ item.id }}" value="{{ item.check }}">
              <van-icon slot="icon" name="{{item.check? 'success' : ''}}" color="#f82521" class="check_icon" />
            </van-checkbox>
          </van-cell>
        </van-cell-group>
      </view>
    </van-dropdown-item>
    <van-dropdown-item id="item2" title="排序">
      <view class="cropCheck">
        <van-radio-group value="{{ order }}">
          <van-cell-group>
            <van-cell wx:for="{{ orderOptions }}" wx:key="value" title="{{ item.text }}" bind:tap="onClick" data-item="{{ item }}">
              <van-radio slot="right-icon" use-icon-slot name="{{ item.value }}" clickable>
                <van-icon slot="icon" name="{{item.value === order? 'success' : ''}}" color="#f82521" class="check_icon" />
              </van-radio>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </view>
    </van-dropdown-item>
  </van-dropdown-menu>
</van-popup>