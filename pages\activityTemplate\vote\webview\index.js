const app = getApp();
const API = app.require('/api/request')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    src: null,
    localWeb: null
  },

  onShow() {
    // wx.hideHomeButton()
  },

  onLoad(options) {
    console.log(15, options)
    console.log(15, decodeURIComponent(options.src))
    this.setData({
      id: options.id,
      src: decodeURIComponent(options.src)
    })

    this.getLocalweb()
  },
  async getLocalweb(){
    let res = await API.getfindByCode({code: "local_url"})
    this.setData({localWeb: res.data.data})
  },
  onUnload() {
    // // 判断当前页面是否是领取成功页面 如果不是则跳转到领取成功页面
    console.log(this.data.src)
    console.log(this.data.localWeb)
    // if(!this.data.src.startsWith(this.data.localWeb)){
    //   wx.navigateTo({
    //     url: '/pages/activityTemplate/vote/webview/index?src=' + encodeURIComponent(`${this.data.localWeb}#/results?activityType=VOTE&activityId=${this.data.id}`)
    //   })
    // }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})