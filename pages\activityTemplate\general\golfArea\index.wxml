<view class="content">
  <view class="header" bindtap="selectArea">
    <view>选择场馆</view>
    <view style="{{titleText.styleStr}}" class="societyName">{{society}}</view>
    <van-icon name="arrow" color="#7E849F" class="arrow"/>
  </view>
  <view class="calendar">
    <calendar spotMap="{{spotMap}}" fullMap="{{fullMap}}" venueId="{{venueId}}"  bind:selectDay="selectDay" bind:getDateList="getDateList" defaultTime="{{defaultTime}}"></calendar>
  </view>
  <view class="time">
    <view class="title">选择时段</view>
    <block wx:if="{{currentperiodList.length!=0}}">
      <van-radio-group   class="period" value="{{ timeIndex }}" bind:change="seletPeriod" >
        <block wx:for="{{currentperiodList}}" class="periods" wx:key="index">
          <van-radio name="{{index}}"  checked-color="#FF5030" disabled="{{item.place == 0||!item.canAppoint}}"
          class="timeItem">
            <text style="{{timeText.styleStr}}">{{item.golfPeriod}}</text>
            <view wx:if="{{item.place == 0}}" class="full">约满</view>
            <view wx:if="{{!item.canAppoint}}" class="full">不可约</view>
          </van-radio>
         
        </block>
      </van-radio-group>
    </block>
    <block wx:else>
      <view class="showText">当前场地暂无可预约时间~</view>
    </block>
  </view>
  <view class="footer">
    <view class="place" style="{{gaokeNumber.styleStr}}">剩余数量：<text class="number">{{place}}</text></view>
    <view class="btn" bindtap="tobuy" style="{{button.styleStr}}">{{button.propValue}}</view>
  </view>
  
</view>
<contact module="活动参与" pageName="选择场馆预约场次" pageUrl="{{pageUrl}}" businessData="{{activity}}"></contact>
