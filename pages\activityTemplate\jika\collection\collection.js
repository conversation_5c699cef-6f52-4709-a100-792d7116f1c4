const http = require('../../../../utils/util.js');
const wxtimer = require('../../../../utils/time.js');
const app = getApp();
const API = require('../../../../api/request.js')
const CryptoJS = require('../../../../utils/CryptoJs')
const { showTab, handleUserStorage } = require('../../../../utils/aboutLogin')

const { setSalesCode } = require('../../../../utils/querySalesCode');
Page({

  /**
   * 页面的初始数据
   */
  data: { 
    isShowBtn:true,
    pageUrl:'pages/activityTemplate/jika/collection/collection',
    rejectReason:'',
    showReject:false,
    linkType:'jika',
    showPoster:false,
    btnText: '按钮',
    showPop: true,
    buttonStyle: '',
    cishu: 1,
    audit:0,

    // 活动Id
    activityId: null,
    timeObj: {},
    isShow: false, //是否显示
    height: 0,
    list: [],
    isClose: true,
    showModalDlg: false,
    showModalDlg1: false,
    showModalDlg3: false,
    showModalDlg2: false,
    leftNum: 0, // 抽奖的次数,
    isHeCheng: false,
    ischoujiang: false,
    type: 1,//1.点击抽奖 2.点击赠送
    isShowImg: true,
    showModalDlg4: false,
    zhulicishu: 1,
    isShare: false,
    showSwitchSale: false,
    changeSalesCode: '',
    currentId: '',
    acceptId: '',
    isShareFlag: false,
    shareUserType: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const that = this
    let yy = new Date().getFullYear()
    let mm = new Date().getMonth() + 1
    let dd = new Date().getDate()
    let ymd = "" + yy + mm + dd
    console.log(options, 'options');
    // 节日弹框
    let expiration = wx.getStorageSync('holiday' + options.activityId)
    // console.log(expiration, 'expiration12345');
    // var timestamp=Date.parse(new Date());//拿到现在时间
    if (expiration == ymd) { //缓存已过期
      //wx.clearStorageSync()
      that.setData({
        holiday: 1
      })
    } else {
      that.setData({
        holiday: 2
      })
    }

    if (options.isShare) {
      this.setData({
        isShare: true,
        changeSalesCode: options.salesCode,
        changeSaleName: options.saleName,
        shareUserType: options.userType,
        isShareFlag: true
      })
    }

    let date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    if (month < 10) {
      month = "0" + month;
    }
    if (day < 10) {
      day = "0" + day;
    }
    var nowDate = year + "-" + month + "-" + day;
    let scene = options.scene
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      var id = option[0]
      that.setData({
        activityId: id,
      })

    } else {
      this.setData({ activityId: options.activityId,audit:options.audit})
    }
    this.setData({
      nowDate,
      shareUserId: options.shareUserId
    })
    let userId = wx.getStorageSync('userId')

    console.log(options.activityId != null, 'options.activityId != null');
    console.log(this.data.activityId);

    // 赠送
    if (options.activityId != null && options.cardId != null && options.cradKaIds != null) {
      this.getCollectDetail(options.activityId)
      that.setData({
        activityId: options.activityId,
        cardIdLQ: options.cardId,
        cradKaIds: options.cradKaIds //赠卡id
       
      })
    }
    // 帮忙助力
    if (options.shareId != null && options.eventCode != null) {
      that.setData({
        eventCode: options.eventCode,
        shareId: options.shareId,
        shareToken: options.shareToken,
        zhulicishu: 1
      })
    }

    // if (http.userAre() == '') { // 用户未登录
    //   // 用户id判断用户是否登录
    //   that.setData({

    //     showModalDlg: true,
    //     height:app.globalData.height
    //   })

    //   return
    // }else{
    // //   that.getIsRegister(userId)
    //   if(wx.getStorageSync('refreshUserInfo')){
    //     that.getUserInfo();
    //   }else{
    //     if(wx.getStorageSync('salesCode')){
    //         that.setData({
    //             type: wx.getStorageSync('userType'),
    //             salesCode: wx.getStorageSync('salesCode'),
    //             idCard:wx.getStorageSync('idCard')?wx.getStorageSync('idCard') : null,
    //             userType: wx.getStorageSync('userType'),
    //             credits: wx.getStorageSync('credits'),
    //             userInfoSuccess: true,
    //         })
    //     }
    //     that.getIsRegister()
    //   }
    //   that.setData({
    //     showModalDlg: false,
    //     height:app.globalData.height
    //   })
    //   // this.getCollectDetail(this.data.activityId)

    // }


  },

  closeChangeTieModal () {
    this.setData({ showSwitchSale: false })
  },
  // 关闭专属海报弹框
  onClickHide(){
    this.setData({
      showPoster: false
    })
  },
  // 下载海报
  downPoster(){
    this.setData({
      showPoster: true
    })
  },
  reject(){
    this.setData({
      showReject:true,
      rejectReason:''
    })
  },
  // 助力事件
  async onToAccept4 () {
    const that = this
    await API.cardCollectEvent({
      activityId: that.data.activityId,
      eventCode: 'FRIENDS_HELP',
      shareId: that.data.shareId
    }).then(res => {
      that.setData({
        zhulicishu: 2
      })
      console.log(res, '助力结果');
      if (res.data.code == 200) {
        wx.showToast({
          title: '助力成功',
          icon: 'none'
        })
        that.onBuriedPoint('onHelp', '助力')

      } else {
        wx.showToast({
          title: res.data.message,
          icon: 'none'
        })
      }
      that.setData({
        showModalDlg4: false
      })

    })
  },
  //活动详情
  async getCollectDetail (activityIds) {
    const that = this
    let res = null
    if(this.data.audit){
      res =  await API.auditCollectDetail({id: activityIds})
      this.setData({
        isShowBtn:false,
      })
    }else{
      res =  await API.cardCollectDetail({activityId: activityIds})
    }
    console.log(res, 'res1221123456');
    if (res.data.code == 200) {
     
      //助力
      if (that.data.shareId != null && that.data.eventCode != null) {
        if(this.data.audit) return
        let token = wx.getStorageSync('token')
        if (that.data.shareToken == token) {
          wx.showToast({
            title: '不能给自己助力',
            icon: 'none'
          })
        } else {
          if (that.data.zhulicishu == 1) {
            that.setData({
              showModalDlg4: true,
            })
          }
        }
      }
      let { data } = res.data
     
      let composeCardConfig = JSON.parse(data.composeCardConfig)
      // console.log(JSON.parse(data.activity.activityPageConfig), '抽奖奖品');
        var reg=new RegExp('TXQImgPath/','ig');
        let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
        let activityData  = data.activity
        let activityPageData = activityData.activityPageConfig&&activityData.activityPageConfig.replace(reg,dynamicDomainName)
        // console.log(activityData,activityPageData);
      const activityPageConfig =activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '集卡首页') : null
      const dialogPageConfig = activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '抽卡/合卡') : null
      const festivalPageConfig =activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '节日赠卡') : null
      const presentPageConfig =activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '收下赠卡') : null
      const helpPageConfig =activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '好友助力') : null
      const activityPosterConfig = activityPageData ? JSON.parse(activityPageData).find((item) => item.title === '专属海报') : null
      if(activityPosterConfig !== undefined && activityPosterConfig !== null){
        (activityPosterConfig.componentData ? activityPosterConfig.componentData : []).map((item) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
          if (item.type === 'button') {//处理圆角
            let radius = typeof (item.style.borderRadius)
            if (radius === 'string') {
              const newRadius = item.style.borderRadius.split(' ').map(item => {
                return item * 2 + 'rpx'
              })
              item['styleStr'] = item['styleStr'] + `border-radius:${newRadius.join(' ')}`
            }
          }
        })
        const posterBackgroundImg = activityPosterConfig.componentData.find((item) => item.type === 'posterPic')
        const posterText = activityPosterConfig.componentData.find((item) => item.type === 'text')
        that.setData({
          posterBackgroundImg,
          activityPosterConfig,
          posterText
        })
      }
      if (activityPageConfig !== undefined && activityPageConfig !== null) {
        (activityPageConfig.componentData ? activityPageConfig.componentData : []).map((item) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
            if (item.btEvent == '赠送') {
              that.setData({
                buttonStyle: Object.assign(item.style, { propValue: item.propValue })
              })
            }
          }
          if (item.type == 'cardCollectingTime') {
            item.style = {
              ...item.style,
              color: item.style.textColor
            }

          }
          if (item.type == 'backgroundImg') {
            if (composeCardConfig.isDraw == 0) {
              if (typeof (item.style.height) == 'number') {
                item.style.height = item.style.height - 200
              }
            } else {
              if (typeof (item.style.height) == 'number') {
                item.style.height = item.style.height - 100
              }
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
          if (item.type === 'button'||item.type==='poster') {//处理圆角
            let radius = typeof (item.style.borderRadius)
            if (radius === 'string') {
              const newRadius = item.style.borderRadius.split(' ').map(item => {
                return item * 2 + 'rpx'
              })
              item['styleStr'] = item['styleStr'] + `border-radius:${newRadius.join(' ')}`
            }
          }
          if (item.type == 'cardCollectingPrize') {
            item['styleStr'] = item['styleStr'] + `width:702rpx;left:50%;background-image:url(${item.imgOptions.url})`
            if (composeCardConfig.isDraw == 1&&!this.data.audit) {
              let params = {
                id: composeCardConfig.drawId,
                drawMode: 'COLLECT',
                qtnId: this.data.activityId
              }
              API.getNewDrawInfo(params).then(response => {
                if (response.data.code === 200) {
                  var reg=new RegExp('TXQImgPath/','ig');
                  let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
                  let drawPrizesData  =  JSON.stringify(response.data.data.drawConfig.drawPrizes)
                  let prizeData = JSON.parse(drawPrizesData.replace(reg,dynamicDomainName))
                  let rewardImg = [] //活动奖品列表
                  for (var i = 0; i < prizeData.length; i++) {
                    if (prizeData[i].rightsSrc != 'NOTHING') {
                      rewardImg.push({ id: prizeData[i].id, name: prizeData[i].prizeName, url: prizeData[i].url, value: '' })
                    }
                  }
                  rewardImg.map((list, key) => {
                    item.cardCollectingPrizeList.map((prize, idx) => {
                      if (list.id == prize.id) {
                        list.value = prize.value
                      }
                    })

                  })
                  that.setData({
                    rewardImg
                  })
                }
              })
            }
            if(composeCardConfig.isDraw == 1&&this.data.audit){
              let prizeData = data.prizes
              let rewardImg = [] //活动奖品列表
              for (var i = 0; i < prizeData.length; i++) {
                if (prizeData[i].rightsSrc != 'NOTHING') {
                  rewardImg.push({ id: prizeData[i].id, name: prizeData[i].prizeName, url: prizeData[i].url, value: '' })
                }
              }
              rewardImg.map((list, key) => {
                item.cardCollectingPrizeList.map((prize, idx) => {
                  if (list.id == prize.id) {
                    list.value = prize.value
                  }
                })

              })
              that.setData({
                rewardImg
              })
            }
          }
        })
        // console.log('页面组件元素',dialogPageConfig)
        that.setData({
          componentData: activityPageConfig.componentData,
          activityPageConfig: activityPageConfig,
        })
      }
      if ((presentPageConfig !== undefined && presentPageConfig !== null) || (festivalPageConfig !== undefined && festivalPageConfig !== null) || (dialogPageConfig !== undefined && dialogPageConfig !== null)) {
        dialogPageConfig && dialogPageConfig.componentData.map((item, key) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        presentPageConfig && presentPageConfig.componentData.map((item, key) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        festivalPageConfig && festivalPageConfig.componentData.map((item, key) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        that.setData({
          presentPageConfig,
          festivalPageConfig,
          dialogPageConfig
        })
      }

      if (helpPageConfig !== undefined && helpPageConfig !== null) {
        helpPageConfig.componentData.map((item, key) => {
          if (item.type === 'button') {
            let backgroundImage = ''
            if (item.bgStatus == 1) {
              backgroundImage = `url(${item.imgOptions.url})`
              item.style.backgroundColor = ''
            }
            item.style = {
              ...item.style,
              backgroundImage
            }
          }
          item['styleStr'] = that.handlerStyle(item.style)
        })
        that.setData({
          helpPageConfig
        })
      }
      let ischoujiang = false
      if (composeCardConfig.isDraw == 1 && data.merged == true) {
        ischoujiang = true
      } else {
        ischoujiang = false
      }
      let flag = data.cardCfgs.every(item => item.cardHold >= 1)
      console.log(flag, 'flag合成', data.cardCfgs);

      let chang = data.cardCfgs.length
      let lengs = 0

      if (chang > 7) {
        lengs = 466
      } else if (chang >= 6 && data.merged == true) {
        lengs = 466
      } else {
        lengs = 252
      }
      console.log('data321654', data);
  

      that.setData({
        hasDraw: data.hasDraw,
        // isShowImg: flag,//是否显示合成按钮
        // activity: data.activity,
        applyStartTime: data.activity.applyStartTime,//? '2022-02-14 14:30:57': data.applyStartTime,
        applyEndTime: data.activity.applyEndTime,// ? '2022-02-15 14:30:57': data.applyEndTime,
        backgroundImg: data.backgroundImg,// '../../../image/imgjf_footer.png',
        coverImg: data.coverImg,  //'../../../image/imgjf_top.png',
        leftNum: data.leftNum, // 剩余抽卡次数
        loginNum: data.loginNum, // 登录获取的抽奖次数
        giveNum: data.giveNum, //赠送获得的抽奖次数
        shareNum: data.shareNum, //	分享获得的抽奖次数
        cardCfgs: data.cardCfgs, //卡片
        lengs: lengs,
        merged: data.merged, // 是否合成
        composeCardConfig: composeCardConfig,
        isSpecialDate: data.isSpecialDate,//指定日期
        currentDate: data.specialDates,// 指定日期 
        ischoujiang,
        mergeCards: data.mergeCards,
        drawId: composeCardConfig.drawId,
        title: data.activity.title,
        flagPoster: data.activity.flagPoster,
        qrcode: data.qrcode,
        title: data.activity.title,
        giveText: data.giveText,
        helpText: data.shareText,
        shareText: data.activity.shareText,
        numberBackgroundColor: data.backgroundColor,
        numberColor: data.numberColor,
        financeStatus: data.activity.financeStatus||'',
        auditStatus: data.activity.auditStatus||'',



      })
      console.log(that.data.cardIdLQ != null, 'that.data.cardIdLQ');
      if (that.data.merged) {
        console.log(that.data.composeCardConfig, 'composeCardConfig')
      }
      // 领卡
      if (that.data.cardIdLQ != null && that.data.cradKaIds != null) {
        let imglq = ''
        data.cardCfgs.filter(item => {
          if (that.data.cardIdLQ == item.id) {
            console.log(item, 'item.id');
            imglq = item.cardImg
          }
        })
        that.setData({
          imgjk: imglq
        })
        if (imglq) {
          that.setData({
            showModalDlg1: true
          })
        }
      }
      //  else {
      //   that.setData({
      //     showModalDlg1: false
      //   })
      // }
      // 指定日期

      if (that.data.isSpecialDate == 1 && that.data.currentDate.length > 0) {
        if (that.data.holiday != 1) {
          that.data.currentDate.filter(item => {
            console.log(item.specialDate.substring(0, 10) == that.data.nowDate, 'item.specialDate.substring(0, 10) == that.data.nowDate');

            if (item.specialDate.substring(0, 10) == that.data.nowDate && that.data.audit!=1) {
              let imglq = item.specialDateImg
              that.setData({
                showModalDlg2: true,
                imgday: imglq,
                specialDateNum: item.specialDateNum
              })

            }
          })
        }
      }

      console.log(that.data.applyStartTime, that.data.applyEndTime);
      let applyStartTime = that.data.applyStartTime
      let applyEndTime = that.data.applyEndTime
      //   // 当前时间
      let nowtime = new Date().getTime() //目前时间
      let createTime = new Date(applyStartTime.replace(/-/g, '/')).getTime()  // 活动开始时间时间戳
      let endTime = new Date(applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒
      console.log(nowtime, createTime, endTime, 999)
      let sub_time1 = parseInt((nowtime - createTime) / 1000) //正数： 活动开始  负数：活动开始倒计时
      let sub_time2 = parseInt((endTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束
      console.log(sub_time1, sub_time2, 998)
      that.setData({
        sub_time1,
        sub_time2
      })
      var subtime = ""
      if (sub_time1 < 0) {
        that.setData({
          isShow: false
        })
        subtime = parseInt((createTime - nowtime) / 1000)  //活动开始倒计时
      }
      console.log(subtime, 'subtime');
      if (sub_time2 < 0) {
        that.setData({
          isShow: false
        })
        wx.showToast({
          title: "活动已结束,请下次参与！",
          icon: "none",
          duration: 2500,
        })
      }
      if (sub_time1 > 0 && sub_time2 > 0) {
        that.setData({
          isShow: true
        })
        subtime = parseInt((endTime - nowtime) / 1000) //进行中  结束倒计时

      }
      console.log(subtime, 'subtime2');

      let timer = new wxtimer({
        complete: function () {
          if (sub_time1 < 0) { // 活动开始倒计时
            clearInterval(timer)
          } else if (sub_time1 > 0 && sub_time2 > 0) {

            wx.showModal({
              title: '提示',
              content: '活动已结束',
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  wx.navigateBack({
                    delta: 1
                  })
                }
              }
            })
          }
        }
      });
      //计时器   
      timer.start(that, subtime, false);

    } else {
      wx.showToast({
        title: res.message,
        icon: "none"
      })
    }

    if (this.data.isShareFlag) {
      this.setData({ isShareFlag: false })
      this.changeOfBusinessRepresentative()
    }
  },

  handlerStyle (style) {
    // console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  // 变更业务员
  changeOfBusinessRepresentative () {
    let salesCode = wx.getStorageSync('salesCode')
    let saleName = wx.getStorageSync('saleName')
    if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
      this.setData({ showSwitchSale: true, currentId: saleName + salesCode, acceptId: this.data.changeSaleName + this.data.changeSalesCode })
    }
  },

  // 赠卡被领取
  async onToAccept2 () {
    const that = this
    let recordId = that.data.cradKaIds
    this.onBuriedPoint('onReceive', '点击领取')
    await API.cardCollectReceive({
      recordId
    }).then(res => {
      if (res.data.code == 200) {
        that.getCollectDetail(that.data.activityId)
      } else {
        wx.showToast({
          title: res.data.message,
          icon: 'none',
          duration: 4000,
        })
      }
    })
    this.setData({
      cardIdLQ: '',
      showModalDlg1: false
    })
  },
  // closeImg
  closeImg () {
    this.setData({
      showModalDlg1: false
    })
  },
  userInfoHandler () {
    console.log('执行了用户微信登录')
    const that = this
    app.getUserInfo(function (userInfo) {
      console.log(userInfo, 'userInfo');
      wx.setStorageSync('userId', userInfo.id);
      wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
      wx.setStorageSync('nickName', userInfo.nikename);
      wx.setStorageSync('openId', userInfo.openId);
      wx.showToast({
        title: '授权成功',
        icon: "none",
        duration: 1500,
      })
      that.setData({
        userId: userInfo.id,
        isAuthorize: true
      })
      if (wx.getStorageSync('userId') !== '') {
        console.log('判断是否注册过')
        // 判断是否注册过    
        that.getUserInfo()
      }
    })

  },
  getUserInfo () {
    let that = this;
    var userId = wx.getStorageSync("userId")
    var token = wx.getStorageSync("token")
    // 获取用户所属门店信息
    if (token) {
      API.getInfoById()
        .then(res => {
          if (res.data.code == 200) {
            console.log('获取用户信息', res);
            console.log(CryptoJS.Decrypt(res.data.data.salesCode));
            // wx.setStorageSync('phonebtn', CryptoJS.Decrypt(res.data.data.phone));
            // wx.setStorageSync('userType', res.data.data.type);
            // wx.setStorageSync('salesCode', CryptoJS.Decrypt(res.data.data.salesCode))
            // wx.setStorageSync('saleName', CryptoJS.Decrypt(res.data.data.salesName));
            // wx.setStorageSync('manager',res.data.data.manager)
            // wx.setStorageSync('supId',res.data.data.supId)
            // wx.setStorageSync('corpName',res.data.data.companyName)

            handleUserStorage(res.data.data)
            console.log('用户信息业务员账号', wx.getStorageSync('salesCode'));
            if (wx.getStorageSync('userId') !== '') {
              // 判断是否注册过    
              that.getIsRegister(wx.getStorageSync('userId'))
            }
          }
        })
    }
    if (wx.getStorageSync("token")) {
      that.setData({
        phonebtn: true,
      })
    } else {
      that.setData({
        phonebtn: false,
        tobind: false
      })
    }
  },
  // 查询是否注册
  getIsRegister (userId) {
    console.log('查询是否注册');
    let that = this
    if (http.unRegister()) {
      // 没有注册
      // 查询绑定的业务员
      that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode,that.data.changeSaleName)
      that.setData({
        personLogin: true,
        showModalDlg: false
      })
    } else {
      showTab()
      that.setData({
        showModalDlg: false
      })

    }
    console.log('personLogin', that.data.personLogin);
    console.log('showModalDlg', that.data.showModalDlg);

    that.getCollectDetail(that.data.activityId)
  },
  // 绑定分享人
  getSalesInfo () {
    console.log('绑定业务员');
    let that = this
    let data = {
      id: that.data.shareUserId
    }
    API.getSalesInfo(data).then(res => {
      console.log(res)
      if (res.data.code == '200') {
        that.setData({
          saleId_num: res.data.data.salesCode
        })
        that.salenumber(res.data.data.salesCode)
      } else {
        wx.showToast({
          title: res.data.message,
          duration: 2000,
          icon: "none",
        })
      }
    })
  },
  // selectGive(e){
  //   console.log(e.currentTarget.dataset.index, 'ee');
  //   let nums =e.currentTarget.dataset.index
  //   const that = this
  //   that.data.giveList.reverse().filter((item,index) => {
  //     if(index == nums){
  //       item.active = true
  //     }else{
  //       item.active = false
  //     }
  //   });
  //   that.setData({
  //     giveList: that.data.giveList
  //   })
  // },
  //业务员查询
  salenumber (saleId) {
    console.log('业务员查询');
    var that = this
    if (!saleId) {
      return
    }
    let data = {
      code: CryptoJS.Encrypt(saleId)
    }
    API.getSaleInfoByCode(data).then(res => {
      if (res.data.code == 200) {
        if (res.data.data !== null) {
          let name = CryptoJS.Decrypt(res.data.data.name)
          let code = CryptoJS.Decrypt(res.data.data.code)
          that.setData({
            saleIdnum: true,
            saleId_num: code,
            saleName: name,
          })
          wx.setStorageSync('salesId_num', res.data.data.code)
          wx.setStorageSync('salesName', res.data.data.name)

        } else {
          that.setData({
            saleIdnum: false,
            saleName: '',
          })
          wx.setStorageSync('salesId_num', '')
          wx.setStorageSync('salesName', '')
        }
      } else {
        wx.showToast({
          title: res.data.message,
          duration: 2000,
          icon: "none",
        })
      }
    })

  },
  preview (event) {
    console.log(event.currentTarget.dataset.src, 'event');
    wx.previewImage({
      current: event.currentTarget.dataset.src,
      urls: [event.currentTarget.dataset.src],
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },
  goBack () {
    console.log("返回");
    wx.switchTab({
      url: "/pages/home/<USER>",
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    const that = this
    if (http.userAre() == '') { // 用户未登录
      // 用户id判断用户是否登录
      that.setData({

        showModalDlg: true,
        height: app.globalData.height
      })

      return
    } else {
      //   that.getIsRegister(userId)
      if (wx.getStorageSync('refreshUserInfo')) {
        that.getUserInfo();
      } else {
        if (wx.getStorageSync('salesCode') || wx.getStorageSync('pcode')) {
          that.setData({
            type: wx.getStorageSync('userType'),
            salesCode: wx.getStorageSync('salesCode'),
            idCard: wx.getStorageSync('idCard') ? wx.getStorageSync('idCard') : null,
            userType: wx.getStorageSync('userType'),
            credits: wx.getStorageSync('credits'),
            userInfoSuccess: true,
          })
        }
        that.getIsRegister()
      }
      that.setData({
        showModalDlg: false,
        height: app.globalData.height
      })
      // this.getCollectDetail(this.data.activityId)

    }
  },



  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },
  // 赠送
  // onGiveImg(e){
  //   console.log(e,'onGiveImg');

  // },

  /**
   * 用户点击右上角分享
   */
  async onShareAppMessage (e) {
    if(this.data.audit) return
    const that = this
    console.log(e, 'resonShareAppMessage');
    let titles = ''
    let paths = ''
    let nickName = wx.getStorageSync('nickName')
    const shareUserId = wx.getStorageSync('userId')
    let salesCode = wx.getStorageSync('salesCode')
    let saleName = wx.getStorageSync('saleName')
    let userType = wx.getStorageSync('userType')

    if (e.target != null) {
      let cardId = e.target.dataset.cardid
      console.log(cardId, 'cardId 赠送');
      let cardName = ''
      that.data.cardCfgs.forEach(item => {
        if (item.id == cardId) {
          cardName = item.cardName
        }
      })
      if (e.target.dataset.cardid != null) {
        // 赠送
        console.log(that.data.activityId, 'that.data.activityId');
        console.log(cardId, 'cardid');
        let res = await API.cardCollectSend({ activityId: that.data.activityId, cardId: cardId })
        console.log(res, '赠卡')
        if (res.data.code == 200) {
          this.onBuriedPoint('onGive', '赠送')
          var result = this.data.giveText.replace('#微信昵称#', nickName);
          // var title = result.replace('#活动名称#', this.data.title);
          titles = result
          paths = '/pages/activityTemplate/jika/collection/collection?cardId=' + cardId + '&activityId=' + that.data.activityId + '&cradKaIds=' + res.data.data + '&clientShareUserId' + shareUserId
          that.setData({
            cradKaIds: res.data.data
          })
          that.getCollectDetail(that.data.activityId)
        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none'
          })
          return
        }
        this.setData({
          showModalDlg1: false
        })
      } else if (e.target.dataset.zhuli != null) {
        console.log(zhuli, 'zhuli 助力');
        let zhuli = e.target.dataset.zhuli
        let token = wx.getStorageSync('token')
        await API.cardCollectEvent({
          activityId: that.data.activityId,
          eventCode: zhuli
        }).then(res => {

          if (res.data.code == 200) {
            console.log(res.data.data, '助力分享');
            that.onBuriedPoint('helpShare', '助力分享')
            var result = this.data.helpText.replace('#微信昵称#', nickName);
            // var title = result.replace('#活动名称#', this.data.title);
            titles = result
            paths = '/pages/activityTemplate/jika/collection/collection?eventCode=FRIENDS_HELP&activityId=' + that.data.activityId + '&shareId=' + res.data.data + '&shareToken=' + token + '&shareUserId=' + shareUserId + '&clientShareUserId' + shareUserId
          } else {
            wx.showToast({
              title: res.data.message,
              icon: 'none'
            })
            return
          }
        })
      }
    } else {
      console.log('右上角分享');
      let share = 'ACTIVITY_SHARE'
      this.onBuriedPoint('shareJiFu', '分享')
      var result = this.data.shareText.replace('#微信昵称#', nickName);
      var title = result.replace('#活动名称#', this.data.title);
      titles = title
      paths = '/pages/activityTemplate/jika/collection/collection?eventCode=' + share + '&activityId=' + that.data.activityId + '&shareUserId=' + shareUserId + '&clientShareUserId' + shareUserId

    }
    return {
      title: titles,
      path: `${paths}&salesCode=${salesCode}&saleName=${saleName}&isShare=1&userType=${userType}`,
    }

  },
  // 助力和分享 API
  async shareOrHelp (eventCodes, shareIds) {
    const that = this
    let data = {}
    if (shareIds != null) {
      data.shareId = shareIds
    }
    data.activityId = that.data.activityId,
      data.eventCode = eventCodes

    await API.cardCollectEvent(data).then(res => {
      console.log(res, 'shareOrHelp12');
      if (res.data.code == 200) {
        that.getCollectDetail(that.data.activityId)
      } else {
        wx.showToast({
          title: res.data.message,
          icon: 'none'
        })
      }
    })
  },
  // 赠送API
  async cardCollectSend (cradIds) {
    const that = this
    await API.cardCollectSend({
      activityId: that.data.activityId,
      cardId: cradIds
    })
      .then(res => {
        console.log(res, 'cardCollectSend123456');
        console.log(res.data.data, 'res.data.data1111');
        if (res.data.code == 200) {

          that.setData({
            cradKaIds: res.data.data
          })
          that.getCollectDetail(that.data.activityId)
        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none'
          })
          return
        }
      })
  },
  // 埋点事件
  onBuriedPoint (name, title) {
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let time = new Date().toLocaleString()
    app.sensors.track(name, {
      name: title,
      personScan: nickName,
      scanTime: time,
      userId: userId
    });
  },
  // 点击抽卡
  onCard () {
    // console.log(111);
    const that = this
    let num = that.data.leftNum
    if(this.data.audit) return
    if (!this.data.isShow) {
      return wx.showToast({
        title: '当前时间不在活动期间，无法参加活动',
        icon: "none"
      })
    }

    if (num <= 0) {
      wx.showToast({
        icon: 'none',
        title: "您当日已无抽卡机会，请尝试赠卡或邀请好友助力获取额外抽卡机会吧~”"
      })
      return
    } else {
      // 埋点
      this.onBuriedPoint('onDrawCard', '点击抽卡')

      // 抽卡
      API.cardCollectTrigger({
        activityId: that.data.activityId
      }).then(res => {
        // console.log(res, 'data123456');
        if (res.data.code == 200) {
          let { data } = res.data
          if (data.cardId != null) {
            // data.cardId = 15
            that.data.cardCfgs.forEach(item => {
              if (item.id == data.cardId) {
                item.cardHold = 1 + item.cardHold
                that.data.imgjk = item.cardImg
              }
            })
            that.setData({
              showModalDlg1: true,
              cardCfgs: that.data.cardCfgs,
              imgjk: that.data.imgjk
            })
            // 刷新接口
            that.getCollectDetail(that.data.activityId)
          } else {
            wx.showToast({
              title: '抱歉没有抽中',
              icon: 'none'
            })
          }
          that.setData({
            leftNum: data.leftNum
          })

        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none'
          })
        }
      })


    }
  },
  // 抽卡
  onToAccept () {
    if(this.data.audit) return
    const that = this
    this.onBuriedPoint('onReceive', '点击抽卡领取')
    let flag = that.data.cardCfgs.every(item => item.cardHold >= 1)
    console.log(flag, 'flag合成');

    that.setData({
      showModalDlg1: false,
      // isShowImg: flag
    })

  },
  onToAccept3 () {
    if(this.data.audit) return
    const that = this
    that.onBuriedPoint('onHoliday', '节日弹窗')
    var timestamp = Date.parse(new Date());
    var expiration = timestamp + 86400000;//1000*60*60*24（一天）
    let yy = new Date().getFullYear()
    let mm = new Date().getMonth() + 1
    let dd = new Date().getDate()
    let ymd = '' + yy + mm + dd
    wx.setStorageSync('holiday' + that.data.activityId, ymd);
    console.log(ymd, 'holiday12345');
    this.setData({
      showModalDlg2: false,
      holiday: 1,
    })
  },
  // 合成事件
  async onHeTap () {
    if(this.data.audit) return
    const that = this
    if (!this.data.isShow) {
      return wx.showToast({
        title: '活动已结束',
        icon: "none"
      })
    }
    let flag = this.data.cardCfgs.every(item => item.cardHold >= 1)
    if (flag) {
      await API.cardCollectMerge({
        activityId: that.data.activityId
      })
        .then(res => {
          console.log(res, 'onHeTap1234');
          if (res.data.code == 200) {
            this.onBuriedPoint('onSynthesis', '合成')
            this.setData({
              showModalDlg3: true,
              merged: true,
              showModalDlgbtn: false,
            })
            setTimeout(() => {
              that.setData({
                showModalDlgbtn: true,
              })
            }, 1000);
            that.getCollectDetail(that.data.activityId)
          } else {
            wx.showToast({
              title: res.data.message,
              icon: 'none'
            })
            return
          }
        })

    } else {
      wx.showToast({
        title: '卡片不足',
        icon: 'none'
      })
    }
  },
  // 合成事件关闭
  onHeTapClone () {
    const that = this
    that.setData({
      showModalDlg3: false,
      isHeCheng: false,
      ischoujiang: true,
      showModalDlgbtn: false,
    })
  },
  close (e) {
    console.log(e, 'eee');
    const that = this
    that.setData({
      personLogin: true
    })
    wx.showToast({
      title: '请选择身份登录',
      icon: 'none',
      duration: 2000
    });
  },
  getRandomInt (min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min
  },
  // 抽奖
  onLuckDraw () {
    if(this.data.audit) return
    const that = this

    if (!this.data.isShow) {
      return wx.showToast({
        title: '当前时间不在活动期间，无法参加活动',
        icon: "none"
      })
    }
    this.onBuriedPoint('onLuckDraw', '抽奖')
    // 跳转界面

    wx.navigateTo({
      url: `/pages/activityTemplate/lottery/lottery?activityId=${that.data.composeCardConfig.drawId}&qtnId=${this.data.activityId}&mode=COLLECT`,
    })

  },
  // 助力记录
  // onAssist () {
  //   console.log('onAssist 助力记录');
  //   const that = this
  //   wx.navigateTo({
  //     url: '/pages/subpages/assistRecord/index?activityId=' + that.data.activityId
  //   })
  // },
  // // 中奖记录
  // onwinningRecord () {
  //   console.log('onwinningRecord 中奖记录');
  //   const that = this
  //   wx.navigateTo({
  //     url: '/pages/subpages/winningRecord/index?activityId=' + that.data.activityId
  //   })
  // }
  jumpPage (e) {
    if(this.data.audit) return
    console.log(e, '活动规则页',this.data.activityId)
    if (e.currentTarget.dataset.btevent == '活动细则') {
      wx.navigateTo({
        url: `/pages/activityTemplate/jika/rule/rule?id=${this.data.activityId}`,
      })
    } else if (e.currentTarget.dataset.btevent == '中奖记录') {
      console.log('中奖记录')
      
      wx.navigateTo({
        url: `/pages/subpages/winningRecord/index?activityId=${this.data.composeCardConfig.drawId}`,
      })
    } else if (e.currentTarget.dataset.btevent == '助力记录') {
      wx.navigateTo({
        url: `/pages/subpages/assistRecord/index?activityId=${this.data.activityId}`,
      })
    }
  },
  getHeight () {
    let systemInfo = wx.getSystemInfoSync()
    let menuButton = wx.getMenuButtonBoundingClientRect()
    let menuHeight = menuButton.height
    let menuRight = systemInfo.screenWidth - menuButton.right
    let menuBotton = menuButton.top - systemInfo.statusBarHeight
    let navBarHeight = (menuButton.top - systemInfo.statusBarHeight) * 2 + menuHeight + systemInfo.statusBarHeight
    const navBarData = {
      navBarHeight,
      menuRight,
      menuBotton,
      menuHeight
    }
    this.setData({
      navBarData
    })
  },

})
