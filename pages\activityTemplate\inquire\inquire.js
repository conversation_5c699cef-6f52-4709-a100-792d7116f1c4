// pages/activityTemplate/inquire/inquire.js
const API = require('../../../api/request')
import { unRegister } from '../../../utils/util';
const { setSalesCode } = require('../../../utils/querySalesCode');
const { showTab, handleUserStorage } = require('../../../utils/aboutLogin')
const app = getApp()
const CryptoJS = require('../../../utils/CryptoJs')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    navBarData: app.globalData.navBarData,
    postersBanner: '', // 专属海报
    posterText: '', // 专属海报文字
    posterTextStype: '', // 专属海报文字样式
    isHomePage: true, // 是否显示首页
    isQW: false, // 企微直接进入问卷
    backgroundImg: '', // 问卷背景图
    activity: {}, // 活动信息
    activityId: 0, // 活动id
    componentData: [],
    questionsData: [], // 问卷数据
    form: {}, // 表单数据
    canSubmit: false, // 是否可以提交
    drawTrigger: 0, // 是否关联抽奖
    drawId: 0, // 抽奖id
    showCancel: true,
    showPoster: false,
    personLogin: false,
    showModalDlg: false,
    isShare: false,
    showSwitchSale: false,
    changeSalesCode: '',
    shareUserType: '',
    currentId: '',
    acceptId: '',
    audit: 0 // 审核状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad (options) {
    let that = this
    console.log(options);
    let scene = options.scene
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      var id = option[0]
      that.setData({ activityId: id * 1 })
    } else {
      if (options.isShare) {
        that.setData({
          clientShareUserId: options.clientShareUserId,
          activityId: options.id * 1,
          audit: options.audit,
          shareUserId: options.shareUserId,
          isShare: true,
          changeSalesCode: options.salesCode,
          changeSaleName: options.saleName,
          shareUserType: options.userType
        })
      } else {
        that.setData({
          audit: options.audit,
          activityId: options.activityId * 1,
        })
      }

      // 企微进入
      if (options.empno) {
        this.setData({
          changeSalesCode: options.empno,
          isShare: true,
          isQW: true,
        })
      }
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow () {
    const userId = wx.getStorageSync('userId')
    if (!userId) {
      this.setData({ showModalDlg: true })
    } else {
      const ret = await this.getUserInfo()
      if (!ret) return
      const flag = await this.getIsRegister()
      if (flag) {
        this.getBasicInfo()
      }
    }
  },

  goBack () {
    wx.navigateBack({
      delta: 1, // 回退前 delta(默认为1) 页面
    })
  },

  //获取基本信息
  async getBasicInfo () {
    let params = { activityId: this.data.activityId }
    wx.showLoading({ title: '加载中' })
    const res = this.data.audit ? await API.auditQuesDetail(params) : await API.getInquireInfo(params)
    wx.hideLoading()
    const { data, code, message } = res.data
    if (code !== 200) return app.showToast(message, 3000)

    const { activity, questions, canSubmit, drawId, drawTrigger } = data
    const { distributionChannel, showHomepageFlag } = activity

    if (distributionChannel === 'WORK_WX') wx.hideShareMenu({})
    const reg = new RegExp('TXQImgPath/', 'ig');
    const dynamicDomainName = wx.getStorageSync('dynamicDomainName')
    const activityData = activity.activityPageConfig ? activity.activityPageConfig.replace(reg, dynamicDomainName) : ''
    const activityPageConfig = activityData ? JSON.parse(activityData) : []

    console.log('--activityPageConfig✨🍎', activityPageConfig)

    // 如果有首页
    const postersPage = activityPageConfig.find(item => item.title === '专属海报').componentData
    const postersBanner = postersPage.find(item => item.type === 'posterPic').propValue.url
    const text = postersPage.find(item => item.type === 'text')


    const questionnairePage = showHomepageFlag === 1 ? activityPageConfig.find(item => item.title === '问卷首页') : activityPageConfig.find(item => item.title === '问卷详情')
    const result = activityPageConfig.find((item) => item.title === '问卷提交')
    wx.setStorageSync('result', JSON.stringify(result))

    if (questionnairePage) {
      const backgroundImg = questionnairePage.componentData.find(item => item.type === 'backgroundImg')
      const componentData = questionnairePage.componentData.filter(item => item.type !== 'backgroundImg')
      this.setData({
        contentStyle: 'background-image: url(' + backgroundImg.propValue.url + ')',
        componentData: componentData.map(item => {
          let styleStr = `${this.handlerStyle(item.style)}position: absolute;`
          if (item.component === 'VButton') {
            const lineHeight = `line-height:${item.style.height * 2}rpx;`

            const bgStyle = item.bgStatus === 1 ? `background-image: url(${item.imgOptions.url});background-color: ''` : ''
            styleStr = item.type === 'poster'? `${styleStr}${bgStyle}` : `${styleStr}${lineHeight}${bgStyle}`
          }

          return {
            ...item,
            styleStr
          }
        }),
      })
    }

    const questionsData = JSON.parse(JSON.stringify(questions).replace(reg, dynamicDomainName))
    questionsData.forEach(item => {
      if (item.type == 'MUTISELECT' || item.type == 'RADIO') {
        let imageUrl = item.options.findIndex((option) => { return option.url != '' })
        if (imageUrl != -1) {
          // 选项只要有一个有图片，就显示为图片类型的选项
          item['imageUrl'] = true
        } else {
          item['imageUrl'] = false
        }
        if (item.type == 'MUTISELECT' && this.data.isEdit) {
          item.defaultValue = item.defaultValue && item.defaultValue.split(",")
        }
      }
    })

    this.setData({
      posterTextStype: text,
      posterText: text.propValue,
      postersBanner,
      isHomePage: showHomepageFlag === 1,
      drawId,
      drawTrigger,
      canSubmit,
      activity,
      questionsData,
      auditStatus: activity.auditStatus || '',
      financeStatus: activity.financeStatus || '',
    })

    if (this.data.isShare) {
      let salesCode = wx.getStorageSync('salesCode')
      let saleName = wx.getStorageSync('saleName')
      const showCancel = this.data.isQW ? false : true
      if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
        this.setData({ showCancel, showSwitchSale: true, currentId: saleName + salesCode, acceptId: this.data.changeSaleName + this.data.changeSalesCode })
        return
      }
    }
  },

  tapItem (e) {
    const { item } = e.currentTarget.dataset
    console.log('--✨🍎', item)
    if (item.component === 'VButton') {

      if (item.btEvent === 'rule') {
        const { startTime, endTime, distributionChannel } = this.data.activity
        const { canSubmit, isHomePage } = this.data
        const userType = wx.getStorageSync('userType')
        if (Number(userType) === 3 && distributionChannel === 'WORK_WX') return this.showTips('仅客户才能参加本次活动~')
        if (this.data.audit) return
        // 判断活动结束时间是否大于当前时间
        if (!canSubmit) return this.showTips('您不能参与此次活动~!')
        if (new Date().getTime().toString() > new Date(endTime.replace(/-/g, '/')).getTime().toString() || new Date().getTime().toString() < new Date(startTime.replace(/-/g, '/')).getTime().toString()) {
          this.showTips('当前时间不在活动范围内，无法参加活动!')
          return
        }
        if (isHomePage) return wx.navigateTo({ url: `/pages/activityTemplate/questions/questions?id=${this.data.activityId}` })
        this.inquireSubmit()
      }

      if (item.btEvent === 'poster') {
        this.setData({ showPoster: true }, () => {
          wx.nextTick(() => {
            const childComponent = this.selectComponent('#poster')
            if(childComponent) childComponent.onClickShow()
          })
        })
      }
    }
  },

  inquireSubmit () {
    let that = this
    console.log(that.data.form);
    let isRequired = that.data.questionsData.filter(item => {
      return !item.defaultValue && item.needed == '1'
    })
    console.log(isRequired)
    if (isRequired && isRequired.length != 0) {
      wx.showToast({
        title: `${isRequired[0].title}为必填项！`,
        duration: 2000,
        icon: "none",
      })
      return
    }
    let data = {
      activityId: that.data.activityId,
      answers: that.data.form
    }
    wx.showLoading({ title: '加载中' })
    console.log(data);
    API.inquireSubmit(data).then((res) => {
      wx.hideLoading()
      if (res.data.code == 200) {
        console.log(res.data);
        wx.redirectTo({  
          url: `/pages/activityTemplate/submitSuccess/submitSuccess?drawId=${that.data.drawId}&type=${that.data.drawTrigger}&qtnId=${that.data.activityId}`,
        })
      } else {
        that.showTips(res.data.message)
      }
    })
  },


  // 单选框
  onChanges (e) {
    console.log(e)
    let questionsData = this.data.questionsData
    let form = this.data.form
    let key = e.target.dataset.key //问题id
    let index = e.target.dataset.index //
    let value = e.detail //选项id
    questionsData[index].defaultValue = value //修改默认值
    let options = questionsData[index].options.filter((item) => {
      return value == item.id
    })
    questionsData[index].options.map((item) => {
      item.select = false
      if (value == item.id) {
        item.select = true
      }
    })
    let option = {
      [value]: options[0].content
    }
    form[key] = option
    this.setData({
      form,
      questionsData
    });
  },

  // 多选框
  onChange (e) {
    console.log(e)
    let questionsData = this.data.questionsData
    let form = this.data.form
    let key = e.target.dataset.key //id
    let index = e.target.dataset.index //父级dex
    let value = e.detail
    questionsData[index].defaultValue = value //修改默认值
    questionsData[index].options.map((item) => {
      item.select = false
      value.map((value) => {
        if (value == item.id) {
          item.select = true
        }
      })
    })
    form[key] = value
    this.setData({
      form,
      questionsData
    });
  },

  inputBlur (e) {
    console.log(e);
    let value = e.detail.value
    let key = e.target.dataset.key //key
    let index = e.target.dataset.index //
    let questionsData = this.data.questionsData
    let form = this.data.form
    questionsData[index].defaultValue = value //修改默认值
    form[key] = value
    this.setData({
      questionsData,
      form
    })
  },

  _clickEvent () {
    // 非客户身份，则提示“仅客户才能参加本次活动~”
    const { canSubmit, startTime, endTime } = this.data.activity

    const userType = wx.getStorageSync('userType')
    if (Number(userType) === 3) return this.showTips('仅客户才能参加本次活动~')

    if (this.data.audit) return
    // 判断活动结束时间是否大于当前时间
    if (!canSubmit) {
      this.showTips('您不能参与此次活动~!')
      return
    }
    if (new Date().getTime().toString() > new Date(endTime.replace(/-/g, '/')).getTime().toString() || new Date().getTime().toString() < new Date(startTime.replace(/-/g, '/')).getTime().toString()) {
      this.showTips('当前时间不在活动范围内，无法参加活动!')
      return
    }

    wx.navigateTo({
      url: `/pages/activityTemplate/questions/questions?id=${this.data.activityId}`,
    })
  },
  // 关闭专属海报弹框
  onClickHide () {
    this.setData({
      showPoster: false
    })
  },
  // 下载海报
  downPoster () {
    this.setData({
      showPoster: true
    })
  },
  handlerStyle (style) {
    console.log(style)
    let newStyle = ''
    // 1为数字类型；2为字符串类型
    const keyValue = new Map([
      ['width', {
        name: 'width',
        type: 1
      }],
      ['height', {
        name: 'height',
        type: 1
      }],
      ['borderRadius', {
        name: 'border-radius',
        type: 1
      }],
      ['top', {
        name: 'top',
        type: 1
      }],
      ['left', {
        name: 'left',
        type: 1
      }],
      ['borderColor', {
        name: 'border-color',
        type: 2
      }],
      ['fontSize', {
        name: 'font-size',
        type: 1
      }],
      ['fontWeight', {
        name: 'font-weight',
        type: 2
      }],
      ['textAlign', {
        name: 'text-align',
        type: 2
      }],
      ['color', {
        name: 'color',
        type: 2
      }],
      ['backgroundColor', {
        name: 'background-color',
        type: 2
      }],
      ['backgroundImage', {
        name: 'background-image',
        type: 2
      }],
      ['padding', {
        name: 'padding',
        type: 1
      }]
    ])
    for (let key in style) {
      newStyle = `${newStyle}` + this.handlerCssStyleStr(keyValue.get(key), style[key])
    }
    return newStyle
  },
  handlerCssStyleStr (keyValueStr, value) {
    if (keyValueStr === undefined) return ''
    if (keyValueStr.type === 1) return `${keyValueStr.name}:${value * 2}rpx;`
    if (keyValueStr.type === 2) return `${keyValueStr.name}:${value};`
  },
  reject () {
    this.setData({
      showReject: true,
      rejectReason: ''
    })
  },
  // 获取用户信息
  async getUserInfo (token) {
    const res = await API.getInfoById()
    console.log('--获取用户信息✨✨', res)
    const { data, code, message } = res.data
    if (code == 200) {
      handleUserStorage(data)
    } else {
      this.showTips(message)
    }

    return new Promise((resolve, reject) => {
      if (code === 200) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  },

  closeChangeTieModal () {
    this.setData({ showSwitchSale: false })
  },

  // 判断是否注册过
  async getIsRegister () {
    let that = this
    if (this.data.isQW) {
      wx.showLoading({ title: '加载中...', mask: true })
      const response = await API.getSaleInfoByCode({ code: CryptoJS.Encrypt(this.data.changeSalesCode) })
      wx.hideLoading()
      const { data, code, message } = response.data
      if (code !== 200) {
        wx.showToast({ title: message, icon: 'none', duration: 2000 })
        Promise.resolve(false)
        return
      }
      const changeSaleName = CryptoJS.Decrypt(data.name)
      const changeSalesCode = CryptoJS.Decrypt(data.code)
      this.setData({ changeSaleName, changeSalesCode })
    }

    return new Promise(async (resolve, reject) => {
      const res = !unRegister()
      if (!res) {
        resolve(false)
        if (that.data.changeSalesCode && that.data.changeSaleName) {
          setSalesCode(that.data.changeSalesCode, that.data.changeSaleName)
        }
        if (that.data.isQW) {
          wx.navigateTo({ url: '/pages/subpages/clientLogin/clientLogin' })
        } else {
          this.setData({ personLogin: true })
        }
      } else {
        this.data.isAuthorize && showTab()
        resolve(true)
      }
    })
  },

  //用户微信登录授权
  async userInfoHandler () {
    app.getUserInfo(async (userInfo) => {
      wx.setStorageSync('userId', userInfo.id);
      wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
      wx.setStorageSync('nickName', userInfo.nikename);
      wx.setStorageSync('openId', userInfo.openId)
      this.setData({ userId: userInfo.id, isAuthorize: true })
      if (userInfo.id) {
        this.setData({ showModalDlg: false })
        const ret = await this.getUserInfo()
        if (!ret) return
        const res = await this.getIsRegister() // 判断是否注册过 
        if (res) {
          this.getBasicInfo()
        }
      }
    })
  },

  showTips (title, duration = 3000) {
    wx.showToast({
      title,
      duration,
      icon: "none",
      mask: true
    })
  },

  close () {
    this.showTips('请选择身份登录')
  },

  /**
   * 用户点击右上角分享
   */
  async onShareAppMessage () {
    if (this.data.audit) return
    const { shareText, shareUrl } = this.data.activity
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let salesCode = wx.getStorageSync('salesCode')
    let saleName = wx.getStorageSync('saleName')
    let userType = wx.getStorageSync('userType')
    var result = shareText.replace('#微信昵称#', nickName);
    var title = result.replace('#活动名称#', this.data.title);
    var reg = new RegExp('TXQImgPath/', 'ig');
    let dynamicDomainName = wx.getStorageSync('dynamicDomainName')
    let imageUrl = shareUrl.replace(reg, dynamicDomainName)
    console.log(imageUrl);
    return {
      title: title,
      imageUrl: imageUrl,
      path: `/pages/activityTemplate/inquire/inquire?id=${this.data.activityId}&salesCode=${salesCode}&saleName=${saleName}&clientShareUserId=${userId}&isShare=1&shareUserId=${userId}&userType=${userType}`
    }
  },
})