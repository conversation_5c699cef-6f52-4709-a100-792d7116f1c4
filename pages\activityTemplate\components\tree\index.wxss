.tree {
  text-align: left;
  /* height: 45px;
  line-height: 45px; */
  padding-left: 10px;
}
.tree-item {
  height: 60rpx;
  line-height: 60rpx;
  display: flex;
  /* padding-left: 15px; */
}
.tree-item-onOff {
  width: 58rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.tree-item-onOff  image {
  width: 42rpx;
  height: 32rpx;
  display: block;
  transition: 0.4s;
}
.tree-item-onOff-closed {
  transform: rotate(-90deg);
}
.tree-item-onOff-open {
  transform: rotate(0deg);
}
.tree-item-name {
  width: calc(100% - 40px);
  display: flex;
  padding-left: 10px;
}
.name {
  width: calc(100% - 50px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 28rpx;
}
.img {
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.img  image {
  width: 15px;
  height: 10px;
  display: block;
}
.tree-item-name-select {
  background: #ECF7FA;
  color: #0079FE;
}
.tree-item-name-select2 {
  color: #0079FE;
}

