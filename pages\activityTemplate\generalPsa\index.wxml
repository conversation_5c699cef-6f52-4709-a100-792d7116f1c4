<import src="../templatesPsa/detailFragments/detailFragments" />
<!-- 加载动画 -->
<loading-animation wx:if="{{myLoadAnimation}}"></loading-animation>
<!-- 加载动画 -->
<!-- 没有同意协议显示弹窗 -->
<privacy-popop showPrivacy="{{disagree}}" bind:agree="agreePrivacy" jumpType="{{jumpType}}"></privacy-popop>
<!-- 没有同意协议显示弹窗 -->
<!-- 刷新弹窗 -->
<view wx:if="{{reflashToast}}">
  <view class="mymask">
    <view class="mask_content">
      <image class="reflash_img" src="https://txqmp.cpic.com.cn/uploads/img/refresh.png" lazy-load="false" />
      <view class="reflash_text_wrap">
        <view>啊哦~</view>
        <view>大伙的热情过于高涨，</view>
        <view>请稍等片刻~</view>
      </view>
      <view class="reflash_btn_wrap">
        <button class="reflash_btn" disabled="{{!isReflash}}" bindtap="reGetActivity">
          刷新{{reflashBtnContent}}
        </button>
      </view>
    </view>
  </view>
  <view class="mask2" catchtouchmove="preventTouchMove"></view>
</view>
<!-- 刷新弹窗 -->
<!-- 登陆者身份选择 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close"></login-box>
<!-- 登陆者身份选择 -->
<!-- 引发授权的弹窗 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler" class="userinfo-user" hidden="{{InfoShow == false}}">
      点击进入
    </button>
  </view>
</view>
<!-- 引发授权的弹窗 -->
<!-- 活动详情内容 {{(audit == 1||checkDetail==1) ? '100vh':showPoster?'100vh':'calc(100vh - 188rpx)'}} -->
<view class="activity_wrap">
  <view class="activity_wrap_info">
    <view class="page_main" style="background-image:url({{backgroundImg}});height: {{backgroundHeight*2}}rpx;">
      <block wx:for="{{componentData}}" wx:key="index">
        <block wx:if="{{item.type === 'activeBannerBackgroundImg'}}">
          <view class="activity_banner_bg" style="{{item.styleStr}};background-image:url({{item.propValue.url}}"></view>
        </block>
        <block wx:if="{{item.type === 'VSwiper'}}">
          <custom-carousel title="{{activity.title}}" bannerConfig="{{VSwiper}}" heightTop="{{heightTop}}" showHomeMenu="{{isShare||scene||linkType=='kege'}}"></custom-carousel>
        </block>
        <block wx:if="{{item.type === 'textDisplayMode'}}">
          <view style="{{item.styleStr}}" class="activity_detail_textDisplayMode">
            <rich-text nodes="{{item.propValue}}" class="ql-editor"></rich-text>
          </view>
        </block>
        <block wx:if="{{item.type === 'activeBackgroundImg' || item.type === 'detailBackgroundImg' || item.type === 'activeBgWhiteBox' || item.type === 'activeBgBox'}}">
          <view class="activity_detail_bg" style="{{item.styleStr}};background-image:url({{item.propValue.url}}"></view>
        </block>
        <block wx:if="{{item.type === 'activityTitle'}}">
          <view class="activity_detail_title" style="{{item.styleStr}}background-image:url({{item.bgStatus==1?item.imgOptions.url:''}});line-height:{{item.style.height*2}}rpx;font-size:{{(item.style.fontSize-1)*2}}rpx;background-color:{{item.bgStatus===0?item.style.backgroundColor:'transparent'}}">
            {{activity.title}}
          </view>
        </block>
        <block wx:if="{{item.type === 'activeTitle'}}">
          <view style="{{item.styleStr}};position: absolute">{{activity.title}}</view>
        </block>
        <block wx:if="{{item.type === 'stock'}}">
          <view class="totalNum" style="{{item.styleStr}}">
            <view class="sale_free" style="{{item.optionsStyleStr}}">
              剩余：
              <view class="mount" style="color:{{item.optionsStyle.timeColor}};font-size: {{item.optionsStyle.timeFontSize}};font-weight: {{item.optionsStyle.timeFontWeight}};">
                {{activity.place}}
              </view>
              <view class="receiveText">(已领取{{activity.totalPlace-activity.place}})</view>
            </view>
          </view>
        </block>
        <!-- 报名时间 -->
        <block wx:if="{{item.type === 'applyTime'}}">
          <view class="activity_detail_time_title" style="{{item.styleStr}}">
            <view style="{{item.optionsStyleStr}}">活动时间：</view>
            <view style="color:{{item.optionsStyle.timeColor}};font-size: {{item.optionsStyle.timeFontSize*2}}rpx;font-weight: {{item.optionsStyle.timeFontWeight}};">
              {{applyStartTime}} ~ {{applyEndTime}}
            </view>
          </view>
        </block>
        <!-- 详情标题 -->
        <block wx:if="{{item.type === 'introduct'}}">
          <view class="activity_detail_title" style="{{item.styleStr}}background-image:url({{item.bgStatus==1?item.imgOptions.url:''}});line-height:{{item.style.height*2}}rpx;font-size:{{(item.style.fontSize-1)*2}}rpx;background-color:{{item.bgStatus===0?item.style.backgroundColor:'transparent'}}">
            {{item.propValue}}
          </view>
        </block>
        <!-- 详情 -->
        <block wx:if="{{item.type === 'detail-area'}}">
          <template is="detail_Fragments" data="{{item,activityTime}}"></template>
        </block>
      </block>
    </view>
  </view>
  <!-- 底部按钮 -->
  <view wx:if="{{!checkDetail}}" class="footer {{showPoster? 'footerHide' : '' }}">
    <block wx:for="{{componentData}}" wx:key="index">
      <block wx:if="{{item.type === 'button' && item.btEvent === 'receiveBtn'}}">
        <block wx:if="{{linkType==='kege'}}">
          <view wx:if="{{(userType===3&&activity.hotId)}}" class="myOrder buttonPopup" bind:tap="showModel" style="{{item.styleStr}}">
            分享客户
          </view>
          <button class="myOrder" wx:if="{{(userType===3&&!activity.hotId&&(bindSalesCode == changeSalesCode))}}" style="{{item.styleStr}}" open-type='share'>
            分享客户
          </button>
        </block>
        <view class="myOrder" style="{{item.styleStr}}" bindtap="tobuy" wx:else>
          {{item.propValue}}
        </view>
        <view class="myOrder buttonPopup" bind:tap="verify" style="{{item.styleStr}}" wx:if="{{(linkType==='kege'&&userType!=3)||(sub_time2 < 0||sub_time1 < 0||(bindSalesCode != changeSalesCode))&&userType==3}}">
          分享客户
        </view>
      </block>
    </block>
  </view>
</view>
<!-- 底部按钮 -->
<!-- 活动详情内容 -->
<!-- 缺少身份证号后完善身份信息 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{chooseArea}}"></view>
<view class='{{chooseArea ? "chooseArea" : ""}} ' wx:if="{{chooseArea}}">
  <view class="modal_icon" catchtap="closeChooseAreaModal">
    <van-icon size="44rpx" color="#D0D2DB" name="cross" />
  </view>
  <view class="title">温馨提示</view>
  <view class="desc">请完善您的身份证号信息</view>
  <view class="choose">
    <view>
      <span>*</span>
      姓名
    </view>
    <view class="chooseClick">
      <input type="text" placeholder="请输入" disabled="disabled" value="{{trueName}}" />
    </view>
  </view>
  <view class="choose">
    <view>
      <span>*</span>
      证件类型
    </view>
    <view class="chooseClick">
      <picker class="truename1s" bindchange="identityChange" range="{{identityType}}" value="{{identityIndex}}" range-key="name">
        <view class="weui-input">{{identity}}</view>
      </picker>
    </view>
  </view>
  <view class="choose">
    <view>
      <span>*</span>
      证件号
    </view>
    <view class="chooseClick">
      <input type="text" placeholder="请输入18位身份证号码" value="{{idCard}}" bindinput="idCardValue" />
    </view>
  </view>
  <view>
    <button catchtap="confrimChooseArea">保存</button>
  </view>
</view>
<!-- 缺少身份证号后完善身份信息 -->
<!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" salesCode="{{changeSalesCode}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" bind:sureSwitch="confirmChangeTieModal" bind:close="closeChangeTieModal" isShowCancel="{{isShowCancel}}" isShowClose="{{isShowClose}}"></switch-sale>
<!-- 分享页面是否切换业务员 -->
<audit wx:if="{{audit}}" bind:reject="reject" activityId="{{ids}}" activityType="{{category}}" financeStatus="{{activity.financeRecheckStatus}}"></audit>
<reject-reason showDialog="{{showReject}}" activityId="{{ids}}" rejectReason="{{rejectReason}}" audit="{{activity.auditStatus}}" financeStatus="{{activity.financeStatus}}" bind:rejectButton="triggerEvent"></reject-reason>
<!-- 一键报障 -->
<contact module="活动参与" pageName="报名/商品活动详情" pageUrl="{{pageUrl}}" businessData="{{activity}}" isShow="{{isShow}}"></contact>
<receiveAddress showAddress="{{showAddress}}" hotId="{{hotId}}" userType="{{userType}}" consumerName="{{consumerName}}" bind:onClickHide="onClickHide" bind:receiveType="receiveType" bind:defaultAddress="defaultAddress" />
<!-- saleName="{{changeSaleName}}" -->
<overTips content="{{ message }}" showClose="{{false}}" isShowOverlay="{{isShowOverlay}}" bind:closeOverTip="closeOverTip"></overTips>
<globalPrompt model:show="{{ showGlobalPrompt }}" content="{{globalPromptMessage}}" bind:btnClick="popClick" btnText="{{buttonText}}"></globalPrompt>
<van-popup show="{{ isShowImg }}" bind:close="onClose"   custom-style="background-color:transparent">
  <view class="psaTipImgWrap" bind:tap="onClose">
    <image class="psaTipImg" src="{{imgUrl}}" mode="scaleToFill" />
  </view>
  <van-icon name="cross" class="closeButton" color="#fff" size="20px" bind:tap="onClose"/>
</van-popup>