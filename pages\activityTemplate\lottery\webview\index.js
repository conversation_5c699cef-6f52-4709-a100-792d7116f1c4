const app = getApp();
const API = app.require('/api/request')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    src: null,
    localWeb: null,
    scrollHeight:0,
  },

  onShow() {
    // wx.hideHomeButton()
  },

  onLoad(options) {
    console.log(15, options)
    // console.log(15, decodeURIComponent(options.src))
    this.setData({
      id: options.id,
      src: decodeURIComponent(options.src),
      mode:options.mode||''
    })
    // if(){

    // }

    this.getLocalweb()
  },
  async getLocalweb(){
    let res = await API.getfindByCode({code: "local_url"})
    this.setData({localWeb: res.data.data})
  },
  imageLoad(e) {
    const imageHeight = e.detail.height; // 图片实际高度
    // 根据需要更新 scrollHeight
  },
  onUnload() {
    // 判断当前页面是否是领取成功页面 如果不是则跳转到领取成功页面
    console.log(this.data.src)
    console.log(this.data.localWeb)
    // if(!this.data.src.startsWith(this.data.localWeb)){
    //   wx.navigateTo({
    //     url: '/pages/activityTemplate/lottery/webview/index?src=' + encodeURIComponent(`${this.data.localWeb}#/results?activityType=DRAW&activityId=${this.data.id}`)
    //   })
    // }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    const systemInfo = wx.getSystemInfoSync();
    const screenHeight = systemInfo.windowHeight; // 屏幕高度
    const statusBarHeight = systemInfo.statusBarHeight; // 状态栏高度
    const scrollHeight = screenHeight - statusBarHeight; // 根据实际情况调整
    this.setData({ scrollHeight });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})