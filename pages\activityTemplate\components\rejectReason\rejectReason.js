const API = require('../../../../api/request')
const tools = require('../../../../utils/util.js');
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showDialog:{
      type: Boolean,
      value: false
    },
    activityId:{
      type:String,
      value:''
    },
    rejectReason:{
      type:String,
      value:''

    },
     // 审核状态
    audit:{
      type:Number,
      value:1
    },
    // 财务审核状态
    financeStatus:{
        type:Number,
        value:1
    },
    // 活动类型
    activityType:{
      type:Number,
      value:1  
    },
    // 返回次数
    linkTimes: {
      type:[Number,String],
      value:1
    }
    


  },

  /**
   * 组件的初始数据
   */
  data: {
    

  },

  /**
   * 组件的方法列表
   */
  methods: {
    bindRejectReason(e){
      this.setData({ rejectReason: e.detail.value })
    },
     rejectButton:tools.debounce(async function (e) {
      let index = e.currentTarget.dataset.index
      if (index == '0') return this.setData({ showDialog: false })
      if (this.data.rejectReason == '') return app.showToast('驳回理由不可为空')
      if(this.data.audit == 0){
        let data = {
          auditStatus:2,
          id:this.data.activityId*1,
          auditRemark:this.data.rejectReason

        }
        let res = await API.startAudit(data)
        console.log(res);
        if(res.data.code == 200){
          app.showToast(res.data.message)
          this.setData({
            showDialog:false
          })
          if(this.data.activityType==7||this.data.activityType==9||this.data.activityType==12||this.data.activityType==13){
            wx.navigateBack({
              delta:2
            })
          }else{
            wx.navigateBack({
              delta:1
            })
          }
         
        }else{
          app.showToast(res.data.message)
        }
      }else if(this.data.financeStatus == 0){
        let data = {
          financeStatus:2,
          id:this.data.activityId*1,
          financeRemark:this.data.rejectReason
        }
        let res = await API.financeAudit(data)
        if(res.data.code == 200){
          app.showToast(res.data.message)
          this.setData({
            showDialog:false
          })
          if(this.data.activityType==7||this.data.activityType==9||this.data.activityType==12||this.data.activityType==13){
            wx.navigateBack({
              delta:this.data.linkTimes
            })
          }else{
            wx.navigateBack({
              delta:1
            })
          }
        }else{
          app.showToast(res.data.message)
        }
      }
      this.triggerEvent('rejectButton')
     
    }),


  }
})
