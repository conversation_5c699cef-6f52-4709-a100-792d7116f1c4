// pages/activityTemplate/springOuting/home/<USER>
import { unRegister } from '../../../../utils/util.js'
import { setSalesCode } from '../../../../utils/querySalesCode'
import { handleUserStorage } from '../../../../utils/aboutLogin'
import API from '../../../../api/request.js'
import HTAPI from '../../../../api/haitong'

const app = getApp()
Page({

  onclickBtn (e) {
    const { item } = e.target.dataset
    console.log('--✨🍎', item)
    if (item.btEvent === "rule") {
      wx.navigateTo({
        url: `/pages/activityTemplate/lotteryRule/lotteryRule?id=${this.data.activityId}`,
      })
    } else if (item.btEvent === "prize") {
      
      if(this.data.distributionChannel === 'WORK_WX') {
        wx.navigateTo({ url: '/pages/myactivity/myactivity?status=4' })
      } else {
        wx.navigateTo({
          url: `/pages/activityTemplate/lotteryReward/lotteryReward?id=${this.data.activityId}&mode=${this.data.drawMode}&qtnId=${this.data.qtnId}&productId=${this.data.productId}`,
        })
      }
    } else if (item.btEvent === 'poster') {
      this.setData({ showPoster: true }, () => {
        this.timer = setInterval(() => {
          const childComponent = this.selectComponent('#poster')
          if (childComponent) clearInterval(this.timer)
          childComponent.onClickShow()
        }, 10)
      })
    }
  },

  onClickHide () {
    this.setData({ showPoster: false })
  },

  changeCrad (e) {
    console.log('--✨🍎', e)

    const userType = wx.getStorageSync('userType')
    const { isHt, distributionChannel, financialManagerShareData, changeSalesCode } = this.data
    const { financialManagerNo, financialManager } = financialManagerShareData

    if (distributionChannel === 'HTZQ') {
      if (userType === 2) return this.setData({ isShowOverlay: true, message: '此活动仅限海通专区用户参与~' })
      if (userType === 3 && !isHt) return this.setData({ isShowOverlay: true, message: '仅海通专区营销员可参与活动~' })
      if (userType === 1 && !isHt && !financialManagerNo) return this.setData({ isShowOverlay: true, message: '此活动仅限海通专区用户参与~' })
      const params = `?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}`
      if (userType === 1 && !isHt && financialManagerNo) return wx.navigateTo({ url: `/pages/fuseSpecial/haitong/login/index${params}` })
    }

    const { item, index } = e.currentTarget.dataset
    if (item.drawn) return
    const { endTime, startTime } = this.data.activityDetailData
    if (this.data.audit == 1) return
    if (!this.data.canDraw) return this.showTips('您不能参与此次活动~!')
    if (this.data.restDrawNum <= 0) return this.showTips('您的抽奖次数已用完!')
    if (endTime && new Date().getTime().toString() > new Date(endTime.replace(/-/g, '/')).getTime().toString()) return this.showTips('抽奖活动已结束!')
    if (startTime && new Date().getTime().toString() < new Date(startTime.replace(/-/g, '/')).getTime().toString()) return this.showTips('抽奖活动未开始!')
    this.setData({ currentCradIndex: index })
    this.getDraw(item)
  },

  async getDraw (item) {
    const { cardNo, backUrl, url } = item
    const params = {
      id: this.data.activityId,
      drawMode: this.data.drawMode,
      qtnId: this.data.qtnId,
      productId: this.data.productId,
      cardNo
    }

    this.setData({
      cardUrl: url,
      cardBackUrl: backUrl,
      isShowAnimation: true
    }, () => {
      setTimeout(() => {
        this.setData({ isShowAnimationCard: true })
      }, 200)
    })
    const res = await API.getNewPrize(params)
    const { code, data, message } = res.data

    setTimeout(() => {
      this.setData({ isShowAnimation: false })
      if (code !== 200) return this.showTips(message)
      this.setData({ restDrawNum: data.restDrawNum, recordId: data.recordId }, () => { this.setData({ isShowAnimationCard: false }) })
      this.toSucess(data.drawPrizes)
    }, 1500)

  },

  toSucess (drawPrizes) {
    wx.showLoading({ title: '加载中...', mask: true })
    const { rightsSrc, prizeName, tips, url, flagReceiveInfo } = drawPrizes
    const { winningPage, nothingPage, currentCradIndex, activityTemplateType, attributes, acceptAwardBtn, acceptAwardImg, activityDetailData, receiveType, getUrlRes, isFirst, recordId, successPage } = this.data
    const page = rightsSrc === 'NOTHING' ? nothingPage : winningPage

    const flipBgImgIndex = page.findIndex(item => item.type === 'flipBgImg')
    const flipTxtImgIndex = page.findIndex(item => item.type === 'flipTxtImg')
    const flipTxtList = page.findIndex(item => item.type === 'flipTxtList')
    // const i = +activityDetailData.drawConfig.mode === 2 ? currentCradIndex : 0

    const pageData = {
      bgUrl: page[flipBgImgIndex].filpSettings.imgListOptions[currentCradIndex].url,
      descUrl: page[flipTxtImgIndex].filpSettings.imgListOptions[currentCradIndex].url,
      fliptextColor: page[flipTxtList].setting.colorListOptions[currentCradIndex].color,
      btnsList: page.filter(item => item.component === 'VButton'),
      plainText: page.find(item => item.component === 'plainText'),
      pictureUrl: page.find(item => item.component === 'Picture').propValue.url
    }

    wx.setStorageSync('winningPage', JSON.stringify(pageData))
    wx.setStorageSync('successPage', JSON.stringify({ successPage, attributes, acceptAwardImg, acceptAwardBtn }))
    const p = JSON.stringify({ prizeName, prizeImg: url, activityTemplateType, receiveType, getUrlRes, isFirst, recordId, flagReceiveInfo })
    wx.hideLoading()
    wx.navigateTo({
      url: `/pages/activityTemplate/springOuting/winning/index?p=${encodeURIComponent(p)}`,
    })
  },

  async activityDetail () {
    wx.showLoading({ title: '加载中' })
    let params = {
      id: this.data.activityId,
      drawMode: this.data.drawMode,
      qtnId: this.data.qtnId,
      productId: this.data.productId
    }
    let res = this.data.audit == 1 ? await API.auditVoteDetail({ id: this.data.activityId }) : await API.getNewDrawInfo(params)
    wx.hideLoading()

    console.log('活动详情', res)

    const { data, code, message } = res.data
    if (code !== 200) return this.showTips(message)

    const { activityPageConfig, qrcode, title, activityTemplateType, attributes, drawConfig, drawInfoVo, distributionChannel } = data
    this.setData({ distributionChannel }, () => {
      if (distributionChannel === 'HTZQ') this.getHyacinthPerm()
    })

    if (distributionChannel === 'WORK_WX') wx.hideShareMenu({})
    const { receiveType } = drawConfig
    const { canDraw, restDrawNum } = drawInfoVo
    var reg = new RegExp('TXQImgPath/', 'ig');
    let dynamicDomainName = wx.getStorageSync('dynamicDomainName')

    const pageData = activityPageConfig && JSON.parse(activityPageConfig.replace(reg, dynamicDomainName))
    const homePage = pageData.find(item => item.title === '抽奖首页').componentData
    this.setData({ homePage })

    const postersPage = pageData.find(item => item.title === '专属海报').componentData
    const winningPage = pageData.find(item => item.title === '中奖页面').componentData
    const nothingPage = pageData.find((item) => item.title === '未中奖页面').componentData
    const successPage = pageData.find(item => item.title === '领奖成功提示').componentData
    const awardCollectionPage = pageData.find((item) => item.title === '领奖弹窗').componentData
    const rulePage = pageData.find((item) => item.title === '规则介绍').componentData
    wx.setStorageSync('lotteryRule', rulePage)

    const postersBanner = postersPage.find(item => item.type === 'posterPic').propValue.url
    const text = postersPage.find(item => item.type === 'text')
    const acceptAwardImg = awardCollectionPage.find((item) => item.type === 'backgroundImg')
    const acceptAwardBtn = awardCollectionPage.find(item => item.type == 'button')

    this.setData({
      qrcode,
      title,
      postersBanner,
      acceptAwardImg,
      activityTemplateType,
      acceptAwardBtn,
      attributes,
      receiveType,
      restDrawNum: drawInfoVo ? restDrawNum : 0,
      posterText: text.propValue,
      textStyle: text,
      activityDetailData: data,
      canDraw,
      winningPage,
      nothingPage,
      successPage
    }, () => {
      this.handleCrad()
    })


    // if (receiveType) {
    //   const activityId = this.data.activityId
    //   const backUrl = encodeURIComponent(`#/results?activityType=DRAW&activityId=${activityId}&drawMode=${this.data.drawMode}&qtnId=${this.data.qtnId}`)
    //   const res = await API.signInGetReceiveUrl({ activityId, backUrl, receiveType, type: "DRAW" })
    //   const { code, data, message } = res.data
    //   if (code !== 200) return this.showTips(message)
    //   this.getUrlRes = res.data.data
    //   this.setData({ isFirst: this.getUrlRes.isNot === 'n', getUrlRes: res.data.data })
    // }
  },

  // 处理卡片
  handleCrad () {
    const { flipsJson } = this.data.activityDetailData.drawConfig
    const filpSettings = this.data.homePage.find((item) => item.type === 'flip').filpSettings
    const { method, imgOptions, imgListOptions, backImgListOptions, backImgOptions } = filpSettings
    console.log('--✨🍎', imgListOptions, filpSettings)
    console.log('--✨flipsJson🍎', flipsJson)
    const cardList = flipsJson.map((item, index) => {
      console.log(imgListOptions[index]);
      const url = method === 2 ? imgListOptions[index].oldUrl : imgOptions.url
      const backUrl = method === 2 ? backImgListOptions[index].url : backImgOptions.url
      return {
        ...item,
        url,
        backUrl
      }
    })
    this.setData({ cardList })
  },

  userInfoHandler () {
    app.getUserInfo(async (userInfo) => {
      this.setData({ userId: userInfo.id })
      if (userInfo.id) {
        this.setData({ showModalDlg: false })
        const ret = await this.getUserInfo()
        if (!ret) return

        await this.getHyacinthPerm()

        const userType = wx.getStorageSync('userType')
        const { isHt, financialManagerShareData, changeSalesCode } = this.data
        const { financialManagerNo, financialManager, distributionChannel } = financialManagerShareData

        // 针对海通活动
        if (distributionChannel === 'HTZQ') {
          const params = `?financialManagerNo=${financialManagerNo}&financialManager=${financialManager}&salesCode=${changeSalesCode}`
          if (userType === 0 && !isHt && financialManagerNo) return wx.navigateTo({ url: `/pages/fuseSpecial/haitong/login/index${params}` })
        }

        const res = await this.getIsRegister()
        if (!res) return
        this.activityDetail()
      }
    })
  },

  // 判断是否注册过
  async getIsRegister () {
    let that = this
    return new Promise((resolve, reject) => {
      const res = !unRegister()
      if (!res) {
        resolve(false)
        that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode, that.data.changeSaleName)
        this.setData({ personLogin: true })
      } else {
        resolve(true)
      }
    })
  },

  // 获取用户信息
  async getUserInfo () {
    return new Promise((resolve, reject) => {
      API.getInfoById().then(res => {
        const { data, code, message } = res.data
        if (code === 200) {
          resolve(true)
          handleUserStorage(data)
        } else {
          this.showTips(message)
          resolve(false)
        }
      }).catch(err => {
        resolve(false)
      })
    })
  },

  async getHyacinthPerm () {
    return new Promise((resolve, reject) => {
      const { distributionChannel } = this.data
      let userType = wx.getStorageSync('userType')
      // 获取理财经理信息
      if (distributionChannel === 'HTZQ' && userType === 1) {
        this.getQueryFinancial()
      }
      HTAPI.getHyacinthPerm().then(res => {
        const { data, code, message } = res.data
        if (code === 200) {
          resolve()
          this.setData({ isHt: data.htUser === 1 })
        }
      }).catch(err => {
        resolve()
      })

    })
  },

  async init () {
    if (!wx.getStorageSync("userId")) return this.setData({ showModalDlg: true })
    if (this.data.audit != 1) {
      wx.showShareMenu({ withShareTicket: false })
    } else {
      wx.hideShareMenu({})
    }
    const ret = await this.getUserInfo()
    if (!ret) return


    const flag = await this.getIsRegister()
    if (flag) {
      this.activityDetail()
    }
  },

  close () {
    this.setData({ personLogin: false })
    this.showTips('请选择身份登录')
  },

  closeNothingPage () {
    this.setData({ showTipsPage: false })
  },

  back () {
    if (this.data.isShare || this.data.scene) {
      wx.switchTab({
        url: '/pages/home/<USER>',
      })
    } else {
      wx.navigateBack({
        delta: 1
      })
    }
  },

  async getQueryFinancial () {
    const salesCode = wx.getStorageSync('salesCode')
    const getInfoByIdData = wx.getStorageSync('getInfoByIdData')
    const { financialManagerNo } = getInfoByIdData
    if (financialManagerNo === undefined || financialManagerNo === null) return
    const res = await HTAPI.getQueryFinancial({ salesCode, managerCode: financialManagerNo })
    console.log('--✨🍎', res)
    const { code, message, data } = res.data
    if (code !== 200) return
    this.setData({ financialManagerData: data })
  },

  showTips (title, duration = 2000) {
    wx.showToast({
      title,
      duration,
      icon: "none",
    })
  },

  closeOverTip () {
    this.setData({ isShowOverlay: false }, () => {
      wx.switchTab({ url: '/pages/home/<USER>' })
    })
  },

  /**
   * 页面的初始数据
   */
  data: {
    navBarData: app.globalData.navBarData,
    financialManagerShareData: {},
    isShowOverlay: false,
    message: '',
    financialManagerData: {},
    distributionChannel: '',
    isHt: false,
    isShowTitle: true,
    showModalDlg: false,
    isShowAnimation: false,
    personLogin: false,
    showSwitchSale: false,
    showReject: false,
    showPoster: false,
    showTipsPage: false,
    isShowAnimationCard: false,
    activityDetailData: '',
    cardUrl: '',
    cardBackUrl: '',
    restDrawNum: 0,
    qrcode: '',
    winningPage: '',
    title: '',
    drawMode: 'DRAW',
    canDraw: false,
    homePage: [],
    nothingPage: [],
    successPage: [],
    cardList: [],
    tipsPage: [],
    recordId: null,
    isShare: '',
    getUrlRes: '',
    isFirst: '',
    acceptAwardImg: '',
    acceptAwardBtn: '',
    qtnId: '',
    productId: '',
    shareUserType: '',
    activityId: '',
    financeStatus: '',
    auditStatus: '',
    audit: 0,
    changeSalesCode: '',
    changeSaleName: '',
    postersBanner: '',
    currentId: '',
    rejectReason: '',
    receiveType: '',
    acceptId: '',
    shareUserId: '',
    activityTemplateType: '',
    currentCradIndex: 0, // 当前点击的卡牌下标
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad (options) {

    let scene = options.scene

    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      var id = option[0]
      this.setData({
        activityId: id * 1,
        qtnId: '',
      })
    } else {
      this.setData({
        activityId: options.id || options.activityId,
        qtnId: options.qtnId || '',
        productId: options.productId || '', //投票活动 作品id
        drawMode: options.mode || '',
        audit: options.audit || ''
      })
    }

    if (options.isShare) {
      this.setData({
        isShare: true,
        changeSalesCode: options.salesCode,
        changeSaleName: options.saleName,
        shareUserType: options.userType,
        financialManagerShareData: {
          financialManagerNo: options.financialManagerNo,
          financialManager: options.financialManager,
          distributionChannel: options.distributionChannel
        }
      })
    }


    this.setData({
      shareUserId: options.shareUserId || ''
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.init()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload () {
    wx.removeStorageSync('winningPage')
    wx.removeStorageSync('successPage')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom () {

  },

  /**
   * 用户点击右上角分享
   */
  async onShareAppMessage () {
    let that = this
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let salesCode = wx.getStorageSync('salesCode')
    let userType = wx.getStorageSync('userType')
    let saleName = wx.getStorageSync('saleName')
    // 埋点
    let time = new Date().toLocaleString()
    app.sensors.track('activityShare', {
      name: '分享抽奖',
      activityId: that.data.activityId,
      personScan: nickName,
      shareTime: time,
      userId: userId
    })

    const { shareText, title, shareUrl } = this.data.activityDetailData
    var result = shareText.replace('#微信昵称#', nickName);
    var titleResult = result.replace('#活动名称#', title);


    const { distributionChannel, financialManagerData } = this.data

    // 客户是理财经理分享带出
    let HTPATH = ''
    if (userType === 1 && distributionChannel === 'HTZQ') {
      const { financialManager, financialManagerNo } = financialManagerData
      HTPATH = `&financialManager=${financialManager}&financialManagerNo=${financialManagerNo}&distributionChannel=${distributionChannel}`
    }

    return {
      title: titleResult,
      imageUrl: shareUrl,
      path: `/pages/activityTemplate/springOuting/home/<USER>
    }
  },
})