// pages/activityTemplate/golfArea/selectArea/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    pageUrl:"pages/activityTemplate/general/selectArea/index",
    golfList:[],
    id:null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options);
    let golfList = JSON.parse( wx.getStorageSync('golfActivityArea'))
    this.setData({
      golfList,
      // id:options.id||'0',
      lockDate: options.lockDate,
      activityId:options.id,
      activeMode: options.activeMode,
      customerPrice: options.customerPrice,
      salesPrice: options.salesPrice,
      salesPay: options.salesPay,
      isQtn: options.isQtn,
      applyStartTime: options.applyStartTime,
      secKill: options.secKill === 'false' ? false : true,
      title: options.title,
      coverImg: options.coverImg,
      creditsSource:options.creditsSource,
      usableCredits:options.usableCredits,
      

    })
    if(options.id){
      this.findArea(options.id)
    }
  },
  select(e){
    let item = e.currentTarget.dataset.item
    console.log(item);
    this.setData({
      id:item.id,
      venuesInfo:item
    })
  },
  findArea(id){
    let obj = this.data.golfList.find((item)=> item.id == id)
    console.log(obj);
    this.setData({
      venuesInfo:obj
    })
  },
  sure(){
    let pages = getCurrentPages();//当前页面  
    let prevPage = pages[pages.length - 2];//上一页面
    let maxNum = 0; let minNum = 0;
    let userType = wx.getStorageSync('userType')
    if (userType == 3) {//业务员
      maxNum = this.data.venuesInfo.salesMaxNum
      minNum = this.data.venuesInfo.salesMinNum //业务员最小购买数量
    } else {//客户最大购买数量
      maxNum = this.data.venuesInfo.consumerMaxNum
      minNum = this.data.venuesInfo.consumerMinNum
    }
    // prevPage.setData({  //直接给上个页面赋值
    //   society:this.data.venuesInfo.field,
    //   id:this.data.id,
    //   maxNum,
    //   minNum
    // });
  
    // wx.navigateBack({
    //   delta:1
    // })
    let that = this
    let url = `/pages/activityTemplate/general/golfArea/index?id=${that.data.activityId}&activeMode=${that.data.activeMode}&customerPrice=${that.data.customerPrice ||0}&salesPrice=${that.data.salesPrice||0}&salesPay=${that.data.salesPay}&lockDate=${that.data.lockDate}&secKill=${that.data.secKill}&isQtn=${that.data.isQtn}&applyStartTime=${that.data.applyStartTime}&venueId=${that.data.id}&society=${that.data.venuesInfo.field}&maxNum=${maxNum}&minNum=${minNum}&creditsSource=${that.data.creditsSource}&usableCredits=${that.data.usableCredits}`
    if (that.data.secKill) {
      url += `&title=${that.data.title}&coverImg=${that.data.coverImg}`
    }
    wx.navigateTo({
      url,
    })


  },

})