<view class="competeInfo" style="background-image:url({{backgroundImg}})">
  <view class="competeInfo_title">
    <van-icon name="arrow-left" bindtap="goBack" />
    <view class="title_name">作品详情</view>
  </view>
  <view class="competeInfo_info">
    <view class="uerInfo">
      <view class="userInfo_info">
        <view class="userInfo_image">
          <image src="{{voteInfo.ownerAvatar}}"></image>
        </view>
        <view class="userInfo_content">
          <view class="content_name">{{ voteInfo.terminal === 'ADMIN' ? title : voteInfo.ownerName }}</view>
          <view class="content_number">编号：{{productInfo.productId}}</view>
        </view>
      </view>
      <view class="saleInfo" wx:if="{{voteConfig.mobileLimit == 1}}">
        <image src="../../../../image/activity/voteSale.png"></image>
        <view>{{voteInfo.salesName === null ? '' : voteInfo.salesName}}</view>
      </view>
    </view>
    <view class="info_pie">
      <view class="pie_info">
        <view class="info_top">
          <view class="allPie" style="background:{{statisticsConfig.backgroundColor}};">
            <view class="allPie_num" style="color:{{statisticsConfig.numberColor}}">
              {{voteInfo.votes || 0}}
            </view>
            <view class="allPie_name" style="color:{{statisticsConfig.textColor}}">累计票数</view>
            <image src="{{statisticsConfigList[0] ? statisticsConfigList[0] : '../../../../image/activity/vote_detail_1.png'}}"></image>
          </view>
          <view class="rewardPie allPie" style="background:{{statisticsConfig.backgroundColor}};">
            <view class="allPie_num" style="color:{{statisticsConfig.numberColor}}">
              {{voteInfo.rank || 0}}
            </view>
            <view class="allPie_name" style="color:{{statisticsConfig.textColor}}">当前排名</view>
            <image src="{{statisticsConfigList[1] ? statisticsConfigList[1] : '../../../../image/activity/vote_detail_2.png'}}"></image>
          </view>
          <view class="rewardPie allPie" style="background:{{statisticsConfig.backgroundColor}};">
            <view class="allPie_num" style="color:{{statisticsConfig.numberColor}}">
              {{voteInfo.gap || 0}}
            </view>
            <view class="allPie_name" style="color:{{statisticsConfig.textColor}}">距上一名</view>
            <image src="{{statisticsConfigList[2] ? statisticsConfigList[2] : '../../../../image/activity/vote_detail_3.png'}}"></image>
          </view>
        </view>
        <view class="info_bottom" style="background:{{statisticsConfig.backgroundColor}};">
          <view class="bottom_textarea">
            拉票宣言：
            <span>{{voteInfo.cfgInfo}}</span>
          </view>
        </view>
      </view>
      <view class="pie_message">
        <view class="message_top">
          <view class="top_icon"></view>
          <view class="top_title">图片和视频</view>
        </view>
        <view class="message_bottom">
          <swiper wx:if="{{message === 'image'}}" circular="{{true}}" indicator-dots="{{true}}" autoplay="{{true}}" interval="2000" duration="{{1000}}">
            <block wx:for="{{voteInfo.mediaSrc}}" wx:key="*this">
              <swiper-item>
                <image class="image" src="{{item}}" data-url="{{item}}" bindtap="toPreview"></image>
              </swiper-item>
            </block>
          </swiper>
          <video wx:else class="suspen_video" src="{{voteInfo.mediaSrc}}" binderror="videoErrorCallback" autoplay="{{false}}" enable-danmu danmu-btn controls poster="" show-center-play-btn='{{false}}' show-play-btn="{{true}}" picture-in-picture-mode="{{['push', 'pop']}}" bindenterpictureinpicture='bindVideoEnterPictureInPicture' bindleavepictureinpicture='bindVideoLeavePictureInPicture' title=""></video>
        </view>
      </view>
    </view>
  </view>
  <block wx:for="{{componentData}}" wx:key="index">
    <view class="item_bottom" style="{{ item.styleStr}} {{btnDisabled ? 'color: #aaa; border: 2rpx solid #aaa;border-radius:48rpx' : ''}}" wx:if="{{item.type === 'button' && item.btEvent === 'vote'}}">
      <view class="bottom_cancel" bindtap="handleVote">投票</view>
    </view>
    <button wx:if="{{item.type === 'button' && item.btEvent === 'canvass'}}" class="item_bottom" style="{{item.styleStr}}" open-type="share">
      马上拉票
    </button>
  </block>
</view>
<!-- 登录逻辑 -->
<login-box personLogin="{{personLogin}}" bind:closeclick="close" allShow='{{isClose}}'></login-box>
<!-- 登录逻辑 -->
<!-- 参与活动逻辑 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{showModalDlg}}"></view>
<view class="modalDlg" wx:if="{{showModalDlg}}">
  <text>参与活动</text>
  <view>
    <button bindtap="userInfoHandler">点击参与</button>
  </view>
</view>
<!-- 参与活动逻辑 -->
<!-- 确认跳转第三方的弹窗 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{goThirdDialog}}"></view>
<view class='voteSuccess' wx:if="{{goThirdDialog}}">
  <view class="modal_icon" catchtap="closeShowGoThird">
    <van-icon size="44rpx" color="#D0D2DB" name="cross" />
  </view>
  <image class="img" src="../../../../image/voteSuccess.png"></image>
  <view class="go-third-desc">{{activityInfo.voteConfig.receiveType === 1 ? '点击下方按钮参与问卷后完成投票' : '点击下方按钮阅读内容后完成投票'}}</view>
  <view class="go-third-btn" bindtap="goThird">立即前往</view>
</view>
<!-- 确认跳转第三方的弹窗 -->
<!-- 投票成功的弹窗 -->
<view class="mask" catchtouchmove="preventTouchMove" wx:if="{{voteSuccess}}"></view>
<view class='voteSuccess' wx:if="{{voteSuccess}}" style="background-image:url({{backgroundImgVote}})">
  <view class="modal_icon" catchtap="closeVoteSuccessModal">
    <van-icon size="44rpx" color="#D0D2DB" name="cross" />
  </view>
  <view class="title">投票成功</view>
  <image class="img" src="../../../../image/voteSuccess.png"></image>
  <block wx:for="{{componentDataVote}}" wx:key="index">
    <view wx:if="{{item.type === 'text'}}" style="{{item.styleStr}}" class="desc">
      {{item.propValue}}
    </view>
    <view wx:if="{{item.type === 'button' && item.btEvent === 'rule'}}" style="{{item.styleStr}}" class="btn" bindtap="goLottery">
      {{item.propValue}}
    </view>
  </block>
</view>
<!-- 投票成功的弹窗 -->
<!-- 分享页面是否切换业务员 -->
<switch-sale show="{{showSwitchSale}}" shareUserType="{{shareUserType}}" currentId="{{currentId}}" acceptId="{{acceptId}}" salesCode="{{changeSalesCode}}" bind:close="closeChangeTieModal"></switch-sale>
<!-- 分享页面是否切换业务员 -->