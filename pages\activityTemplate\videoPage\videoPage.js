// pages/subpages/videoPage/videoPage.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    src: 'https://tb.yptech.tv/live/login/login?video_id=144163&share_code=40dc48a6-2732-ccf1-5c50-0019e656449e'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options)
    console.log(decodeURIComponent(options.url))
    this.setData({
      src: decodeURIComponent(options.url)
    })

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
})