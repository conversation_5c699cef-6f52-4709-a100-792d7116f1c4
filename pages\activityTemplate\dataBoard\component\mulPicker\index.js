
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show:{
      type: Boolean,
      value: false
    },
    selectedCode: {
      type: Array,
      value:[]
    },
    columns:{
      type: Array,
      value: []
    },
    option: {
      type: Object,
      default: function () {
        return { label: "label", value: "value" };
      },
    },

  },

  /**
   * 组件的初始数据
   */
  data: {
    checkBoxValue:[]

  },
  observers: {
    'selectedCode': function (val) {
      this.setData({
        checkBoxValue:JSON.parse(JSON.stringify(val))
      })
      console.log(this.data.checkBoxValue)
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    toggle(e) {
      const { index } = e.currentTarget.dataset;
      const checkbox = this.selectComponent(`.checkboxes-${index}`);
      checkbox.toggle();
    },
    onClose(){
      this.triggerEvent("closeTopic", {})
    },
    noop() {},
    
    onChange(e){
      console.log(e);
      if(e.detail[e.detail.length-1] === '-1'){
        this.setData({
          checkBoxValue:['-1']
        });
      }else{
        if(e.detail[0] === '-1'){
          let newArray = e.detail
          newArray.splice(0,1)
          this.setData({
            checkBoxValue:newArray
          });
        }else{
          this.setData({
            checkBoxValue: e.detail
          });
        }
      }
      
    },
    onConfirm() {
      this.setData({
        resultValue:this.data.checkBoxValue
      })
      let confirmData = {code:this.data.resultValue,arrayValue:this.getData(this.data.resultValue)}
      this.triggerEvent("confirm", confirmData)
    },
    getData(val) {
      const res = this.data.columns.filter((item) => {
        return val.indexOf(item.enumValue) > -1;
      });
      return res;
    },
  }
})
