
.popup_content{
  padding: 32rpx 32rpx 0;
  box-sizing: border-box;
  /* overflow: hidden; */
  height: 90vh;
}
.activity_banner{
  width: 686rpx;
  height: 368rpx;
  border-radius: 16rpx;
  /* margin-bottom: 48rpx; */
  overflow: hidden;
}
.activity_price{
  position: absolute;
  font-size: 36rpx;
  color: #FF5030;
  font-weight: 500;
}
.activity_price text{
  font-size: 24rpx;
}
.place_box{
  padding-bottom: 24rpx;
  border-bottom: 1px solid #F2F3F5;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
}
.LineShape{
  position: absolute;
}
.activity_place{
  position: absolute;
  font-size: 24rpx;
  color:#7E849F
}
.project_top{
  position: absolute;
  font-family: PingFang SC-Bold;
  font-size: 36rpx;
  color: #17204D;
  margin-bottom: 24rpx;
  font-weight: bold;
}
.project_box{
  width: 686rpx;
  height: 128rpx;
  border-radius:16rpx;
  /* background: #F7F7F7 ; */
  font-size: 28rpx;
  /* color: #17204D ; */
  padding: 24rpx;
  box-sizing: border-box;
  display: flex;
  margin-bottom: 24rpx;
  align-items: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.selectProject{
  /* background: #9ee8f5; */
  /* color: rgb(250, 251, 252) ; */
  border:1px solid #ccc;

}
.scrollView{
  position: absolute;
  /* overflow: hidden; */
  /* height: 462rpx; */
  padding-bottom: 194rpx;
  box-sizing: border-box;
}
.project_img{
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}
.project_title{
  width: 534rpx;
}
.bottomBox {
  position: fixed;
  width: 686rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 48rpx;
  color: #fff;
  background:linear-gradient(to right,#025CEA ,#4492FC);
  bottom: 40rpx;
}