const wxtimer = require('../../../utils/time.js');
const http = require('../../../utils/util.js');
const app = getApp();
const CryptoJS = require('../../../utils/CryptoJs.js');
const API = require('../../../api/request.js')
const { showTab, handleUserStorage } = require('../../../utils/aboutLogin')
const { setSalesCode } = require('../../../utils/querySalesCode')
const { identity,identityEnum } = require('../../../utils/phone-idcard')

let totalTime = 5
let clock = null
Page({
  /**
   * 页面的初始数据
   */
  data: {
    showParamModal:true,
    showReject:false,
    audit:'',
    isAttentionAgainFlag: false,
    disagree: false,
    projectId:0,
    loadAnimation: true,
    playing: true,
    img1: "../../../image/sperkers.png",
    img2: "../../../image/speaker.png",
    subtime: "00:00:00",
    project:[{id:1,url:'../../../image/mineActive.png',total:0,title:'活动计划阿萨德瓦二收到任何'},{id:2,url:'../../../image/mineActive.png',total:12,title:'活动名称'},{id:3,total:10,url:'../../../image/mineActive.png',title:'活动名称'}],
    timeObj: {},
    ids: '', // 活动id
    xsde: false, //用户绑定框
    code: false,
    phoneCode: '',
    codename: '获取验证码',
    returns: '',
    showModalDlg: false,
    idCard: '',
    userType: '',
    personLogin: false,
    productionTalk: false, // true隐藏立即购买按钮
    logining: false, // true登录按钮不可点击
    category: '', // 活动类别5高尔夫
    isFinish: false,//活动是否结束
    InfoShows: false,
    scene: '',
    batchFlag: '1',
    warnInfo: '', // 提醒我/已预约
    reflashToast: false,
    isReflash: false,
    userInfoSuccess: false,
    detailSucess: false,
    chooseArea:false,
    trueName:'',
    idCard: '',
    VSwiperImgList:[],
    currentIndex:1,
    showPoster:false,
    btnText:{
      text:'专属海报',
      cssStr:'width:20rpx;height:96rpx;background:linear-gradient(90deg, #FF5030 0%, #FC6B37 100%);top:276rx;left:710rpx;color:#fff;font-size:20rpx;font-weight:400;'
    },
    showSwitchSale:false,
    currentId: '',
    acceptId:'',
    shareUserType:'',
    identityType: identityEnum,
    identity:'身份证',
    identityIndex:0,
    cardType:'ID_CARD',
    golfActivityArea:[]
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let scene = options.scene
    let that = this
    let userId = wx.getStorageSync('userId')
    let userType = wx.getStorageSync('userType')
    let groupNo = options.groupNo || ''
    let saleName = options.saleName || ''
    let salesCode = options.salesCode || ''
    if (scene) {
      let option = decodeURIComponent(options.scene).split(",")
      let id = option[0]
      that.setData({
        heightTop: app.globalData.height,
        ids: id,
        userType: userType,
        userId,
        scene: scene
      })

    } else {
      let id = options.id
      that.setData({
        heightTop: app.globalData.height,
        ids: id,
        isShare: options.isShare || '',
        shareUserId: options.shareUserId || '',
        audit: options.audit || '',
        userType: userType,
        userId,
        groupNo,
        changeSalesCode: salesCode,
        changeSaleName: saleName,
        shareUserType:options.userType || ''
      })
    }
  },
  onShow: function () {
    if (wx.getStorageSync('token')) {
      wx.showShareMenu({
        withShareTicket: false,
      })
    } else {
      wx.hideShareMenu({})
    }
    let that = this;
    let userId = wx.getStorageSync('userId')
    if (userId !== '') {
      // 判断是否注册过    
      // that.getIsRegister(userId)
      if (wx.getStorageSync('refreshUserInfo')) {
        that.getUserInfo();
      } else {
        if (wx.getStorageSync('salesCode') || wx.getStorageSync('pcode')) {
          const cardType = wx.getStorageSync('cardType')
          const identityValue = that.data.identityType.filter((item) => item.value === cardType )
          console.log(identityValue)
          that.setData({
            type: wx.getStorageSync('userType'),
            salesCode: wx.getStorageSync('salesCode'),
            cardType:wx.getStorageSync('cardType'),
            identity:identityValue[0]?.name || '',
            idCard: wx.getStorageSync('idCard') ? wx.getStorageSync('idCard') : null,
            userType: wx.getStorageSync('userType'),
            credits: wx.getStorageSync('credits'),
            userInfoSuccess: true
          })
        }
        that.getIsRegister()
      }
    } else {
      that.setData({
        showModalDlg: true,
        InfoShow: true
      })
    }
  },
  // 查询是否注册
  getIsRegister() {
    let that = this
    if (http.unRegister()) {
      // 已经授权没有注册
      // 查询绑定的业务员
      if (that.data.isShare) {
      that.data.changeSalesCode && setSalesCode(that.data.changeSalesCode,that.data.changeSaleName)
      }
      that.setData({
        personLogin: true,
        showModalDlg: false,
        InfoShow: false
      })
      if (!that.data.activity) {
        that.getactivity(that.data.ids)
      }
    } else {
      showTab()
      that.setData({
        showModalDlg: false,
        InfoShow: false
      })
      if(that.data.isShare == 1 ||that.data.isShare == 2){
        that.openChangeScole()
      }
      if (!that.data.activity) {
        that.getactivity(that.data.ids)
      }
    }
  },
  onHide () {
    let that = this
    let nickName = wx.getStorageSync('nickName')
    let userId = wx.getStorageSync('userId')
    let activityName = that.data.activity.title
    let activityId = that.data.activity.id
    let time = new Date().toLocaleString()
    let hour = (new Date().getTime() - that.data.enterTime) / 1000
    app.sensors.track('activityDetailStayTime', {
      name: '活动详情页停留时长',
      personScan: nickName,
      activityName: activityName,
      activityId: activityId,
      leaveTime: time,
      userId: userId,
      stayTime: hour + 's'

    });

  },
  // 刷新获取活动数据
  reGetActivity () {
    let that = this
    this.handleReflashToast()
    if (!that.data.userInfoSuccess) {
      this.getUserInfo()
    }
    if (!wx.getStorageSync('key')) {
      CryptoJS.getKey()
    }
    if (that.data.userInfoSuccess && !that.data.detailSucess && !that.data.activity) {
      console.log('加载活动');
      this.getactivity(this.data.ids)
    }
  },
  handleReflashToast () {
    let that = this
    that.setData({
      reflashToast: true,
      reflashBtnContent: `(${totalTime}s)`,
      isReflash: false
    })
    clearInterval(clock);
    clock = setInterval(() => {
      totalTime--;
      that.setData({
        reflashBtnContent: `(${totalTime}s)`
      })
      if (totalTime < 1) {
        clearInterval(clock);
        totalTime = 5
        that.setData({
          reflashBtnContent: '',
          isReflash: true
        })
      }
    }, 1000);
  },
  // 我想参加
  wantToJoin () {
    if(this.data.audit) return
    // console.log('我想参加')
    let that = this
    let data = {
      activityId: Number(that.data.ids)
    }
    API.wantAttention(data).then(res => {
      // console.log('async我想参加', res)
      if (res.data.code == 200) {
        that.setData({
          isAttentionAgainFlag: true
        })
      } else {
        wx.showToast({
          title: res.data.message,
          icon: 'none'
        })
      }
    })
  },
  //活动详情
  async getactivity (id) {
    const that = this
    that.setData({
      myLoadAnimation: true,
    })
    let res = null
    if(that.data.audit == 1){
      res = await API.auditDetail({id})
    }else {
      res = await API.getActivityDetail({id})
    }
    console.log('详情数据：', res)
    if (res.header['Content-Type'] === 'text/html' && !that.data.reflashToast) {
      that.setData({
        myLoadAnimation: false,
        detailSucess: false
      })
      this.handleReflashToast()
    } else {
      if (res.data.code == 200) {
        that.setData({
          detailSucess: true,
          myLoadAnimation: false,
          isAttentionAgainFlag: res.data.data?.isAttention == 1 ? true : false
        })
        if (that.data.detailSucess && that.data.userInfoSuccess && wx.getStorageSync('key')) {
          that.setData({
            reflashToast: false
          })
        }
        // 埋点
        const nickName = wx.getStorageSync('nickName')
        const userId = wx.getStorageSync('userId')
        const activityName = res.data.data?.title || ''
        const activityId = res.data.data?.id || ''
        const time = new Date().toLocaleString()
        app.sensors.track('activityDetail', {
          name: '活动详情页',
          personScan: nickName,
          activityName,
          activityId,
          scanTime: time,
          userId
        });
        const create_time = res.data.data.startTime // 活动开始时间
        const applyStartTime = res.data.data.applyStartTime // 活动买票开始时间
        const end_time = res.data.data.endTime
        const applyEndTime = res.data.data.applyEndTime
        if (res.data.data.detailImgs) {
          res.data.data.detailImgs = res.data.data.detailImgs.split(",")
        }
        // 免费活动的时候显示freeLimit字段，默认是限时免费
        if (!res.data.data.freeLimit) {
          res.data.data.freeLimit = "限时免费"
        }
        // totalplace: 活动总票数
        // place: 活动票数剩余库存
        // shiwutype: 活动类型 0:虚拟 1:实物活动(有收货地址)
        if (res.data.data.category == 3) {
          that.setData({
            minNum: that.data.userType == 3 ? res.data.data.salesMinNum : res.data.data.minNum,
            maxNum: that.data.userType == 3 ? res.data.data.salesMaxNum : res.data.data.maxNum,
          })
        }
        that.setData({
          enterTime: new Date().getTime(),
          lockDate: res.data.data.lockDate,
          activity: res.data.data,
          place: res.data.data.limitBuy,
          applyEndTime: res.data.data.applyEndTime,
          applyStartTime: res.data.data.applyStartTime,
          create_time: create_time,
          end_time: end_time,
          prdsen: parseInt((1 - res.data.data.place / res.data.data.totalplace) * 100),
          shiwutype: res.data.data.type,
          isProductionTalk: res.data.data.isProductionTalk, // 是否是产说会
          category: res.data.data.category,
          videoUrl: res.data.data.video,
          batchFlag: res.data.data.batchFlag,
          loadAnimation: false,
          warnInfo: res.data.data.subscribed ? '已预约' : '提醒我',
          chatLst: res.data.data.chatLst,  //活动可以参与的业务员渠道
          myLoadAnimation: false,
          isReflash: false,
        })
       
        // 判断高尔夫
        if (res.data.data.category == 5) {
          if (res.data.data.batchFlag == '1') { //批次预约
            res.data.data.golfList.map(element => {
              element.selFlag = false
              element.appointTimes = JSON.parse(element.appointTimes)
            })
          } else if (res.data.data.batchFlag == '2') {//机构预约
            res.data.data.golfList.map(element => {
              element.selFlag = false
            })
          } else {
            that.setData({
              golfActivityArea: res.data.data.golfList,
            })
          }
          that.setData({
            activeMode: res.data.data.activityMode,
            golfActivityArea: res.data.data.golfList
          })
          
        }
        // 判断产说会 isProductionTalk : 1 是产说会,0不是
        if (res.data.data.isProductionTalk == 1 && res.data.data.category == 1 && (that.data.userType == 1 || that.data.userType == 2)) { // 普通产说会
          that.setData({
            productionTalk: true,//隐藏立即购买按钮
          })
        } else if (res.data.data.isProductionTalk == 1 && (res.data.data.category == 4 || res.data.data.category == 3) && (that.data.userType == 1 || that.data.userType == 2)) { // 1+1产说会
          that.setData({
            productionTalk: true,
          })
        } else {
          that.setData({
            productionTalk: false,
          })
        }
        // 处理页面元素
        this.handlePageStyle(res.data.data)
        let nowtime = new Date().getTime() //目前时间
        let createTime = new Date(applyStartTime.replace(/-/g, '/')).getTime()  // 活动开始时间时间戳
        let endTime = new Date(applyEndTime.replace(/-/g, '/')).getTime() // 活动结束时间时间戳毫秒
        // console.log(nowtime, createTime, endTime, 999)
        let sub_time1 = parseInt((nowtime - createTime) / 1000) //正数： 活动开始  负数：活动开始倒计时
        let sub_time2 = parseInt((endTime - nowtime) / 1000)   // 正数 结束倒计时   负数： 活动已结束
        that.setData({
          sub_time1,
          sub_time2
        })
        var subtime = ""
        if (sub_time1 < 0) {
          subtime = parseInt((createTime - nowtime) / 1000)  //活动开始倒计时
          that.setData({
            countDownConfig: {
              ...that.data.countDownConfig,
              textStart:that.data.countDownConfig?.endLeftText || '',
              textEnd:that.data.countDownConfig?.endRightText || ''
            }
          })
        }
        if (sub_time2 < 0 && !that.data.audit) {
          that.setData({
            isFinish: true,
          })
          wx.showToast({
            title: "活动已结束,请下次参与！",
            icon: "none",
            duration: 2500,
          })
        }
        if (sub_time1 > 0 && sub_time2 > 0) {
          subtime = parseInt((endTime - nowtime) / 1000) //进行中  结束倒计时
          that.setData({
            countDownConfig: {
              ...that.data.countDownConfig,
              textStart:that.data.countDownConfig?.startLeftText || '',
              textEnd:that.data.countDownConfig?.startRightText || ''
            }
          })
        }
        let timer = new wxtimer({
          complete: function () {
            if (sub_time1 < 0) { // 活动开始倒计时
              that.getactivity(that.data.ids)
              clearInterval(timer)
            } else if (sub_time1 > 0 && sub_time2 > 0 && !that.data.audit) {
              that.setData({
                isFinish: true,
              })
              wx.showModal({
                title: '提示',
                content: '活动已结束',
                showCancel: false,
                success: function (res) {
                  if (res.confirm) {
                    wx.navigateBack({
                      delta: 1
                    })
                  }
                }
              })
            }
          }
        });
        //  计时器
        timer.start(that, subtime, false);
      } else {
        that.setData({
          myLoadAnimation: false,
        })
        if (res.data.code === 500 && res.data.message === '活动火爆，小主请稍后再试' && !that.data.reflashToast) {
          that.setData({
            detailSucess: false
          })
          that.handleReflashToast()
        } else {
          wx.showToast({
            title: "网络繁忙，请稍后重试",
            icon: "none",
            duration: 2000,
          })
        }
      }
    }
      // }).catch(err => {
      //   console.log('catch', err)
      //   that.setData({
      //     myLoadAnimation: false
      //   })
      //   if (!that.data.reflashToast) {
      //     that.handleReflashToast()
      //   }
      // })
  },
  // 处理页面元素
  handlePageStyle(res){
    // console.log(res.activityPageConfig)
    const that = this
    const activityPageConfig = res.activityPageConfig ? JSON.parse(res.activityPageConfig).find((item) => item.title === '商品详情页') : null
    if(activityPageConfig !== undefined && activityPageConfig !== null){
      // console.log('页面组件元素',activityPageConfig.componentData)
      let backgroundImg = {}
      if(this.data.category == 5){
         backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'backgroundImg')
      }else{
         backgroundImg = activityPageConfig.componentData.find((item) => item.type === 'pageBackgroundImg')

      }
      const countDownConfig = activityPageConfig.componentData.find((item) => item.type === 'countDown')
      const registrationPrice = activityPageConfig.componentData.find((item) => item.type === 'registrationPrice')
      const limitTextConfig = activityPageConfig.componentData.find((item) => item.type === 'limitText')
      that.setData({
        backgroundImg: backgroundImg?.propValue.url || '',
        componentData:activityPageConfig.componentData,
        countDownConfig,
        registrationPrice,
        limitTextConfig
      })
    }
    const activityPosterConfig = this.data.activity.activityPageConfig ? JSON.parse(this.data.activity.activityPageConfig).find((item) => item.title === '专属海报') : null
    if(activityPosterConfig !== undefined && activityPosterConfig !== null){
      const posterBackgroundImg = activityPosterConfig.componentData.find((item) => item.type === 'posterPic')
      const posterText = activityPosterConfig.componentData.find((item) => item.type === 'text')
      that.setData({
        posterBackgroundImg,
        activityPosterConfig,
        posterText
      })
    }
    const activityPeriodConfig = this.data.activity.activityPageConfig ? JSON.parse(this.data.activity.activityPageConfig).find((item) => item.title === '预约场次配置') : null
    if(activityPeriodConfig !== undefined && activityPeriodConfig !== null){
     wx.setStorageSync('activityPeriodConfig', JSON.stringify(activityPeriodConfig))
    }
  }, 
  //用户登录验证
  userInfoHandler: function (e) {
    var that = this;
    if (e.detail.errMsg == "getUserInfo:fail auth deny") { } else {
      wx.removeStorageSync('defaultPerson');
      wx.removeStorageSync('refreshUserInfo')
      app.getUserInfo(function (userInfo) {
        console.log(userInfo)
        wx.setStorageSync('headImgUrl', userInfo.avatarUrl);
        wx.setStorageSync('nickName', userInfo.nikename);
        wx.setStorageSync('openId', userInfo.openId);
        wx.setStorageSync('userId', userInfo.id);
        // that.getisplay(that.data.ids)
        that.setData({
          showModalDlg: false,
          InfoShow: false,
          isAuthorize: true
        })
        wx.showToast({
          title: '授权成功',
          icon: "none",
          duration: 1500,
        })
        if (userInfo) {
          that.getactivity(that.data.ids)
          var timer3 = setInterval(() => {
            if (http.userAre() != '') {
              that.getUserInfo();
              clearInterval(timer3);
            }
          }, 100)
        }
      })
    }
  },
  // 获取用户信息
  getUserInfo () {
    const that = this
    const token = wx.getStorageSync("token")
    // 获取用户所属门店信息
    if (token) {
      this.setData({
        myLoadAnimation: true,
      })
      API.getInfoById()
        .then(res => {
          if (res.header['Content-Type'] === 'text/html' && !that.data.reflashToast) {
            that.setData({
              myLoadAnimation: false,
            })
            this.handleReflashToast()
          } else {
            if (res.data.code == 200) {
              console.log('获取用户信息', res);
              const {cardType} = res.data.data
              const identityValue = that.data.identityType.filter((item) => item.value === cardType )
              console.log(identityValue)
              that.setData({
                type: res.data.data.type,
                salesCode: res.data.data.salesCode,
                idCard: CryptoJS.Decrypt(res.data.data.idcard),
                userType: res.data.data.type,
                credits: res.data.data.credits,
                userInfoSuccess: true,
                myLoadAnimation: false,
                cardType:cardType,
                identity:identityValue[0]?.name || '',
              })

              if (that.data.detailSucess && that.data.userInfoSuccess && wx.getStorageSync('key')) {
                that.setData({
                  reflashToast: false
                })
              }
              console.log(CryptoJS.Decrypt(res.data.data.salesCode));
              handleUserStorage(res.data.data)
              console.log('用户信息业务员账号', wx.getStorageSync('salesCode'));
              if (wx.getStorageSync('userId') !== '') {
                // 判断是否注册过
                that.getIsRegister(wx.getStorageSync('userId'))
              }
            } else {
              that.setData({
                myLoadAnimation: false,
              })
              if (res.data.code === 500 && res.data.message === '活动火爆，小主请稍后再试' && !that.data.reflashToast) {
                this.handleReflashToast()
              } else {
                wx.showToast({
                  title: res.data.message,
                  icon: 'none',
                  duration: 2000
                })
              }
            }
          }
        })
    }
    if (wx.getStorageSync("token")) {
      that.setData({
        phonebtn: true,
      })
    } else {
      that.setData({
        phonebtn: false,
        tobind: false
      })
    }
  },
  // 立即购买
  async tobuy () {
    const that = this
    const { ids,category,shiwutype,activity} = that.data
    console.log(activity)
    const isRequire = activity.attributes.filter((item) => item.propertyCode === 'cardNo')[0]?.required
    // const selBatchId = (that.data.batchFlag == '1' || that.data.batchFlag == 2) ? that.data.selBatchId : ''
    if (wx.getStorageSync('token') && that.data.userType == 0) {// && !that.data.isShare不管是否分享都要判断是否注册
      if (wx.getStorageSync('userId') !== '') {
        // 判断是否注册过
        that.getIsRegister(wx.getStorageSync('userId'))
      }
      return false;
    } else if (that.data.activity.place === 0) {
      wx.showToast({
        title: '前库存已空，无法下单~',
        icon: 'none',
        duration: 1500,
        mask: false,
      });
      return
    } else if (that.data.activity.secKill && that.data.chatLst.findIndex(item => ['root', wx.getStorageSync('chatType')].includes(item)) === -1) {
      wx.showToast({
        title: '您无法参与此次活动,您的客户经理渠道不在该活动的允许范围内',
        icon: 'none',
        duration: 2500,
        mask: false,
      });
      return false
      // }
    } else if (that.data.userType != 3 && ((that.data.category == 5 && that.data.activeMode === 'PLUS') || that.data.category == 4) && that.data.activity.salesPay == 1) {
      wx.showToast({
        title: '请联系您的客户经理进行活动报名',
        icon: 'none',
        duration: 1500,
      });
      return false
    } else if (that.data.sub_time1 < 0) {
      wx.showToast({
        title: "活动尚未开始，请耐心等待！",
        icon: "none",
        duration: 2500,
      })
      return false;
    } else if (that.data.sub_time2 < 0) {
      that.setData({
        isFinish: true
      })
      wx.showToast({
        title: "活动已结束,请下次参与！",
        icon: "none",
        duration: 2500,
      })
    } else if (!that.data.activity.secKill && wx.getStorageSync('token') && (wx.getStorageSync('idCard') == null || wx.getStorageSync('idCard') == '') && isRequire === 1) {
      
      that.setData({
        chooseArea: !that.data.chooseArea,
        trueName: wx.getStorageSync('trueName')
      })
    } else if (that.data.userType == 3) {
      that.checkYwyLabel()
      return false;
    } else {
      //校验是否同意隐私项
      if (http.userAre() && wx.getStorageSync('agree') == '0') {//已登录没有同意隐私项
        that.setData({
          disagree: true,
          jumpType: 'tobuy'
        })
        return
      } else {
        const nickName = wx.getStorageSync('nickName')
        // 埋点
        const activityName = that.data.activity.title
        const activityId = that.data.activity.id
        const activity = that.data.activity
        const userId = wx.getStorageSync('userId')
        const time = new Date().toLocaleString()
        app.sensors.track('activityBuy', {
          name: '立即购买',
          activityId,
          activityName,
          personScan: nickName,
          time: time,
          userId
        });
        const url = `/pages/activityready/activityready?cate=${that.data.category}&id=${ids}&shiwutype=${shiwutype}&batchId=${selBatchId}&activeMode=${that.data.activeMode || ''}&groupNo=${that.data.groupNo}&minNum=${activity.minNum || 0}&maxNum=${activity.maxNum}&salesMinNum=${activity.salesMinNum || 0}&salesMaxNum=${activity.salesMaxNum || 0}&customerPrice=${activity.price || 0}&salesPrice=${activity.salesPrice || 0}&salesPay=${activity.salesPay || 0}&lockDate=${activity.lockDate}&secKill=${activity.secKill}&isQtn=${that.data.activity.isQtn}&creditsSource=${activity.creditsSource}&usableCredits=${activity.usableCredits}`
        if (activity.secKill) {
          url += '&title=' + activity.title + '&coverImg=' + activity.coverImg
        }
        wx.navigateTo({
          url,
        })

      }
    }
  },
  agreePrivacy() {//同意协议
    let that = this
    that.toActivityReady()
  },
  // 跳转报名页的判断
  toActivityReady() {
    let that = this
    if (that.data.userType != 3 && that.data.activeMode === 'PLUS' && that.data.salesPay == 1) {
      wx.showToast({
        title: '请联系您的客户经理进行活动报名',
        icon: 'none',
        duration: 1500,
      });
      return false
    }
      wx.setStorageSync('currentIndex', that.data.currentIndex)
      wx.setStorageSync('currentperiodList', that.data.currentperiodList)
      console.log('秒杀', that.data.secKill);
      let url = `/pages/activityready/activityready?golfId=${that.data.areaInfo.id}&id=${that.data.activityId}&maxNum=${that.data.maxNum}&minNum=${that.data.minNum}&salesMaxNum=${that.data.salesMaxNum}&salesMinNum=${that.data.salesMinNum}&activeMode=${that.data.activeMode}&customerPrice=${that.data.customerPrice}&salesPrice=${that.data.salesPrice||0}&salesPay=${that.data.salesPay}&cate=5&lockDate=${that.data.lockDate}&secKill=${that.data.secKill}&isQtn=${that.data.isQtn}&creditsSource=${that.data.activity.creditsSource}&usableCredits=${that.data.activity.usableCredits}`
      if (that.data.secKill) {
        url += `&title=${that.activity.title}&coverImg=${that.activity.coverImg}`
      }
      wx.navigateTo({
        url,
        success: function (res) { }
      })
   
  },
  // 业务员标签校验
  checkYwyLabel () {
    const that = this
    const data = {
      activityId: that.data.ids
    }
    API.checkYwyLabel(data)
      .then(res => {
        if (res.data.code == 200) {
          if (!res.data.data) {
            wx.showToast({
              title: '当前为限定业务员标签活动，您无法参与！',
              icon: 'none',
              duration: 2000,
            });
          } else {
            //校验是否同意隐私项
            if (http.userAre() && wx.getStorageSync('agree') == '0') {//已登录没有同意隐私项
              that.setData({
                disagree: true,
                jumpType: 'tobuy'
              })
              return
            } else {
              const nickName = wx.getStorageSync('nickName')
              const userId = wx.getStorageSync('userId')
              // 埋点
              const activityName = that.data.activity.title
              const activityId = that.data.activity.id
              const activity = that.data.activity
              const time = new Date().toLocaleString()
              app.sensors.track('activityBuy', {
                name: '立即购买',
                activityId,
                activityName,
                personScan: nickName,
                time,
                userId
              });
              console.log('标签校验');
              let url = '/pages/activityready/activityready?cate=' + that.data.category + '&id=' + that.data.ids + '&shiwutype=' + that.data.shiwutype + '&activeMode=' + that.data.activeMode + '&minNum=' + activity.minNum + '&maxNum=' + activity.maxNum + '&salesMinNum=' + activity.salesMinNum + '&salesMaxNum=' + activity.salesMaxNum + '&customerPrice=' + activity.price + '&salesPrice=' + activity.salesPrice + '&salesPay=' + activity.salesPay + '&lockDate=' + activity.lockDate + '&secKill=' + activity.secKill + '&isQtn=' + that.data.activity.isQtn+'&creditsSource='+that.data.activity.creditsSource +'&usableCredits='+that.data.activity.usableCredits
              if (activity.secKill) {
                url += '&title=' + activity.title + '&coverImg=' + activity.coverImg
              }
              wx.navigateTo({
                url,
              })
            }

          }
        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none',
            duration: 1500,
          });

        }
      })
  },
  // 分享活动
  onShareAppMessage () {
    if(this.data.audit) return
    const that = this
    const nickName = wx.getStorageSync('nickName')
    const userId = wx.getStorageSync('userId')
    // 埋点
    const activityName = that.data.activity.title
    const activityId = that.data.activity.id
    const time = new Date().toLocaleString()
    const salesCode = wx.getStorageSync('salesCode')
    const saleName = wx.getStorageSync('saleName')
    const userType = wx.getStorageSync('userType')
    app.sensors.track('activityShare', {
      name: '分享活动',
      activityId,
      activityName,
      personScan: nickName,
      shareTime: time,
      userId
    });
    // pages/activitydetail/activitydetail?id=285&isShare=1&shareUserId=36
    const title = that.data.activity.shareText ? that.data.activity.shareText.replace('#微信昵称#',wx.getStorageSync('nickName')).replace('#活动名称#',this.data.activity.title) : ''
    return {
      title: title,
      path: `/pages/activityTemplate/general/index?id=${that.data.ids}&isShare=1&shareUserId=${wx.getStorageSync('userId')}&clientShareUserId=${userId}&salesCode=${salesCode}&saleName=${saleName}&userType=${userType}`
    }
  },
  preventTouchMove: function () {
    //阻止触摸
  },
  // 选择批次或者机构预约
  selectBatch(e) {
    console.log(e)
    if(this.data.audit) return
    let batch = e.currentTarget.dataset.item
    let id = e.currentTarget.dataset.id
    let index = e.currentTarget.dataset.selindex
    console.log(batch,index)
    if (this.data.batchFlag == '2' && !batch.buyable) {
      return
    }
    if (!batch.selflag) {
      // 当前列表其他恢复
      this.data.golfActivityArea.forEach((ele, elindex) => {
        console.log('选中的批次', elindex)
        let curSelFlag = `golfActivityArea[${elindex}].selFlag`
        this.setData({
          [curSelFlag]: batch.selFlag,
        })
      })
      console.log(this.data.golfActivityArea)
    }
    let selIndex = `golfActivityArea[${index}].selFlag`
    //  选中与不选中
    this.setData({
      [selIndex]: !e.currentTarget.dataset.selflag,
      selBatchId: id,
      selBatch: batch
    })
    console.log('选中的批次号', this.data.selBatchId)
  },
  // 去高尔夫活动详情
  jumpToGolf(e) {
    let that = this
    const applyStartTime = new Date(this.data.applyStartTime.replace(/-/g, '/')).getTime()
    const nowtime = new Date().getTime()
    if (nowtime < applyStartTime) {
      return wx.showToast({
        title: '活动尚未开始,请耐心等待!',
        icon: 'none'
      })
    }
    //活动模式 PLUS:1+1模式； PRODUCT:商品模式
    if (wx.getStorageSync('token') && that.data.userType == 0) {// && !that.data.isShare不管是否分享都要判断是否注册
      if (wx.getStorageSync('userId') !== '') {
        // 判断是否注册过
        that.getIsRegister(wx.getStorageSync('userId'))
      }
    }else {
      let idCard = wx.getStorageSync('idCard');
      if (wx.getStorageSync('token') && (idCard == null || idCard == '')) {
        return wx.showModal({
          title: '提示',
          content: '请完善个人信息',
          confirmText: '立即前往',
          success(res) {
            if (res.confirm) {
              wx.navigateTo({
                url: `/pages/userinfo/userinfo?isActivity=2`,
                success: function (res) { }
              })
            } else if (res.cancel) {
              return false
            }
          }
        })
      }
    wx.setStorageSync('applyStartTime', that.data.applyStartTime)
    wx.setStorageSync('applyEndTime', that.data.applyEndTime)
    wx.setStorageSync('golfActivityArea', JSON.stringify(this.data.golfActivityArea))
    wx.setStorageSync('activityId', that.data.ids)
    wx.setStorageSync('sub_time1', that.data.sub_time1)
    wx.setStorageSync('sub_time2', that.data.sub_time2)
    console.log(that.data.activity);
    let url = `/pages/activityTemplate/general/selectArea/index?id=${that.data.activity.id}&activeMode=${that.data.activeMode}&customerPrice=${that.data.activity.price ||0}&salesPrice=${that.data.activity.salesPrice||0}&salesPay=${that.data.activity.salesPay}&lockDate=${that.data.lockDate}&secKill=${that.data.activity.secKill}&isQtn=${that.data.activity.isQtn}&applyStartTime=${applyStartTime}&creditsSource=${that.data.activity.creditsSource}&usableCredits=${that.data.activity.usableCredits}`
    if (that.data.activity.secKill) {
      url += `&title=${that.activity.title}&coverImg=${that.activity.coverImg}`
    }
    wx.navigateTo({
      url,
    })

    }
    
  },
  // 提醒我
  handleWarnMe () {
    let { warnInfo, activity: { id } } = this.data
    if (warnInfo === '已预约') {
      API.cardCollectNewWarn({ activityId: id, status: 0 }).then(res => {
        if (res.data.code === 200) {
          this.setData({
            warnInfo: '提醒我'
          })
        }
      })
      return
    }
    let message = 'Y7lBG9cAPzsB1QzpK9MASEp8Xv2YFH4YnkCmILWTlCA'
    wx.getSetting({
      withSubscriptions: true,//是否同时获取用户订阅消息的订阅状态，默认不获取
      success: (res) => {
        if (res.subscriptionsSetting.mainSwitch) { //用户是否打开了接收消息的总开关
          if (res.subscriptionsSetting.itemSettings != null && res.subscriptionsSetting.itemSettings[message]) { // 用户同意总是保持是否推送消息的选择, 这里表示以后不会再拉起推送消息的授权
            const status = res.subscriptionsSetting.itemSettings[message]
            if (status == 'accept') { // accept：接收，reject：拒绝，ban：已被后台禁止
              wx.requestSubscribeMessage({
                tmplIds: [message],
                success: (item) => {
                  if (item[message] == 'accept') {
                    API.cardCollectNewWarn({ activityId: id, status: 1 }).then(res => {
                      if (res.data.code === 200) {
                        this.setData({
                          warnInfo: '已预约'
                        })
                      }
                    })
                  }
                },
                fail: (res) => { console.log('2', res) },
              })
            } else {
              wx.openSetting({
                withSubscriptions: true,
                success: (rej) => {
                  if (rej.subscriptionsSetting.itemSettings != null && rej.subscriptionsSetting.itemSettings[message] == 'accept') {
                    wx.showToast({
                      title: '权限修改成功，请点击提醒我',
                      icon: 'none',
                      duration: 3000
                    })
                  }
                }
              })
            }
          } else {
            wx.requestSubscribeMessage({
              tmplIds: [message],
              success: (res) => {
                if (res[message] == 'accept') {
                  API.cardCollectNewWarn({ activityId: id, status: 1 }).then(res => {
                    if (res.data.code === 200) {
                      this.setData({
                        warnInfo: '已预约'
                      })
                    }
                  })
                }
              },
              fail: (res) => { console.log('2', res) },
            })
          }
        }
      }
    })
  },
  // 缺少身份证号展示弹框
  closeChooseAreaModal(){
    this.setData({
      chooseArea: !this.data.chooseArea
    })
  },
  //身份证号输入
  idCardValue (e) {
    const value = e.detail.value
    this.setData({ idCard: value })
  },
  // 身份证号输入后确认逻辑
  confrimChooseArea() {
    const idCard = this.data.idCard
    const cardType = this.data.cardType
    if ( idCard == '' || idCard === null) {
      wx.showToast({
        title: "证件号输入格式有误",
        icon: 'none',
        duration: 2000
      })
      return false;
    }
    this.updateUserInfo()
  },
  // 更新用户信息
  async updateUserInfo() {
    const that = this
    const params = {
      idcard:CryptoJS.Encrypt(that.data.idCard),
      cardType: that.data.cardType
    }
    const res = await API.updateUserInfo(params)
    if(res.data.code === 200){
      wx.showToast({
        title: "保存成功",
        icon: 'none',
        duration: 2000
      })
      wx.setStorageSync('idCard',that.data.idCard);
      wx.setStorageSync('cardType', that.data.cardType);
      that.closeChooseAreaModal()
    } else {
      wx.showToast({
        title: res.data.message,
        icon: 'none',
        duration: 2000
      })
    }
    console.log(res)
  },
  // 关闭专属海报弹框
  onClickHide(){
    this.setData({
      showPoster: false
    })
  },
  // 下载海报
  downPoster(){
    this.setData({
      showPoster: true
    })
  },
  close (e) {
    wx.showToast({
      title: '请选择身份登录',
      duration: 2000,
      icon: "none",
    })
    this.setData({
      personLogin:true
    })
  },
  goMyorder(){
    wx.navigateTo({
      url: `/pages/myactivity/myactivity`,
    })
  },
  reject(){
    this.setData({
      showReject:true,
      rejectReason:''
    })
  },
  selectProject(e){
    console.log(e.currentTarget.dataset.item);
    let {id,total} = e.currentTarget.dataset.item
    if(!total) return  wx.showToast({
      title: '该项目暂无库存！',
      duration: 2000,
      icon: "none",
    })
    this.setData({
      projectId:id
    })

  },
  triggerEvent(e){
    console.log(e);
    wx.navigateBack({
      delta:1
    })
  },
  nextStep(){
    wx.navigateTo({
      url:'/pages/activityAudit/activityDetail/index'
    })
  },
  confirmChangeTieModal() {
    let avatarUrl = wx.getStorageSync('headImgUrl')
    let phone = wx.getStorageSync('phonebtn')//加密1
    let gender = wx.getStorageSync('gender')//1
    let name = wx.getStorageSync('trueName');//加密1
    let nikename = wx.getStorageSync('nickName')//1
    let idCard = wx.getStorageSync('idCard') //加密1,
    let salesCode = this.data.changeSalesCode//加密

    if (idCard == '' || idCard == null) {
      // 身份证为空时保存
      var data = {
        name: CryptoJS.Encrypt(name),
        phone: CryptoJS.Encrypt(phone),
        gender: gender,
        nikename: nikename,
        avatarUrl: avatarUrl,
        serialCode: CryptoJS.Encrypt(salesCode),
        idCard: '',
      }
    } else {
      var data = {
        name: CryptoJS.Encrypt(name),
        phone: CryptoJS.Encrypt(phone),
        gender: gender,
        nikename: nikename,
        avatarUrl: avatarUrl,
        serialCode: CryptoJS.Encrypt(salesCode),
        idCard: CryptoJS.Encrypt(idCard),

      }
    }
    API.updateBinding(data).then(res => {
      if (res.data.code == 200) {
        this.setData({
          showSwitchSale: !this.data.showSwitchSale,
        })
      }
    })
  },
  closeChangeTieModal(){
    this.setData({
      showSwitchSale: false
    })
  },
  openChangeScole() {
    let salesCode = wx.getStorageSync('salesCode')//加密
    let saleName = wx.getStorageSync('saleName')
    console.log('--✨🍎',)
    if (salesCode != this.data.changeSalesCode || saleName != this.data.changeSaleName) {
      this.setData({
        showSwitchSale: true,
        currentId: saleName + salesCode,
        acceptId: this.data.changeSaleName + this.data.changeSalesCode
      })
    }
  },
  // 更改证件类型
  identityChange (e) {
    const that = this
    const identityType = that.data.identityType
    console.log('picker发送选择改变，携带值为', e.detail.value);//index为数组点击确定后选择的item索引
    this.setData({
      identityIndex: e.detail.value,
      identity: identityType[e.detail.value].name,
      cardType:identityType[e.detail.value].value
    })
  },
})